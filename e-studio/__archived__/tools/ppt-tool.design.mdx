# PPT Generation Toolset for Teacher

## Core Problem to Solve

Help @Vicky to generate PPTs slides to use in the class.

### Core Features

* One prompt to generate the whole PPT with rich-resources in teaching class

### Core Scenarios

* Process and generate segments of PPTs according to the class content, provided by @Vicky

### Core Structure

* integrated in `ElaborationStudio` as a sub-system as `tools.e-studio.ai/ppt`
* use prompt and then use `ai-sdk` to generate the dialog system from the scratch and
* the llm calling price is shared via `tokens` from `ElaborationStudio` and ends with e-commerce system to pay for the usage

## Tech Details

* work as a new app in `Vercel`
* share the domain from `ElaborationStudio` as `tools.e-studio.ai/ppt`, others tools can take the same domain such as `tools.e-studio.ai/horizon`

### Ideas

* use web-ppt architecture to generate the PPT
* use MCP technology to do images / resources fetching
* use Google Image Search to get the related images
* use Artifacts similar technology to generate the cool animated effects in web-page

### Agentic System

* let's try to one-agent to rule them all method
* if one agent is not possible, try to use multi-agents to rule them all method, use a2a protocol to make them work together

## Exp. Feedback

> Experience BP

* impress.js + style-guide prompts + images + deep-research
* radix-ui powered + math-renderer + code-block + mermaid
* combo special effects few shots -> UI b.p.

## Exp. Feedback

> Experience BP

* impress.js + style-guide prompts + images + deep-research
* radix-ui powered + math-renderer + code-block + mermaid
* combo special effects few shots -> UI b.p.
