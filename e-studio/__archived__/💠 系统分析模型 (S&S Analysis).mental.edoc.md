---
type: doc
uuid: f1670110-8625-4243-8a76-7b4695b2e104
name: 💠 系统分析模型 (S&S Analysis).mental
---

# 💠 系统分析模型 (S&S Analysis).mental

> 系统分析最重要的是学会从「变化」的视角看问题。一定要明白：「系统在不断演化」，当下的目标决定当下的系统的一切结构、功能以及背后的基因。
> 能够「数学化」和「抽象化」研究，形成模型，模型构筑系统。适用：对能够抽象的事件、实体的系统性分析，以理解本质，做预测，解决问题。

从形式化的角度上来说，我们可以用：公理、定理（定律）等形式化的方式来描述结构和关系。包括一阶、二阶甚至三阶序。

## 模型定义

典型的系统分析将具备如下的结构：

### ⚙️ 功能 Functionality

重点理解系统整体和局部的功能，以及它关键的目标 → 系统的开放性体现于此。

- 「**目标**」是功能的关键导向。考量「**目的性**」对系统功能的设计，功能本身是目的性的表现挖掘系统牵引的「**核心价值**」，以价值驱动系统，实现「**意义**」
- 「**I / O**」 物质（要素等）、能量、信息等与外界的 **交换**，过程即为功能
  - 对「开放」程度和「内敛」程度的控制。
- 「**变化**」理解在时间等维度上，动作或者措施引起的变化对整个实体属性的影响
  - 考量「**突变性**」、「**稳定性**」带来的功能变化，探究「不动点」 [不动点(FixedPoint).mental](https://www.notion.so/FixedPoint-mental-a553f71df31c4004900b07905961bfe5?pvs=21)
  - 发掘性质变化的内部原因、影响因子以及影响结果。
  - 发掘动态演变和演化规律。（等势曲线、逆向思考、运动、多维度变化）
- 「**功能**」系统的功能，可以使用「相似性」类比同类系统
  - 从系统行为角度出发探索也是一种切入思路
- 「**反脆弱**」在系统功能的体现

### 📐 形式 Structure

理解清楚研究对象的结构，它是由什么组成。范畴、内涵的定义。

- 内部实体 / 范畴 / 内涵- 子系统类型 & 数量 $E_i = q$
  - 子系统间属性 & 关系 $f(x)$
  - 子系统之间的相互作用 $F(x)$
- 局部性质 —— 和其它实体的结构相似性- 同类异构、同构 ...
- 结构设计如何符合功能和「目的性」结构和功能如何相互影响。
  - 整体大于局部之和的根因是因为内部的互作用、以及与外部环境的互作用
- 变化的角度看待结构，不断更新对结构边界的定义
- 系统结构特质
  - 「**对称性**」 —— 对称和非对称
  - 「**层次性**」 —— 内部元素属性组在度量 $X([a_i])$ 下的相似性
  - 常规维度包括：时空、质量等
  - 层次性结构具备：功能和结构发展的连续性和阶段性
  - 低层次结构控制强，高层次结构自组织强
  - 针对层次结构建立分层的方法论和认知体系
  - 层次和信息过载的考量
  - 「**相似性**」 —— 系统的结构相似性结构考量
    - 可复制性 / 可替代性
    - 中心化 v.s. 去中心化（中心化的往往具备类 DNA 🧬 结构）
  - 「**自组织性**」 Self-Organization- 涌现- 元胞自动机
    - 原子结构（无意识结构）构筑有意识结构
  - 自适应性 Resilience：大量的修正和反馈回路
    - Meta-resilience
- 结构关键因素发现
  - 「**突变**」引发结构变化
  - 「**稳定**」性所带来的结构特性（自我调节和恢复）
  - 「**规模**」（Scale）所带来的结构特性复杂系统对结构的伸缩性有一定的适应性，对应不同的规模，有不同的结构支撑，因而随着时间发展也会有演化的效应。
  - 「不动点」结构的探索
  - 「第一原理」的推导经典的基本结构
  - 「反脆弱」的结构思考和设计

### 🎰 属性 Properties

属性意味着系统的性质。数字化的系统能够使用属性剖析出系统的当前状态。

- 「**核心指标**」的量化目的性和功能
- （可量化、可被准确量化），必须有所考量 & 设计。
- 抽取认为可理解的有意义的特征，并加以量化。
- 结合「**目的性**」，抽炼出下一个转移状态的量化 & 维度定义属性变化，带来系统的「**突变**」、「**稳定**」状态的考量。
  - 考量「相似性」系统的属性
  - 关注属性量化和数字化的选取、评估方法
  - 考量系统的相关「**涨落有序性**」、「**优化演化**」带来的演化
  - 考量「**量变**」和「**质变**」的关系

### 🔗 关系 Relations

系统内部、外部之间，和其它系统实体的关系表现。

- 寻找关注的属性和内部结构（子系统）、外部系统的关系对内分析、对外分析兼具（输入输出因素分析）- 内部实体属性
  - 实体其它属性
  - 外部实体属性
- 常见的关系结构- **反馈关系（反馈回路）**维持开放与稳定，需要发现系统中子系统之间的「反馈关系」以及整体影响因子。反馈是重要的维稳因素。
  - 系统属性关系大多是非线性结构，用非线性的思考来解释问题
[循环图 & 反馈环模型(FeedbackLoop).mental](https://www.notion.so/FeedbackLoop-mental-16f20cbc4bb44208954f9d377bcb847e?pvs=21)
  - 替代 & 泛化 & 特化- 替代：接口设计
  - 泛化：继承
  - 特化：实例化
- 包含 & 被包含：向上一层看问题 [升维视角(GodView).pattern](https://www.notion.so/GodView-pattern-36caa3233195432db36f2727785e9b0d?pvs=21)
- 反馈 & 控制
- 中间者关系（代理）
- 竞争 & 合作（协同）：发现「**竞争**」、「**协同**」等关键关系，包括其与内部系统 & 外部系统的关系
- 共生 & 寄生
- 扩散 & 传播（模型） → 病毒模型
- 自组织结构下的关系体系
  - 考量「**突变**」、「**稳定**」带来的关系变化
  - 考量「**目的**」应该创建、维护、断绝的关系

### 🧬 基因 Genes

支撑起系统的关键认知，包括：智慧、经验、知识、信息、数据等集合。

包括但不限于背后的：

- 思考 / 思维模型
- 理论模型
- 经验模型
- 原则和方法论
- …

相关联的思考模型包括：

- @🛥️ DFSQBZS - 道法术器兵志势.mental
- 「**不动点**」的寻求：万变之中，不变至美

### 🧪 演化 Elaborations

持续演进，找到系统的变更和关键的设计节点，也可以是一个系统的关键 `Snapshot`

> 实践中一定要定义有效的目标采取合理的手段，切勿治标不治本。

- 过去、现在、未来的视角看问题
- 系统的杠杆点寻求- 参数变革（将参数视为关键的促进点），但往往是下策
- 建立有效的缓冲系统（Buffer），通过设置有效的 buffer，可以让系统更加趋于稳定
- 考虑存量的系统之间的关系，但变革它们往往不是高效的手段
- 变化时间延迟：修改系统响应的速度
- 创造增长地反馈闭环：正向循环，削弱负循环
- 强调结构之间信息的有效性，
- 认真评估：激励、惩罚、限制条件
- 考虑不断创造自组织性的要素：变更结构增强自组织性，产生进化的力量，多样性是大型系统的重要诉求
- 评估系统范式：也就是决定系统之所以为系统的心智模式，反思这类范式 Paradigm（世界观等），下探至此类更基础的认知：对世界或者系统运作的一般假设、规则或者信念。
- 需要明白：杠杆的层次越高，作用力量越大，往往阻力也越大，需要评估到阻力的代价
- 时延性：系统演化的重要思考点- 要素变化带来的时延性的确需要作为分析要素
- 系统子结构之间的目标一致性需要用心设计- 很多时候政策阻力便是来源于系统中各个参与者的有限理性- 比如：公允悲剧（经济学的公共性）
- 关注系统目标的绝对性，不要出现目标的逐渐侵蚀，应该创造良性效益
- 多元化是创造系统结构稳定（LongLive 的重要要素）- 鼓励子系统之间的良性竞争
  - 反垄断等机制就是从此演绎出来的

## 适用场景

需要进行分析的复杂系统。

比如：

- 个人的股票投资系统
- 经典的产品软件系统

## Cases

- Arno 的关键系统（均按照此结构进行建模）
