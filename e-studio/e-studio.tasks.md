# eStudio todos / task queues / ideas

Focus:

> EStudio Agentic System Scenario PMF, find the leverage points with various scenarios and tools.

- 🧰 [e-tools] assemble the best tools for `e-studio` to integrate and build Arno's BP of AI tools
  - 🧰 [vscode-e-studio-extension] stable e-studio x VSCode extension to sync agent / e-doc view MVP
- 🚀 [systemic-systematic] build theory based product x AI in one space of e-studio, fully apply this theory on both Arno's real systems and `e-studio` production features
  - 🎖️ **apply the core-theories to real systems and products** 🎖️
- 🧬 [systems] Arno's all core / none-core systems on `e-studio` with _S&S theory_ x _AI_

Research and Study:

- continue read books about S&S and find the best practices for S&S agentic systems
- find my best practices for AI Booster x Systemic Agent x `e-studio`, to guide the product design and development

## Product Design & Impl.

## S&S Agent

- [2] add `template` and BP from systematic agent when creating new `elaborator`, template x share is the great way
  - research on the best way of `systematic-agent` context-organizing and template of systematic-agent is neat and clean and **institutive**
- [3] [elaboration-session] add `execution-plan` generator to systematic agent view as sub-elaboration session, by chatting or generating in QA format and integrate with `Google DeepResearch` ... tools
- [3] [systematic-composable-section] section content title / content / ref is composable and optional and customizable
  - section can have individual `icon` emoji cool ~
- [3] fork and share the elaborator and copy all of the content formats based on specific topics in AI generation forked system agent

## Elaboration Doc

- [3] upgrade basic-editor instance to stay sync with `elaborator-sys-view-editor` the same as the `doc-editor`
  - `doc-editor` in systematic view should be MonoSpace style and in higher density

## Tools leverages (Agent Canvas)

- `e-studio` x `Google DeepResearch`
- `e-studio` x `VSCode` x `Github Copilot`


- [3] more operations such as doc-create / modify in external editor x API
- [3] consider integrate with `Excalidraw` for the diagram and drawing as a sort type of block
- [2] [MCP] provide MCP of e-studio server to consume the context in client side use claude or other tools to operate private context

## Basic Production Features

- [3] [block] you can now edit / preview block via `/b/[uuid]` operation in e-studio
- [3] add google's `gemini-pro-2.5` to the model list (free for run first)
- [3] support add icons for key resources: workspace, doc, agent and block (emoji icon first)

## Bugfix


## Trace

### 2025.05

- [x] [elaboration-doc] elaborations of system should work in separated record in the ref-list - 25.05
- [x] task list queue of sync from remote have `\n` bug in the list - 25.05
- [x] sync elaboration doc from the remote the list order seems not right, it is flatten and not in the order of the remote one - 25.05
- [x] [systematic-workspace] better systematic all in one workspace & view - 25.05.08
  - [x] one space for new `elaborator` view: merge the elaborator config and the elaborator-sys-view-editor
  - [x] mobile friendly and responsive, user can edit and see things in mobile device
- [x] sync `quotable` content from sever is wrong
- [x] sync doc / agent with `VSCode` extension
- [x] [p1] [e-studio] [vs-code-ext] agent should be sync with more resource references to local files and linkable to web-pages, and more stable and reliable apis and content structure sync
- [x] [p0] where is your second growing curve of money? -> think the future of AI era's money making machine ~
- [x] [p1] [S&S] research on Systematic Study & Thinking theory and add AI to such context to ensure the new inspiration and ideas spark ~ 🎇
- [x] [S&S] S&S research: idea sparking + foundation knowledge enhancement
- [x] [e-studio] optimize the agent systematic view for more clear and easy to use, all in one view-space
- [x] [e-studio] optimize the agent systematic state-editing experience, mobile-view compatible, and bug for sync new agent
- [x] [S&S] connect mental-models & thinking-models with systematic theory and refine the whole theory and guide new topics for deep research
- [x] [mental-models] general models summary and integrations
- [x] [e-studio] elaborate `e-studio` itself in structure and basic content design
- [x] [e-studio] elaborate `e-studio` itself in question form
  - re-design the e-studio S&S design
  - define the S&S agentic core features
- [♥️] [e-studio] what truly make you `fall in love` with `e-studio`?
- [x] [e-studio] fix vscode-extension sync issue: when state is undefined, shall throw error - 25.05
- [x] [e-studio] re-rank this week's main tasks and find the ignite ones to perform
- [🧬] deep research on `systemic` and `systematic` theory and practice and combine with `e-studio` in AI ERA
- [x] [S&S] 🧬 learn S&S repeatedly and find chance for integration of `e-studio` agentic systems
- [😎] [vscode-ext] enhance notifications, status-bar and vibe-coding context, short-keys, welcome prompt by one vibe-coding
- [🚀] QA block driven design the basic block system for `elaborator` S&S basic theory
  - [🆒] setup BP for vibe-coding in `e-studio` project 5.20
  - [x] design the UI / UX for the basic block system for `Elaborator` 5.21
  - [x] design the QA block system for `elaborator` and `systematic agent` 5.21
  - [x] implement the basic block system for `elaborator` 5.22
  - [x] implement the QA block system for `elaborator` 5.22
  - [x] better ux and dx for qa-blocks on Web end 5.23
  - [x] vscode extension enhancement for qa-blocks with block-level sync 5.26
- [x][elaborator] fix add relation of doc / agent source to `elaborator` server error
- [x][extension] add short-cut key for sync in vscode easily