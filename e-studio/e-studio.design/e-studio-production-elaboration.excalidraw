{"type": "excalidraw", "version": 2, "source": "https://marketplace.visualstudio.com/items?itemName=pomdtr.excalidraw-editor", "elements": [{"id": "4zxYMPhcmMzNGQuwwBOl3", "type": "ellipse", "x": 203.3359375, "y": 513.28125, "width": 194.08984375, "height": 194.08984375, "angle": 0, "strokeColor": "#1971c2", "backgroundColor": "#e9ecef", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "a0", "roundness": {"type": 2}, "seed": 1552959795, "version": 117, "versionNonce": 127202333, "isDeleted": false, "boundElements": [{"type": "text", "id": "BFiOA1_qEc6XAh2PDvHYU"}, {"id": "o8D7aKMgXVC02ZwUE2z0F", "type": "arrow"}], "updated": 1748322562128, "link": null, "locked": false}, {"id": "BFiOA1_qEc6XAh2PDvHYU", "type": "text", "x": 264.40976145153127, "y": 572.7050495374688, "width": 71.699951171875, "height": 75, "angle": 0, "strokeColor": "#1971c2", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "a1", "roundness": null, "seed": 688610109, "version": 86, "versionNonce": 1537394493, "isDeleted": false, "boundElements": [], "updated": 1748322545390, "link": null, "locked": false, "text": "VSCode\nbased\nTools", "fontSize": 20, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "4zxYMPhcmMzNGQuwwBOl3", "originalText": "VSCode\nbased\nTools", "autoResize": true, "lineHeight": 1.25}, {"id": "TbvcMW0aJLc12TTITxBVr", "type": "text", "x": 256.48828125, "y": 470.07421875, "width": 102.41993713378906, "height": 25, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "a2", "roundness": null, "seed": 1697000979, "version": 84, "versionNonce": 812956733, "isDeleted": false, "boundElements": [], "updated": 1748322337741, "link": null, "locked": false, "text": "AI FROCE", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "AI FROCE", "autoResize": true, "lineHeight": 1.25}, {"id": "1j5Dfv6qcRWEqW4c_C5_o", "type": "diamond", "x": 528.0703125, "y": 175.12890625, "width": 227.1953125, "height": 227.1953125, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "a3", "roundness": {"type": 2}, "seed": 953585917, "version": 526, "versionNonce": 616543137, "isDeleted": false, "boundElements": [{"type": "text", "id": "yNMYBZ1LoC63mAaR-JgWL"}, {"id": "o8D7aKMgXVC02ZwUE2z0F", "type": "arrow"}], "updated": 1748322949984, "link": null, "locked": false}, {"id": "yNMYBZ1LoC63mAaR-JgWL", "type": "text", "x": 607.9791717529297, "y": 238.927734375, "width": 67.77993774414062, "height": 100, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "a3V", "roundness": null, "seed": 2145361629, "version": 501, "versionNonce": 1353357185, "isDeleted": false, "boundElements": [], "updated": 1748322949984, "link": null, "locked": false, "text": "Limited\nEnergy\n&\nTime", "fontSize": 20, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "1j5Dfv6qcRWEqW4c_C5_o", "originalText": "Limited\nEnergy\n&\nTime", "autoResize": true, "lineHeight": 1.25}, {"id": "o8D7aKMgXVC02ZwUE2z0F", "type": "arrow", "x": 711.484590524087, "y": 604.2711358034967, "width": 311.21604141330954, "height": 1.3779867344835566, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "a6", "roundness": {"type": 2}, "seed": 1448142333, "version": 570, "versionNonce": 267035009, "isDeleted": false, "boundElements": [{"type": "text", "id": "5P0UT3N-Dc1GzhNcks0KT"}], "updated": 1748322992002, "link": null, "locked": false, "points": [[0, 0], [-311.21604141330954, -1.3779867344835566]], "lastCommittedPoint": null, "startBinding": {"elementId": "3Psmd-KpqDndW32KD-In_", "focus": -0.13807002670028884, "gap": 1}, "endBinding": {"elementId": "4zxYMPhcmMzNGQuwwBOl3", "focus": -0.08075829280288796, "gap": 3.118945805842319}, "startArrowhead": "arrow", "endArrowhead": "arrow", "elbowed": false}, {"id": "5P0UT3N-Dc1GzhNcks0KT", "type": "text", "x": 514.0543673670504, "y": 591.0567076213991, "width": 83.63992309570312, "height": 25, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "a7", "roundness": null, "seed": 439407581, "version": 19, "versionNonce": 345499887, "isDeleted": false, "boundElements": [], "updated": 1748322992001, "link": null, "locked": false, "text": "Leverage", "fontSize": 20, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "o8D7aKMgXVC02ZwUE2z0F", "originalText": "Leverage", "autoResize": true, "lineHeight": 1.25}, {"id": "CPTyeWx15gw8JxPf3l_sR", "type": "rectangle", "x": -14.2578125, "y": 527.05859375, "width": 245.234375, "height": 70.3046875, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "a8", "roundness": {"type": 3}, "seed": 1890809491, "version": 268, "versionNonce": 791788285, "isDeleted": false, "boundElements": [{"type": "text", "id": "Zuof0B3Cajk1KnFw9W97e"}], "updated": 1748322501644, "link": null, "locked": false}, {"id": "Zuof0B3Cajk1KnFw9W97e", "type": "text", "x": 60.08940887451172, "y": 549.7109375, "width": 96.53993225097656, "height": 25, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "a8V", "roundness": null, "seed": 1248571485, "version": 127, "versionNonce": 1453019997, "isDeleted": false, "boundElements": [], "updated": 1748322501644, "link": null, "locked": false, "text": "Extension", "fontSize": 20, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "CPTyeWx15gw8JxPf3l_sR", "originalText": "Extension", "autoResize": true, "lineHeight": 1.25}, {"id": "23ei8xiZ6Hr5oz7U3Ium-", "type": "diamond", "x": 351.41796875, "y": 168.80859375, "width": 237.88671875, "height": 237.88671875, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "aA", "roundness": {"type": 2}, "seed": 6329277, "version": 611, "versionNonce": 692977505, "isDeleted": false, "boundElements": [{"type": "text", "id": "Jn9ejdtQFDV2m85WKWoHB"}], "updated": 1748322949984, "link": null, "locked": false}, {"id": "Jn9ejdtQFDV2m85WKWoHB", "type": "text", "x": 417.48968505859375, "y": 237.7802734375, "width": 105.7999267578125, "height": 100, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "aB", "roundness": null, "seed": 1991984787, "version": 600, "versionNonce": 217672513, "isDeleted": false, "boundElements": [], "updated": 1748322949984, "link": null, "locked": false, "text": "Production\nPhrase\nME\nSatisfied", "fontSize": 20, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "23ei8xiZ6Hr5oz7U3Ium-", "originalText": "Production Phrase\nME Satisfied", "autoResize": true, "lineHeight": 1.25}, {"id": "gwoc9tHzWfamYIC0BuTw4", "type": "rectangle", "x": -12.609375, "y": 621.33203125, "width": 245.234375, "height": 70.3046875, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "aD", "roundness": {"type": 3}, "seed": 1236515421, "version": 334, "versionNonce": 809013395, "isDeleted": false, "boundElements": [{"type": "text", "id": "HHpvfQhqzYR8p-a9c0rTO"}], "updated": 1748322506977, "link": null, "locked": false}, {"id": "HHpvfQhqzYR8p-a9c0rTO", "type": "text", "x": 61.93785095214844, "y": 643.984375, "width": 96.13992309570312, "height": 25, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "aE", "roundness": null, "seed": 57042621, "version": 216, "versionNonce": 2057242163, "isDeleted": false, "boundElements": [], "updated": 1748322506977, "link": null, "locked": false, "text": "MCPClient", "fontSize": 20, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "gwoc9tHzWfamYIC0BuTw4", "originalText": "MCPClient", "autoResize": true, "lineHeight": 1.25}, {"id": "3Psmd-KpqDndW32KD-In_", "type": "ellipse", "x": 711.5625, "y": 487.42578125, "width": 206.05078125, "height": 206.05078125, "angle": 0, "strokeColor": "#e03131", "backgroundColor": "#e9ecef", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "aF", "roundness": {"type": 2}, "seed": 1847809395, "version": 491, "versionNonce": 661002959, "isDeleted": false, "boundElements": [{"type": "text", "id": "mO-wbyve8zK06Sl_B3uil"}, {"id": "o8D7aKMgXVC02ZwUE2z0F", "type": "arrow"}], "updated": 1748322992001, "link": null, "locked": false}, {"id": "mO-wbyve8zK06Sl_B3uil", "type": "text", "x": 759.397972459357, "y": 565.6012195296695, "width": 110.679931640625, "height": 50, "angle": 0, "strokeColor": "#e03131", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "aG", "roundness": null, "seed": 1294787997, "version": 488, "versionNonce": 2109275553, "isDeleted": false, "boundElements": [], "updated": 1748322992001, "link": null, "locked": false, "text": "Elaboration\nStudio", "fontSize": 20, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "3Psmd-KpqDndW32KD-In_", "originalText": "Elaboration\nStudio", "autoResize": true, "lineHeight": 1.25}, {"id": "RbVVvV0dVXzi9rnwux-qP", "type": "rectangle", "x": 865.0078125, "y": 452.33984375, "width": 245.234375, "height": 70.3046875, "angle": 6.117607355369801, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "aH", "roundness": {"type": 3}, "seed": 95462067, "version": 727, "versionNonce": 725875855, "isDeleted": false, "boundElements": [{"type": "text", "id": "3op5QoF-6nQ3pIzqxLqMc"}], "updated": 1748323014901, "link": null, "locked": false}, {"id": "3op5QoF-6nQ3pIzqxLqMc", "type": "text", "x": 889.575080871582, "y": 474.9921875, "width": 196.09983825683594, "height": 25, "angle": 6.117607355369801, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "aI", "roundness": null, "seed": 663737427, "version": 627, "versionNonce": 768840367, "isDeleted": false, "boundElements": [], "updated": 1748323014901, "link": null, "locked": false, "text": "S&S Agentic System", "fontSize": 20, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "RbVVvV0dVXzi9rnwux-qP", "originalText": "S&S Agentic System", "autoResize": true, "lineHeight": 1.25}, {"id": "_hmkdq21njlZVK9AoK9Xs", "type": "rectangle", "x": 433.63671875, "y": 633.23046875, "width": 245.234375, "height": 70.3046875, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "aJ", "roundness": {"type": 3}, "seed": 1359293885, "version": 582, "versionNonce": 2032628033, "isDeleted": false, "boundElements": [{"type": "text", "id": "4f-5phyHKiA-ADUaTWi_8"}, {"id": "3Y5nyiCyyW2tItoQaWNFZ", "type": "arrow"}], "updated": 1748326006870, "link": null, "locked": false}, {"id": "4f-5phyHKiA-ADUaTWi_8", "type": "text", "x": 504.8739471435547, "y": 655.8828125, "width": 102.75991821289062, "height": 25, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "aK", "roundness": null, "seed": 988966429, "version": 472, "versionNonce": 1565390333, "isDeleted": false, "boundElements": [], "updated": 1748322736097, "link": null, "locked": false, "text": "MCPServer", "fontSize": 20, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "_hmkdq21njlZVK9AoK9Xs", "originalText": "MCPServer", "autoResize": true, "lineHeight": 1.25}, {"id": "Hptir5ak7x-aDJfCgYiKF", "type": "rectangle", "x": 893.78515625, "y": 558.296875, "width": 245.234375, "height": 70.3046875, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "aL", "roundness": {"type": 3}, "seed": 810821341, "version": 880, "versionNonce": 2017238639, "isDeleted": false, "boundElements": [{"type": "text", "id": "D6lEDo9jTaeQNnVoom6Kc"}], "updated": 1748323013485, "link": null, "locked": false}, {"id": "D6lEDo9jTaeQNnVoom6Kc", "type": "text", "x": 955.952392578125, "y": 580.94921875, "width": 120.89990234375, "height": 25, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "aM", "roundness": null, "seed": 71648061, "version": 791, "versionNonce": 2065496207, "isDeleted": false, "boundElements": [], "updated": 1748323013485, "link": null, "locked": false, "text": "Block Based", "fontSize": 20, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "Hptir5ak7x-aDJfCgYiKF", "originalText": "Block Based", "autoResize": true, "lineHeight": 1.25}, {"id": "XsnEqrBfywOTzE5SiagDE", "type": "rectangle", "x": 858.89453125, "y": 663.78515625, "width": 245.234375, "height": 70.3046875, "angle": 0.16717147368024676, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "aN", "roundness": {"type": 3}, "seed": 1923476115, "version": 1008, "versionNonce": 1392620161, "isDeleted": false, "boundElements": [{"type": "text", "id": "a31yPmV2QnymMmAZGs8Q2"}], "updated": 1748323010784, "link": null, "locked": false}, {"id": "a31yPmV2QnymMmAZGs8Q2", "type": "text", "x": 884.5717926025391, "y": 686.4375, "width": 193.87985229492188, "height": 25, "angle": 0.16717147368024676, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "aO", "roundness": null, "seed": 2052137011, "version": 939, "versionNonce": 671726177, "isDeleted": false, "boundElements": [], "updated": 1748323010784, "link": null, "locked": false, "text": "All in one workspace", "fontSize": 20, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "XsnEqrBfywOTzE5SiagDE", "originalText": "All in one workspace", "autoResize": true, "lineHeight": 1.25}, {"id": "R0SoN-UV2yR7SLoNDgC49", "type": "text", "x": 660.2734375, "y": 770.83984375, "width": 336.8997802734375, "height": 25, "angle": 0, "strokeColor": "#e03131", "backgroundColor": "#e9ecef", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "aP", "roundness": null, "seed": 2088524477, "version": 388, "versionNonce": 1002009455, "isDeleted": false, "boundElements": [], "updated": 1748323008634, "link": null, "locked": false, "text": "elaborate ideas and solve problems", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "elaborate ideas and solve problems", "autoResize": true, "lineHeight": 1.25}, {"id": "H3xp-Q2m5SMYf6B3_BN6z", "type": "text", "x": 481.2734375, "y": 544.5859375, "width": 174.61988830566406, "height": 25, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "aQ", "roundness": null, "seed": 333283617, "version": 88, "versionNonce": 655997441, "isDeleted": false, "boundElements": null, "updated": 1748322830565, "link": null, "locked": false, "text": "AI iteration loops", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "AI iteration loops", "autoResize": true, "lineHeight": 1.25}, {"id": "vMw9rzYhX9XIWgOqOJz_o", "type": "text", "x": 159.0859375, "y": 743.66796875, "width": 287.97979736328125, "height": 75, "angle": 0, "strokeColor": "#1971c2", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "aR", "roundness": null, "seed": 1054993857, "version": 170, "versionNonce": 7622319, "isDeleted": false, "boundElements": null, "updated": 1748322876802, "link": null, "locked": false, "text": "leverage the agentic features\nfrom public products like \nCURSOR and GithubCopilot", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "leverage the agentic features\nfrom public products like \nCURSOR and GithubCopilot", "autoResize": true, "lineHeight": 1.25}, {"id": "Mb_dfpFDLK6GGqPvnVlES", "type": "text", "x": 198.765625, "y": 270.484375, "width": 120.69992065429688, "height": 25, "angle": 0, "strokeColor": "#1971c2", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "aS", "roundness": null, "seed": 1273119055, "version": 182, "versionNonce": 1678531361, "isDeleted": false, "boundElements": null, "updated": 1748322949984, "link": null, "locked": false, "text": "Arno FIRST", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Arno FIRST", "autoResize": true, "lineHeight": 1.25}, {"id": "Eg_AtMFzONPXrCn3ym_Qr", "type": "text", "x": 779.4508209228516, "y": 275.51171875, "width": 124.95993041992188, "height": 25, "angle": 0, "strokeColor": "#e03131", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "aT", "roundness": null, "seed": 1631174881, "version": 280, "versionNonce": 1004687713, "isDeleted": false, "boundElements": [], "updated": 1748322967817, "link": null, "locked": false, "text": "P0 task only", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "P0 task only", "autoResize": true, "lineHeight": 1.25}, {"id": "PoGK-pULJ044AIUg5DDhU", "type": "text", "x": 486.8888393863316, "y": 512.6435797475076, "width": 147.87986755371094, "height": 25, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#e9ecef", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "aU", "roundness": null, "seed": *********, "version": 69, "versionNonce": *********, "isDeleted": false, "boundElements": null, "updated": 1748323388239, "link": null, "locked": false, "text": "sync & enhance", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "sync & enhance", "autoResize": true, "lineHeight": 1.25}, {"id": "C8bKQ_2Taxf6vCAu00l6w", "type": "text", "x": 449.35592162734895, "y": 859.2968138220058, "width": 228.21987915039062, "height": 25, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#e9ecef", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "aV", "roundness": null, "seed": 59628719, "version": 106, "versionNonce": *********, "isDeleted": false, "boundElements": [{"id": "3Y5nyiCyyW2tItoQaWNFZ", "type": "arrow"}], "updated": 1748326006870, "link": null, "locked": false, "text": "More TOOLs to engage", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "More TOOLs to engage", "autoResize": true, "lineHeight": 1.25}, {"id": "-imz9yahtA6B7Nr9ixKkt", "type": "ellipse", "x": 411.425830135467, "y": 909.5657692826258, "width": 149.4961930632919, "height": 149.4961930632919, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#e9ecef", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "aW", "roundness": {"type": 2}, "seed": 1157937711, "version": 149, "versionNonce": 1999680129, "isDeleted": false, "boundElements": [{"type": "text", "id": "-CvcPCZyqk_AIOIlTNO_A"}], "updated": 1748325996073, "link": null, "locked": false}, {"id": "-CvcPCZyqk_AIOIlTNO_A", "type": "text", "x": 442.1990837585846, "y": 946.9589798759582, "width": 88.23991394042969, "height": 75, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#e9ecef", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "aX", "roundness": null, "seed": 611343727, "version": 127, "versionNonce": 1851308641, "isDeleted": false, "boundElements": null, "updated": 1748325996073, "link": null, "locked": false, "text": "Google\nDeep\nResearch", "fontSize": 20, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "-imz9yahtA6B7Nr9ixKkt", "originalText": "Google\nDeep\nResearch", "autoResize": true, "lineHeight": 1.25}, {"id": "oY5JhXRI9hpWhxPUTeKn5", "type": "ellipse", "x": 779.8640434594913, "y": 909.446617162756, "width": 149.4961930632919, "height": 149.4961930632919, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#e9ecef", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "aY", "roundness": {"type": 2}, "seed": 1496034383, "version": 312, "versionNonce": 1995753025, "isDeleted": false, "boundElements": [{"type": "text", "id": "OOcU66_2UDdCil085OtQt"}], "updated": 1748325996073, "link": null, "locked": false}, {"id": "OOcU66_2UDdCil085OtQt", "type": "text", "x": 822.2872757203043, "y": 971.8398277560884, "width": 64.93995666503906, "height": 25, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#e9ecef", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "aZ", "roundness": null, "seed": 1789872751, "version": 298, "versionNonce": 1273255457, "isDeleted": false, "boundElements": [], "updated": 1748325996073, "link": null, "locked": false, "text": "<PERSON><PERSON><PERSON>", "fontSize": 20, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "oY5JhXRI9hpWhxPUTeKn5", "originalText": "<PERSON><PERSON><PERSON>", "autoResize": true, "lineHeight": 1.25}, {"id": "Nnpvm6HnIS4whnNQTuJAY", "type": "ellipse", "x": 594.9134751727231, "y": 912.829213454615, "width": 149.4961930632919, "height": 149.4961930632919, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#e9ecef", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "aa", "roundness": {"type": 2}, "seed": 1974379983, "version": 410, "versionNonce": 538372609, "isDeleted": false, "boundElements": [{"type": "text", "id": "i94i_ejaNYPMLYgaxKuVT"}], "updated": 1748325996073, "link": null, "locked": false}, {"id": "i94i_ejaNYPMLYgaxKuVT", "type": "text", "x": 623.7067025507235, "y": 950.2224240479475, "width": 92.19996643066406, "height": 75, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#e9ecef", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "ab", "roundness": null, "seed": 1066865647, "version": 424, "versionNonce": 478937569, "isDeleted": false, "boundElements": [], "updated": 1748325996073, "link": null, "locked": false, "text": "Google\nNotebook\nML", "fontSize": 20, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "Nnpvm6HnIS4whnNQTuJAY", "originalText": "Google\nNotebook\nML", "autoResize": true, "lineHeight": 1.25}, {"id": "4uK04Vu-raf2A4ZRg1cz_", "type": "ellipse", "x": 223.37068716986937, "y": 910.8764426011935, "width": 149.4961930632919, "height": 149.4961930632919, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#e9ecef", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "ac", "roundness": {"type": 2}, "seed": 64093839, "version": 294, "versionNonce": 508763585, "isDeleted": false, "boundElements": [{"id": "xZE06t4x-Dbs4DL4CXNoo", "type": "text"}], "updated": 1748325996073, "link": null, "locked": false}, {"id": "xZE06t4x-Dbs4DL4CXNoo", "type": "text", "x": 250.69394384474478, "y": 960.769653194526, "width": 95.13990783691406, "height": 50, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#e9ecef", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "ad", "roundness": null, "seed": 156325039, "version": 345, "versionNonce": 881573281, "isDeleted": false, "boundElements": [], "updated": 1748325996073, "link": null, "locked": false, "text": "LLM\nAssistant", "fontSize": 20, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "4uK04Vu-raf2A4ZRg1cz_", "originalText": "LLM\nAssistant", "autoResize": true, "lineHeight": 1.25}, {"id": "3Y5nyiCyyW2tItoQaWNFZ", "type": "arrow", "x": 562.0804465863819, "y": 707.9272846518727, "width": 0.14563036872959856, "height": 142.4728375531895, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#e9ecef", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "ae", "roundness": {"type": 2}, "seed": 1098971439, "version": 64, "versionNonce": 1753850145, "isDeleted": false, "boundElements": null, "updated": 1748326006870, "link": null, "locked": false, "points": [[0, 0], [-0.14563036872959856, 142.4728375531895]], "lastCommittedPoint": null, "startBinding": {"elementId": "_hmkdq21njlZVK9AoK9Xs", "focus": -0.04783377103670727, "gap": 4.3921284018726965}, "endBinding": {"elementId": "C8bKQ_2Taxf6vCAu00l6w", "focus": -0.013607421234132508, "gap": 8.896691616943599}, "startArrowhead": "arrow", "endArrowhead": "arrow", "elbowed": false}], "appState": {"gridSize": 20, "gridStep": 5, "gridModeEnabled": false, "viewBackgroundColor": "#ffffff"}, "files": {}}