# Horizon App

---
description: Horizon app guide for development, must read before execute the related code.
globs:
alwaysApply: false
---

# Horizon App

Horizon App is a web app that allows users to explore the `horizon` of a topic through AI powered `elaboration` of the topic. By letting user adding RSS feeds to the topic, the app will use AI to generate the horizon of the topic and filter, aggregate and display it in a timeline.

## Import Feeds

### AI

weekly

### FrontEnd x FullStack

weekly

* Github Trends
* FE Classical Sites -> Feedly
* React Weekly
* Github Trending Repos

### Finance

weekly

* Business News
* Economic News

## Project Features

* support stash every AI generated topic content in table and make it manageable
* support schedule to generate topic horizon via cron job
* support show e-studio remaining credits & tokens and charge tokens' way
* support mark topic as public and shared it with other users

## Project Description

routes:

* `/` - home page a simple hello world page
* `/horizon/new` - a page to create a new topic
* `/horizon/[topicUuid]` - a page to display the horizon of a topic
* `/horizon/[topicUuid]/config` - a page to configure the horizon of a topic

## Tech Stack

[package.json](mdc:package.json)

* Next.js 15 and Turbopack & Turborepo
* React 19
* TypeScript 5.x
* TailwindCSS 3
* shadcn/ui / lucide-react / radix-ui
* `zod` for data validation
* Lucide Icons
* Zustand for state management (complex state)
* Database is using PlanetScale / Prisma

## Project Structure

* `apps/horizon` - contains the horizon app logic
* `shared` - contains the shared biz logic
* `shared-packages` - contains the shared packages

## Notice

* This project is a monorepo project, so you need to use `pnpm` to add dependencies under specific sub-packages. for app is under `apps/horizon`, and shared is under `shared`.
