# New Elaboration Block Architecture

* every section can have *blocks*
* `ResourceType` should be based on the `block` type
* block have status `archived` or `active`
* block can be `sorted` and drag to reorder the blocks
* block title is editable
* every block is show with `title` but can see `detail` when click the block, the detailed behavior is based on the `block` type
* block can be `synced` back to the vscode extension to parsed as a mutable or immutable content block, resource can be synced into `xxx.sys/*` as resource folder
* block have its own `toText()` method to convert the block into a text better markdown format to be consumed by the AI

## QA Block Design

* every section can have `QA` block to do the qa dive
* block detail is a `tip-tap` simple rich text editor exported markdown to be stored in `Block` data content
* add AI context assist skill to help user to understand the question and answer according to the rich-text content

## ideas of blocks

* OKR block
* Goal block
* Vision block
* I/O block
* Connection Circle / Casual Loop / Feedback Loop block
* Data query 
* Flow Diagram block -> `Mermaid` x `Excalidraw`
* Environment Sandbox block -> place the system in the sandbox of environment
* Leverage Points -> State mutator
* Decision Tree x MECE
* RCA block -> find the root cause of the problem
* BCG Matrix -> Stars / Cash Cows / Dogs (Pets) / Question Marks (Problem Children | Wild Cats)

---

mental-model blocks -> [MentalModels](../../meta/mental-models/mental-models.arno.md)

---

