{"type": "excalidraw", "version": 2, "source": "https://marketplace.visualstudio.com/items?itemName=pomdtr.excalidraw-editor", "elements": [{"id": "8aGrKFtoUK7mRXztkNns1", "type": "rectangle", "x": 215.65625, "y": 891.25, "width": 298.51171874999994, "height": 103.359375, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 2, "opacity": 100, "groupIds": [], "frameId": null, "index": "aC", "roundness": {"type": 3}, "seed": 1293826948, "version": 65, "versionNonce": 411903420, "isDeleted": false, "boundElements": [{"type": "text", "id": "I9Q8V2JRTWvnhCvWswUgz"}, {"id": "2l9H89LdkV0k6m7zyTglS", "type": "arrow"}], "updated": 1747100693508, "link": null, "locked": false}, {"id": "I9Q8V2JRTWvnhCvWswUgz", "type": "text", "x": 278.49217224121094, "y": 905.4296875, "width": 172.83987426757812, "height": 75, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 2, "opacity": 100, "groupIds": [], "frameId": null, "index": "aCV", "roundness": null, "seed": 53463740, "version": 64, "versionNonce": 1309033860, "isDeleted": false, "boundElements": [], "updated": 1747100648010, "link": null, "locked": false, "text": "Build your system\nwith S&S theory\nfrom scratch", "fontSize": 20, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "8aGrKFtoUK7mRXztkNns1", "originalText": "Build your system\nwith S&S theory\nfrom scratch", "autoResize": true, "lineHeight": 1.25}, {"id": "QDKzTOYdHl2VIT-HsbU8s", "type": "diamond", "x": 688.42578125, "y": 828.68359375, "width": 208.7578125, "height": 220, "angle": 0, "strokeColor": "#1971c2", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 4, "strokeStyle": "solid", "roughness": 2, "opacity": 100, "groupIds": [], "frameId": null, "index": "aF", "roundness": {"type": 2}, "seed": 1071089340, "version": 203, "versionNonce": 190694762, "isDeleted": false, "boundElements": [{"type": "text", "id": "gyrPRe2GZ8Nv4bI8Dq6VS"}, {"id": "2l9H89LdkV0k6m7zyTglS", "type": "arrow"}, {"id": "I4dmoZIKCev7JvzwYxMUx", "type": "arrow"}, {"id": "NvzzcBzyV90uksOGnFg_H", "type": "arrow"}, {"id": "JNF1pJ7FCfncPeycVtMuO", "type": "arrow"}], "updated": 1748324556710, "link": null, "locked": false}, {"id": "gyrPRe2GZ8Nv4bI8Dq6VS", "type": "text", "x": 753.6052627563477, "y": 888.68359375, "width": 78.01994323730469, "height": 100, "angle": 0, "strokeColor": "#1971c2", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 2, "opacity": 100, "groupIds": [], "frameId": null, "index": "aG", "roundness": null, "seed": 476934332, "version": 113, "versionNonce": 911941174, "isDeleted": false, "boundElements": [], "updated": 1748324556710, "link": null, "locked": false, "text": "Specific\nS&S\npowered\nsystem", "fontSize": 20, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "QDKzTOYdHl2VIT-HsbU8s", "originalText": "Specific\nS&S powered\nsystem", "autoResize": true, "lineHeight": 1.25}, {"id": "2l9H89LdkV0k6m7zyTglS", "type": "arrow", "x": 528.2723190481797, "y": 936.5061198308649, "width": 166.2132395338856, "height": 1.4208810903694484, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "aH", "roundness": {"type": 2}, "seed": 1117011844, "version": 142, "versionNonce": 1626085180, "isDeleted": false, "boundElements": [{"type": "text", "id": "YZBO62qVmucgHkhsjeumR"}], "updated": 1747100707970, "link": null, "locked": false, "points": [[0, 0], [166.2132395338856, -1.4208810903694484]], "lastCommittedPoint": null, "startBinding": {"elementId": "8aGrKFtoUK7mRXztkNns1", "focus": -0.09611590110332931, "gap": 15.2265625}, "endBinding": {"elementId": "QDKzTOYdHl2VIT-HsbU8s", "focus": 0.04061797307754928, "gap": 1.439657241315336}, "startArrowhead": null, "endArrowhead": "arrow", "elbowed": false}, {"id": "YZBO62qVmucgHkhsjeumR", "type": "text", "x": 574.9389745206889, "y": 923.2956792856802, "width": 72.87992858886719, "height": 25, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "aI", "roundness": null, "seed": 837320708, "version": 9, "versionNonce": 1889999748, "isDeleted": false, "boundElements": [], "updated": 1747100706712, "link": null, "locked": false, "text": "crafted", "fontSize": 20, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "2l9H89LdkV0k6m7zyTglS", "originalText": "crafted", "autoResize": true, "lineHeight": 1.25}, {"id": "E88osxqlUbn8OyKa9BYmj", "type": "rectangle", "x": 216.943359375, "y": 1206.1875, "width": 298.51171874999994, "height": 103.359375, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 2, "opacity": 100, "groupIds": [], "frameId": null, "index": "aJ", "roundness": {"type": 3}, "seed": 1002574012, "version": 654, "versionNonce": 256794372, "isDeleted": false, "boundElements": [{"type": "text", "id": "IiyYxnk5Ho9-k7gNF3m_K"}, {"id": "JNF1pJ7FCfncPeycVtMuO", "type": "arrow"}, {"id": "K1GtayjCm-wd_I-gLfL3U", "type": "arrow"}], "updated": 1747100980122, "link": null, "locked": false}, {"id": "IiyYxnk5Ho9-k7gNF3m_K", "type": "text", "x": 250.9293060302734, "y": 1232.8671875, "width": 230.53982543945312, "height": 50, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 2, "opacity": 100, "groupIds": [], "frameId": null, "index": "aK", "roundness": null, "seed": 389585212, "version": 742, "versionNonce": 1846038588, "isDeleted": false, "boundElements": [], "updated": 1747100973434, "link": null, "locked": false, "text": "Ask questions based on\nSystems", "fontSize": 20, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "E88osxqlUbn8OyKa9BYmj", "originalText": "Ask questions based on\nSystems", "autoResize": true, "lineHeight": 1.25}, {"id": "AMX6j1cfnfFYXp4QCHc0l", "type": "ellipse", "x": 715.2109375, "y": 1193.41015625, "width": 141.23046875, "height": 131.9921875, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "aL", "roundness": {"type": 2}, "seed": 1149180548, "version": 266, "versionNonce": 373124612, "isDeleted": false, "boundElements": [{"type": "text", "id": "z1IsUDHgOdUfQp9Kq66oh"}, {"id": "3ExpGE1K8ut6pe4Dn8rbo", "type": "arrow"}, {"id": "NvzzcBzyV90uksOGnFg_H", "type": "arrow"}, {"id": "K1GtayjCm-wd_I-gLfL3U", "type": "arrow"}], "updated": 1747100980123, "link": null, "locked": false}, {"id": "z1IsUDHgOdUfQp9Kq66oh", "type": "text", "x": 742.7036964989265, "y": 1221.739964577552, "width": 86.37992858886719, "height": 75, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "aM", "roundness": null, "seed": 1197120700, "version": 257, "versionNonce": 1971934140, "isDeleted": false, "boundElements": [], "updated": 1747100872641, "link": null, "locked": false, "text": "Question\n&\nProblem", "fontSize": 20, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "AMX6j1cfnfFYXp4QCHc0l", "originalText": "Question\n&\nProblem", "autoResize": true, "lineHeight": 1.25}, {"id": "Pir3h6AKEXQpujZzXkcu4", "type": "rectangle", "x": 994.767578125, "y": 1205.875, "width": 298.51171874999994, "height": 103.359375, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 2, "opacity": 100, "groupIds": [], "frameId": null, "index": "aN", "roundness": {"type": 3}, "seed": 2118561668, "version": 542, "versionNonce": 436936196, "isDeleted": false, "boundElements": [{"type": "text", "id": "uGssoNRjRK8hPDta9EOrg"}, {"id": "I4dmoZIKCev7JvzwYxMUx", "type": "arrow"}, {"id": "3ExpGE1K8ut6pe4Dn8rbo", "type": "arrow"}, {"id": "vzUT0uB4jP5b8Eh1PTqV7", "type": "arrow"}, {"id": "NvzzcBzyV90uksOGnFg_H", "type": "arrow"}], "updated": 1747101040130, "link": null, "locked": false}, {"id": "uGssoNRjRK8hPDta9EOrg", "type": "text", "x": 1072.6034774780273, "y": 1245.0546875, "width": 142.8399200439453, "height": 25, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 2, "opacity": 100, "groupIds": [], "frameId": null, "index": "aO", "roundness": null, "seed": 474881796, "version": 573, "versionNonce": 252941756, "isDeleted": false, "boundElements": [], "updated": 1747100872641, "link": null, "locked": false, "text": "Solve Problems", "fontSize": 20, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "Pir3h6AKEXQpujZzXkcu4", "originalText": "Solve Problems", "autoResize": true, "lineHeight": 1.25}, {"id": "I4dmoZIKCev7JvzwYxMUx", "type": "arrow", "x": 898.9933086331966, "y": 937.0184392337297, "width": 563.3670672476217, "height": 257.1076173672385, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "aP", "roundness": {"type": 2}, "seed": 1010892092, "version": 1060, "versionNonce": 1568162339, "isDeleted": false, "boundElements": [{"type": "text", "id": "CkHYD56bCapx0n454BtPa"}], "updated": 1747566039981, "link": null, "locked": false, "points": [[0, 0], [302.7742777930807, 36.51216989712964], [563.3670672476217, 257.1076173672385]], "lastCommittedPoint": null, "startBinding": {"elementId": "QDKzTOYdHl2VIT-HsbU8s", "focus": -0.13155143356809937, "gap": 8.553857406308033}, "endBinding": {"elementId": "S7aVqEDxhfKzFFtNGLy87", "focus": 0.09152860089318024, "gap": 8.735265205775496}, "startArrowhead": null, "endArrowhead": "arrow", "elbowed": false}, {"id": "CkHYD56bCapx0n454BtPa", "type": "text", "x": 1052.595832824707, "y": 935.91015625, "width": 220.43984985351562, "height": 25, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "aQ", "roundness": null, "seed": 544468668, "version": 30, "versionNonce": 30203324, "isDeleted": false, "boundElements": [], "updated": 1747101016557, "link": null, "locked": false, "text": "provide useful context", "fontSize": 20, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "I4dmoZIKCev7JvzwYxMUx", "originalText": "provide useful context", "autoResize": true, "lineHeight": 1.25}, {"id": "3ExpGE1K8ut6pe4Dn8rbo", "type": "arrow", "x": 986.5606313795016, "y": 1259.9483961342332, "width": 126.31167226381126, "height": 0.15153503758051556, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "aR", "roundness": {"type": 2}, "seed": 1425030332, "version": 195, "versionNonce": 977163964, "isDeleted": false, "boundElements": [], "updated": 1747100872642, "link": null, "locked": false, "points": [[0, 0], [-126.31167226381126, -0.15153503758051556]], "lastCommittedPoint": null, "startBinding": {"elementId": "Pir3h6AKEXQpujZzXkcu4", "focus": -0.04980095928558542, "gap": 8.787109375}, "endBinding": {"elementId": "AMX6j1cfnfFYXp4QCHc0l", "focus": 0.0034136427541585353, "gap": 3.8087180766656834}, "startArrowhead": null, "endArrowhead": "arrow", "elbowed": false}, {"id": "NvzzcBzyV90uksOGnFg_H", "type": "arrow", "x": 1098.453765534113, "y": 1201.7387057762724, "width": 232.58473959500475, "height": 199.42048884833912, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "aS", "roundness": {"type": 2}, "seed": 882720132, "version": 298, "versionNonce": 999412284, "isDeleted": false, "boundElements": [{"type": "text", "id": "q1jDC9AhbqlcHDFY6N_pu"}], "updated": 1747101043469, "link": null, "locked": false, "points": [[0, 0], [-232.58473959500475, -199.42048884833912]], "lastCommittedPoint": null, "startBinding": {"elementId": "Pir3h6AKEXQpujZzXkcu4", "focus": 0.0932023015540458, "gap": 4.136294223727646}, "endBinding": {"elementId": "QDKzTOYdHl2VIT-HsbU8s", "focus": 0.008986604294250355, "gap": 20.476659193285}, "startArrowhead": null, "endArrowhead": "arrow", "elbowed": false}, {"id": "q1jDC9AhbqlcHDFY6N_pu", "type": "text", "x": 753.6155507048724, "y": 1098.6202582271028, "width": 71.07997131347656, "height": 25, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "aT", "roundness": null, "seed": 1636301956, "version": 5, "versionNonce": 1759881732, "isDeleted": false, "boundElements": [], "updated": 1747100895143, "link": null, "locked": false, "text": "evolved", "fontSize": 20, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "NvzzcBzyV90uksOGnFg_H", "originalText": "evolved", "autoResize": true, "lineHeight": 1.25}, {"id": "JNF1pJ7FCfncPeycVtMuO", "type": "arrow", "x": 362.53125, "y": 1195.12109375, "width": 364.6171875, "height": 195.08203125, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "aU", "roundness": {"type": 2}, "seed": 2118853692, "version": 76, "versionNonce": 766286980, "isDeleted": false, "boundElements": [{"type": "text", "id": "BLqzJjIGcBtLkA6NV1KyM"}], "updated": 1747100957408, "link": null, "locked": false, "points": [[0, 0], [147.22265625, -114.7265625], [364.6171875, -195.08203125]], "lastCommittedPoint": null, "startBinding": {"elementId": "E88osxqlUbn8OyKa9BYmj", "focus": -0.49194418873599627, "gap": 11.06640625}, "endBinding": {"elementId": "QDKzTOYdHl2VIT-HsbU8s", "focus": -0.23842948831496227, "gap": 13.934720165556717}, "startArrowhead": null, "endArrowhead": "arrow", "elbowed": false}, {"id": "BLqzJjIGcBtLkA6NV1KyM", "type": "text", "x": 454.7539596557617, "y": 1067.89453125, "width": 109.99989318847656, "height": 25, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "aV", "roundness": null, "seed": 105987132, "version": 17, "versionNonce": 976706748, "isDeleted": false, "boundElements": [], "updated": 1747100956685, "link": null, "locked": false, "text": "best-guider", "fontSize": 20, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "JNF1pJ7FCfncPeycVtMuO", "originalText": "best-guider", "autoResize": true, "lineHeight": 1.25}, {"id": "K1GtayjCm-wd_I-gLfL3U", "type": "arrow", "x": 539.42578125, "y": 1260.3203125, "width": 166.84375, "height": 0.45703125, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "aW", "roundness": {"type": 2}, "seed": 200299068, "version": 66, "versionNonce": 531881604, "isDeleted": false, "boundElements": [], "updated": 1747100980123, "link": null, "locked": false, "points": [[0, 0], [166.84375, -0.45703125]], "lastCommittedPoint": null, "startBinding": {"elementId": "E88osxqlUbn8OyKa9BYmj", "focus": 0.05620507899275238, "gap": 23.970703125}, "endBinding": {"elementId": "AMX6j1cfnfFYXp4QCHc0l", "focus": -0.003612407858667884, "gap": 8.942885106027727}, "startArrowhead": null, "endArrowhead": "arrow", "elbowed": false}, {"id": "S7aVqEDxhfKzFFtNGLy87", "type": "diamond", "x": 1416.80859375, "y": 1143.9921875, "width": 208.7578125, "height": 220, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 2, "opacity": 100, "groupIds": [], "frameId": null, "index": "aX", "roundness": {"type": 2}, "seed": 627330052, "version": 320, "versionNonce": 18606724, "isDeleted": false, "boundElements": [{"type": "text", "id": "VF7xofnSNZzyO2wyP3mPw"}, {"id": "vzUT0uB4jP5b8Eh1PTqV7", "type": "arrow"}, {"id": "I4dmoZIKCev7JvzwYxMUx", "type": "arrow"}], "updated": 1747101026474, "link": null, "locked": false}, {"id": "VF7xofnSNZzyO2wyP3mPw", "type": "text", "x": 1479.1780853271484, "y": 1216.4921875, "width": 83.63992309570312, "height": 75, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 2, "opacity": 100, "groupIds": [], "frameId": null, "index": "aY", "roundness": null, "seed": 914631556, "version": 249, "versionNonce": 650377732, "isDeleted": false, "boundElements": [], "updated": 1747101026474, "link": null, "locked": false, "text": "Leverage\nwith\nAIs", "fontSize": 20, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "S7aVqEDxhfKzFFtNGLy87", "originalText": "Leverage\nwith\nAIs", "autoResize": true, "lineHeight": 1.25}, {"id": "vzUT0uB4jP5b8Eh1PTqV7", "type": "arrow", "x": 1413.5928403117935, "y": 1255.7610087525616, "width": 115.15989230279524, "height": 1.20766382887291, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "aZ", "roundness": {"type": 2}, "seed": 1400163644, "version": 136, "versionNonce": 197007748, "isDeleted": false, "boundElements": [], "updated": 1747101026474, "link": null, "locked": false, "points": [[0, 0], [-115.15989230279524, 1.20766382887291]], "lastCommittedPoint": null, "startBinding": {"elementId": "S7aVqEDxhfKzFFtNGLy87", "focus": -0.0051587971470015955, "gap": 9.799393658572033}, "endBinding": {"elementId": "Pir3h6AKEXQpujZzXkcu4", "focus": 0.017448077647632565, "gap": 5.494140625}, "startArrowhead": null, "endArrowhead": "arrow", "elbowed": false}], "appState": {"gridSize": 20, "gridStep": 5, "gridModeEnabled": false, "viewBackgroundColor": "#f8f9fa"}, "files": {}}