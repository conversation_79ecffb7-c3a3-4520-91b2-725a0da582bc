---
type: agent
uuid: 10ddc74c-6682-4709-a053-6783845db5ca
name: DingTalkCodeGen
desc: DingTalk AI Code Generator x Yida LowCode PaaS systems
---

# DingTalkCodeGen

## Goal

> Make the world's best source-code x low-code driven page generation tool.

long term: _AI CodeGen_

- next generation of [Salesforce.com](http://Salesforce.com) in AI era

mid term: _DingTalk CodeGen_

- provide `SDK` level for biz-integration by other business units
- generate next-era UI x BUSINESS_DOMAIN scenarios code

short term: _Yida CodeGen_

- simple long-tailed apps x standalone pages x existed systems page enhancement
- flow x process-driven apps (AI-powered)

core values:

- 代码平权（民主、普惠）的模式（低代码 -> AI Vibe Coding）
- 加速数字化系统建设，加速业务创新（AI 驱动，effective and efficient）

### Values Explanation

- **BREAKING boundary** -> what things can not be done before, but can be done now with AI
  - by skills
  - by roles
- **EFFICIENT boosts** -> improve the productivity of doing things
- **ENRICH abilities** -> enrich the abilities / features / user-experience for long-tailed custom scenarios

## States

| State    | Value               | Description                                    |
| -------- | ------------------- | ---------------------------------------------- |
| 推演阶段 | PMF & DEMO 原型阶段 | 需要尽快上线 online 版本找客户共创并向无招汇报 |

## Abilities

[current-stage]

L0 Production Design

(Local)[production-design](./ai-codegen.design/code-gen.design.md)

(Local)[ideas](./ai-codegen.idea.md)

---

System core abilities:

- [artifact-mode] generate artifacts (simple page) -> C 端（业务线低成本接入，能力供给）
- [custom-page-mode] generate stand-alone custom pages for existed low-code powered systems -> B 端（交付 & 服务商）
- [long-tailed-app-mode] generate long-tailed simple apps (different scale and level) -> B 端（个体创意，代码平权）
- [custom-component-mode] generate stand-alone custom components for existed low-code powered systems -> B 端（交付 & 服务商）

Key & Target Scenarios:

- long tailed mini-apps (vote, survey, etc.)
- data x info -> page (sales, marketing, landing, introduction, info page etc.)
- apps with existed systems page enhancement

Core competitive advantages:

- existed Yida customers, systems and data
- highly scenario-fit based on sophisticated prompting-engineering
- leverage of `Yida` platform abilities → metadata-engine, process, auth, OSS, …
- possible tech-highlights
  - native editing in code and mirror to visual-page edit by user

## Architecture

WIP: L0 technology diagrams

- `app shell` : UI for chat and link with Yida’s platform APIs
- `code-gen-agent`: ReAct architecture support tools, agents call and scheduler
- `llm-services`: open-router / BaiLian platform / dingtalk’s llm

## Genes

### design target

- **effectiveness**: stable, accurate, and effective
- **efficiency**: fast, low-cost, and scalable
- **elegance**: with aha moment; aesthetics, animations, high-quality UI, precised interactions, etc.

### design strategies

- simplicity is beautify

## Elaborations

### Growth Strategies

- for personal of _Arno_: focus on domain of **UI code generation** to match the strict requirements of measuring metrics
- for team of _Yida_: find cooperation with other teams, find PMF customers, and work out well with them

### Traces

Phase 1: 2025.03 ~ 04

one-month start up for MVP of code-gen. Try to give a 6-top long-tailed apps scenarios for stable generation.

---

Phase 2: 2025.05

PMF, and production level cooperate with our KOL, customers, and partners.

### SWOTs

see -> [SWOT Analysis](https://docs.google.com/document/d/1OCTBhZXoAP_Wp6_6rZBDpRI9YGLyU0AAL6jNbVFfCjE/edit?tab=t.0)

S

- deep integration with Yida Platform and DingTalk ecosystem
- visionary AI-centered approach: 'AI Code-Gen' and 'AI Vibe Coding'
- specific scenarios driven and dive deeper into these scenarios to make is good by default
- long vision of 'Code Equality' and 'Code Democratization'
- technologies booster: WIP

W

- PMF stage, face the potential risk of remove from CEO for human-resources efficient reason
- complexity requirements of software engineering, especially for large-scale systems is not easy to achieve
- internal competitors from Alibaba Cloud and DingTalk other teams

O

- low-code marketing growth, ai gen code marketing growth, chinese ai market growth
- strong demand for AI powered BPA and page generation tools in market
- long-tailed apps is always untapped potential in specific verticals

T

- intense competition from other low-code platforms and AI code generation tools globally and locally
- rapid pace of AI innovation and changing landscape
- security, governance, and data privacy concerns in LCNC / AI
- market skepticism or slow adoption for new Entrants in a Crowded market

recommendations:

- enterprise features: security, audits, governance, and compliance, even deploy local off cloud
- focus on the competitive advantages of Yida and DingTalk ecosystem, and make difference
- roll out with customers and partners, and make it a co-creation process

### QAs

Q: What is the current most-focus on stuffs?

- nail the _AI Vibe Coding_ experience
- deep Yida / DingTalk integration
- robust programming & engineering for targeted scenarios
- intensive feedback and iteration with customers and partners
- community seeding
- delivery on _elegancy_

---

Q: What new strengths do you think in products design and features of *UI CodeGen*?

- deeper page type scenario optimization
- tech-stack variations (dynamic tech-stack injection)
- web-container technology (web-c)

---

Q: What aha moments we have to strike our customers?

---

## References

WIP
