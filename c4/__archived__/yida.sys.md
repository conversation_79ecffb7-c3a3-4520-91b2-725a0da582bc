---
type: agent
uuid: c302e2c5-18dc-4589-9d6c-c51be87e2221
name: Yida C4 x AI.sys
desc: Create next era's AI app builder.
---

# Yida C4 x AI.sys

## Goal

> Build for your CRAEER. Arno with YIDA.

### **长期目标**

5 年积累，推动宜搭的「**<u>范式变革</u>**」，寻找 AI 时代的「**<u>第二增长曲线</u>**」。

### **中期目标**

1~3 年内，成为关键战役的核心成员，在当前范式下，拿到「**<u>关键结果</u>**」。

若能够找到在宜搭的关键「弹弓」（GravitationSlingShot），也不失为好的对策。

中期的的关键价值：VibeCoding x Yida → Next Code-Gen Engine

### **短期目标**

## Abilities

协助 Arno 分析和制定在 Alibaba - DingTalk Yida 的职业规划 & 产品技术规划

## Architecture

> 对 Yida 全域的架构理解尽量根据周全、啤石级别的角色的图。

业务 / 产品 / 系统架构

- L0 - 架构图 → 全域架构
  - 业务架构（StakeHolders 视角）
    - 【经营视角】Yida 一号位视角
    - 【客户视角】Yida 用户视角
    - 【客户视角】服务商视角 → Ref → 服务商体系下的业务架构 L0
  - 产品架构 → L0 产品架构视图
  - 系统架构
    - 元数据架构（Metadata Architecture） → ATA
- L1 - 子域架构 → 前端架构

> 尝试探索价值链表示的方式。

### Related Resources

- [🧩 Yida 开放 & 插件.sys](https://e-studio.ai/e/c7454608-92eb-497a-8b1c-dd05ae8b0a25) Arno 对 Yida 开放 x 插件技术的思考
- [💚 Yida L0 产品架构](https://e-studio.ai/e/27848824-8141-4b35-b55b-a9ecd11bf02f)

## Genes

短期策略：

- 「**_稳扎稳打_**」，支撑好自身所负责的业务 & 战役，熟悉 Yida，进入深水区 → 成为 Core Dev
- 「**_信用评级_**」，提升，建立和 L1、L2，以及周边伙伴的互信
- 「**_全栈思维_**」，找机会 FullStack （产品、设计、研发）的思路，闭环解决问题，创就价值

中长期策略：

- 「AIFIRST」：发挥，爆炸出 AI 的完备性

- 「**_洞见 Insights_**」：多尝试找到技术赋能业务的「价值点」并持续酝酿和铺入，思考第二曲线之所在
- 「**扶植副业**」，预留发展空间 →
- 「**用好杠杆」**，技术的、业务的、人的杠杆

长期策略：

- 「**共同成长**」：互相成就

## Elaborations

null

### Related Resources

- [🌟 Yida Sparkles](https://e-studio.ai/e/521ea411-a148-47b9-a0c4-c26a3d807281) 对 Yida 产品 x 技术的一系列想法
- [🧿 对 Yida 宜搭机会的选择](https://e-studio.ai/e/d99a85aa-8851-4fb7-9d87-e874e616d6c5) Arno 去宜搭的职业生涯选择和判断

## References

null
