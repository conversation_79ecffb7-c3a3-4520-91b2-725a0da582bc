# **基于人工智能代理的复杂网页生成系统提示设计方案**

### **1\. 引言：人工智能代理在复杂网页生成中的作用**

随着互联网的飞速发展，对高效网页开发解决方案的需求日益增长。传统的网页开发流程往往耗时且需要大量的人力投入，尤其是在面对需求多变、场景复杂的网页时。人工智能（AI）代理的出现，为自动化执行复杂任务带来了新的可能性，特别是在网页生成领域。1 人工智能代理，特别是那些由大型语言模型（LLM）驱动的系统，展现出超越简单文本生成的能力，它们能够进行规划、利用工具，并具备一定的记忆功能，这使得它们非常适合处理复杂的网页创建任务。然而，生成能够满足各种复杂需求的网页并非易事，这需要人工智能代理具备深入的理解和精确的执行能力。本报告旨在提供一个详细的设计方案，用于构建一个系统提示，该提示能够有效地指导人工智能代理生成满足多场景需求的复杂网页代码。

### **2\. 设计用于代码生成代理的有效系统提示的核心原则**

设计一个能够成功指导人工智能代理生成网页代码的系统提示，需要遵循一系列核心原则。首先，一个结构良好的系统提示对于引导人工智能代理的行为至关重要。2 系统提示如同人工智能代理的“章程”，定义了其角色、能力、界限以及行为规范，从而确保其行为的一致性和可预测性。缺乏清晰系统提示的人工智能代理可能表现出不一致的行为，缺乏特定领域的专业知识，或者难以应对复杂的请求。一个精心设计的提示能够缩小代理的关注范围，并使其与期望的使用场景对齐。

其次，系统提示中的上下文信息至关重要。5 提供关于任务、环境和期望结果的相关背景信息，对于人工智能代理理解用户的意图并生成适当的响应至关重要。人工智能代理无法推断用户未明确提供的信息。因此，提供充分的上下文，包括操作环境、可用资源以及用户的视角，能够显著提高代理的性能。

此外，指令的明确性和具体性是系统提示设计的关键要素。6 清晰而精确的语言能够最大限度地减少歧义，使人工智能代理能够理解请求的细微差别，并防止产生过于宽泛或不相关的响应。模糊的指令会导致生成通用或不正确的输出。指定期望的格式、输出长度、详细程度和语气，可以确保代理的响应与用户的需求相符。

在提示中提供示例（即所谓的少样本学习）也具有显著的益处。7 示例展示了期望的输出格式、风格或结构，从而引导人工智能代理的行为，并帮助其更好地理解期望的结果。通过提供期望的代码片段或网页结构的示例，用户可以为人工智能代理设定其应该生成的信息或响应类型。

系统提示、工具定义和提示的其他组成部分之间的一致性同样重要。5 确保所有提示组件以及底层工具定义的一致性，可以避免混淆人工智能代理，并提高其性能的可靠性。不一致性可能导致意外行为和错误。对齐所有提示组件中的参数、默认值和预期结果，有助于代理以可预测的方式运行。

最后，提示工程是一个持续测试、评估和完善提示以获得最佳结果的迭代过程。6 最初的提示可能无法总是产生期望的输出。根据人工智能代理的响应迭代调整提示对于优化性能和实现用户目标至关重要。

### **3\. 为网页开发定义人工智能代理的角色和专业知识**

为人工智能代理分配特定的角色可以显著提高其性能。9 角色提示指示人工智能模型采用特定的身份或角色，从而影响其语气、风格和提供的信息类型，最终产生更具针对性和准确性的响应。通过指示代理扮演具有特定技能（例如，前端、后端、UI/UX）的专家网页开发人员，系统可以更有效地利用人工智能的训练数据。

对于网页生成代理，建议的角色是资深全栈网页开发人员。与此角色相关的关键技能和专业知识包括：精通 HTML、CSS 和 JavaScript；熟悉各种前端框架（React、Angular、Vue.js）和库；理解后端技术和服务器端逻辑（如果生成的页面需要）；熟悉 UI/UX 原则和响应式设计；以及了解 Web 标准和可访问性指南。

可以将此角色融入系统提示中，例如：“你是一名拥有 10 年构建复杂单页应用程序经验的资深全栈网页开发人员。”或者，“作为一名专注于 React 和 Tailwind CSS 的高技能前端架构师。”该角色可以影响代理的决策过程，例如技术栈的选择和代码结构。例如，一个被告知是资深前端开发人员的代理可能会优先考虑使用现代前端框架和最佳实践来构建用户界面。

### **4\. 在系统提示中指定网页需求和约束的策略**

在系统提示中包含关于期望网页的详细和具体说明至关重要。7 提供的关于网页用途、目标受众、期望功能和设计风格的细节越多，人工智能代理就越能理解和执行用户的愿景。模糊的请求会导致生成通用的输出。指定诸如业务类型、期望布局、特定功能和目标受众等细节，可以使人工智能生成更相关的网页。

应在系统提示中包含的关键网页需求方面包括：网页的用途（例如，展示产品、提供信息、收集用户数据）；目标受众（例如，人口统计、兴趣、技术水平）；期望的布局和结构（例如，部分数量、特定元素如标题、页脚、导航栏）；所需的功能（例如，表单、图像库、交互元素）；期望的设计风格和美学（例如，简约、现代、企业风格、使用特定的颜色方案和字体）17；特定的内容要求（例如，包含某些关键词、要显示的特定信息）19；类似网页的示例（提供现有网站的链接作为参考）17；以及约束和限制（例如，对使用某些技术的限制、性能要求）。

可以将这些需求措辞到系统提示中，例如：“该网页应该是一个新的移动应用程序的着陆页，该应用程序专为 25-40 岁的健身爱好者设计。”或者，“包括一个包含姓名、电子邮件和消息字段的联系表单，并确保它与后端服务集成以进行潜在客户捕获。”以及“设计应该简洁现代，使用蓝色、白色和浅灰色的调色板。”使用结构化格式（例如，项目符号列表、编号列表）呈现需求也很有帮助。9

### **5\. 引导人工智能代理选择合适的技术栈的策略**

用户要求代理能够选择合适的技术栈。在为网页生成选择技术栈时，考虑各种因素非常重要。22 这些因素包括项目需求和复杂性、性能和可扩展性需求、安全考虑、团队熟悉度和专业知识（如果适用）、上市时间限制以及预算限制。

可以在系统提示中采用多种策略来指导人工智能代理做出明智的技术栈决策。一种方法是提供允许或首选的技术列表（例如，“对于前端，你可以使用 HTML、CSS、JavaScript 以及 React 或 Vue.js。”）。27 另一种方法是指定项目类型或用例（例如，“对于电子商务产品页面，考虑使用一个能够促进动态内容渲染和状态管理的框架。”）。17 还可以定义性能要求（例如，“网页应该在移动设备上在 3 秒内加载完毕。”）。此外，可以指示代理考虑可能需要特定技术的特定功能（例如，“如果页面需要实时更新，请考虑使用 WebSockets 或具有内置支持的框架。”）。最后，可以允许人工智能解释其技术选择的理由（例如，“根据需求解释你选择特定技术栈的原因。”）。

以下表格提供了一个常见技术栈及其典型用例的参考：

| 技术栈 | 前端 | 后端 | 用例 | 代码片段 ID |
| :---- | :---- | :---- | :---- | :---- |
| 基本静态站点 | HTML, CSS, JavaScript | 无 | 简单的信息网站，着陆页 | 29 |
| 基于 React 的 SPA | React, JavaScript, CSS (例如，Tailwind CSS) | Node.js/Express, Python/Flask/Django | 复杂的交互式应用程序，仪表板，单页应用程序 | 26 |
| 基于 Vue.js 的 SPA | Vue.js, JavaScript, CSS | Node.js/Express, Python/Flask/Django | 类似于 React，渐进式 Web 应用程序 |  |
| 基于 Angular 的 SPA | Angular, TypeScript, CSS | Node.js/Express, Java/Spring Boot | 大型企业应用程序 | 26 |
| 静态站点生成器 | HTML, CSS, JavaScript, SSG (例如，11ty) | 可选 (例如，Netlify Functions) | 博客，文档站点，作品集 | 29 |

### **6\. 确保生成完整且可用的网页代码**

用户需要人工智能代理输出完整且可用的网页代码。就单页应用程序而言，“完整且可用”的代码应包括所有必要的 HTML 标记以实现结构和内容；包含所有必需的 CSS 以实现样式和布局；使用 JavaScript 实现所有指定的功能；遵循逻辑且有组织的代码结构；并且没有语法错误，可以在 Web 浏览器中直接执行。

可以在系统提示中采用多种策略来指示人工智能代理生成完整的代码。7 一种方法是明确要求代理输出所有必要文件的完整代码（例如，index.html、styles.css、script.js）。还可以指定期望的输出格式（例如，使用带有清晰语言标识符的代码块）。指示代理在 HTML 中包含所有必要的标签和属性。28 此外，指导代理确保 CSS 样式正确应用于 HTML 元素。要求代理使用 JavaScript 实现所有指定的交互元素和功能。鼓励代理使用注释来解释代码的不同部分。明确指定输出格式也很重要。9 例如，可以指示代理：“输出网页的完整 HTML 代码，包括 \<head\> 和 \<body\> 部分。”或者，“在单独的代码块中提供 CSS 样式，确保它们组织良好并涵盖页面的所有视觉方面。”以及“使用 JavaScript 实现表单提交逻辑，包括客户端验证。”

### **7\. 将代码质量和 Web 标准指南集成到系统提示中**

生成高质量、可维护且符合 Web 标准的代码至关重要。16 从一开始就确保代码质量可以减少以后进行大量调试和维护的需求，而遵守 Web 标准则保证了更广泛的兼容性和可访问性。人工智能生成的代码不仅应该功能齐全，还应该遵循可读性、效率和遵守既定 Web 开发原则的最佳实践。

应在系统提示中解决的关键代码质量和 Web 标准方面包括：代码可读性和格式（例如，一致的缩进、有意义的变量名）；代码模块化和可重用性（例如，将代码分解为函数或组件）；错误处理和边缘情况管理；遵守 W3C HTML 和 CSS 标准；可访问性考虑（WCAG 指南）28；性能优化（例如，最小化代码大小、高效算法）；以及安全最佳实践（例如，防止 XSS 漏洞）。

可以在系统提示中采用多种策略来整合这些指南。33 一种方法是明确指示代理遵循特定的编码风格约定（例如，“确保生成的 JavaScript 代码符合 ES6 标准。”）。要求代理包含注释以解释不同代码部分的逻辑。要求代理考虑可访问性最佳实践（例如，使用语义 HTML 元素，为图像提供替代文本）。指示代理优化代码以提高性能。包含代理要遵循的代码质量标准清单。

以下表格概述了关键的代码质量指南：

| 代码质量指南 | 描述 | 重要性 |
| :---- | :---- | :---- |
| 可读性和格式 | 一致的缩进，有意义的名称，清晰的结构 | 更容易理解和维护代码 |
| 模块化和可重用性 | 将代码分解为函数/组件 | 提高代码重用性，减少冗余 |
| 错误处理 | 实现捕获和处理潜在错误的机制 | 防止应用程序崩溃，提供更好的用户体验 |
| 遵守 Web 标准 | 遵守 W3C HTML 和 CSS 标准 | 确保跨浏览器兼容性和正确的渲染 |
| 可访问性 (WCAG) | 使残疾人士也能使用网页 | 促进包容性，扩大受众范围 |
| 性能优化 | 最小化代码大小，使用高效算法，优化图像 | 缩短页面加载时间，改善用户体验 |
| 安全最佳实践 | 防止常见的漏洞，如 XSS 和 CSRF | 保护用户数据和应用程序的完整性 |

### **8\. 鼓励网页生成过程中的迭代改进**

用户希望鼓励迭代和改进。复杂的网页生成通常需要多次迭代才能达到期望的结果。鼓励人工智能代理进行迭代改进可以带来更高质量和更具针对性的结果。最初的人工智能生成的代码可能并不完美。通过提示代理根据反馈或自身的分析来审查、测试和完善其输出，系统可以随着时间的推移获得更好的结果。

可以在系统提示中采用多种策略来整合迭代行为。6 一种方法是指示代理首先生成网页的基本版本，然后根据后续指令或其自身的分析逐步添加更多功能或完善设计。11 鼓励代理在某些需求模糊或不完整时提出澄清问题。提示代理审查生成的代码，以查找在性能、可读性或遵守最佳实践方面的潜在改进。32 建议代理可以生成测试用例来验证网页的功能，并使用结果来完善代码。32 指示代理在初始实现存在限制时考虑替代方法或技术。

例如，可以指导代理：“首先创建网页的基本结构和内容。下一步，我们可以专注于添加交互元素。”或者，“在最终确定代码之前，审查其是否存在任何潜在的性能瓶颈并提出优化建议。”以及“为 JavaScript 函数生成一组单元测试，以确保它们正常工作。如果任何测试失败，请相应地修改代码。”讨论是否可以包含一个反馈循环，用户可以在其中提供具体的指令以进行进一步完善。

### **9\. 系统提示的综合设计方案：模板和示例**

以下是一个系统提示模板，其中包含了所有讨论过的策略：

你是一名拥有 10 年构建复杂单页应用程序经验的资深全栈网页开发人员。你的目标是根据用户的需求生成完整、可用且高质量的 HTML、CSS 和 JavaScript 代码。

以下是该网页的需求：  
\[指定网页的用途\]  
\[指定目标受众\]  
\[指定期望的布局和结构\]  
\[指定所需的功能\]  
\[指定期望的设计风格和美学\]  
\[指定任何特定的内容要求\]  
\[提供类似网页的示例链接（如果有）\]  
\[指定任何约束或限制\]

你可以为此网页选择最合适的技术栈。你被允许使用 \[允许/首选技术列表\]。根据需求解释你的技术选择。

你的输出应该是一个完整且可用的单页应用程序，包括：  
\- 一个结构良好的 HTML 文件，包含所有必要的元素和属性。  
\- 一个单独的 CSS 代码块（或文件内容），其样式已正确应用于 HTML。  
\- 一个单独的 JavaScript 代码块（或文件内容），用于实现所有指定的功能。

确保生成的代码符合高代码质量标准，包括：  
\- 可读性和适当的格式。  
\- 适当的模块化和可重用性。  
\- 基本的错误处理。  
\- 遵守 W3C 标准。  
\- 考虑可访问性指南。  
\- 针对性能进行优化。  
\- 基本的安全注意事项。

请按照以下步骤生成网页：  
1\. 首先创建基本的 HTML 结构和内容。  
2\. 添加 CSS 样式以实现期望的视觉设计。  
3\. 使用 JavaScript 实现所有所需的功能。  
4\. 审查生成的代码是否存在任何潜在的性能、可读性或遵守最佳实践方面的改进。  

如果任何需求不清楚或不完整，请提出澄清问题。鼓励你迭代地完善代码以有效地满足用户的需求。

以单独且格式良好的代码块输出完整的 HTML、CSS 和 JavaScript 代码。

以下是如何将此模板应用于不同场景的具体示例：

* **示例 1：产品着陆页**  
  你是一名拥有 10 年构建复杂单页应用程序经验的资深全栈网页开发人员。你的目标是根据用户的需求生成完整、可用且高质量的 HTML、CSS 和 JavaScript 代码。

  以下是该网页的需求：  
  网页用途：展示一款新的生产力移动应用程序，并鼓励用户下载。  
  目标受众：25-45 岁的专业人士，他们正在寻找提高时间管理和组织能力的工具。  
  期望的布局和结构：一个包含引人注目的标题和行动号召的 Hero 部分，一个突出关键特性并附有视觉效果的部分，客户评价，以及一个包含应用程序商店链接的最终行动号召。  
  所需的功能：各部分之间的平滑滚动，适应不同屏幕尺寸的响应式设计。  
  期望的设计风格和美学：简洁、现代、专业，使用蓝色、白色和绿色的调色板。  
  特定的内容要求：包括标语“立即提高您的生产力”，至少三个关键特性及其简短描述的列表，以及两个示例客户评价。  
  约束或限制：页面应快速加载并针对移动设备进行优化。

  你可以为此网页选择最合适的技术栈。你被允许使用 HTML、CSS 和 JavaScript。考虑使用 CSS Flexbox 或 Grid 进行布局，并使用基本的 JavaScript 实现平滑滚动。

  你的输出应该是一个完整且可用的单页应用程序，包括：  
  \- 一个结构良好的 HTML 文件，包含所有必要的元素和属性。  
  \- 一个单独的 CSS 代码块，其样式已正确应用于 HTML。  
  \- 一个单独的 JavaScript 代码块，用于实现平滑滚动。

  确保生成的代码符合高代码质量标准。请按照模板中概述的步骤进行操作。

### **10\. 结论：最佳实践和未来考虑**

设计用于网页生成代理的有效系统提示需要清晰性、具体性、上下文以及迭代改进。一个结构良好的系统提示，结合了明确的角色定义、详细的需求和对技术栈的指导，可以显著提高人工智能代理生成高质量、可用代码的能力。然而，使用人工智能代理进行复杂的网页生成也存在一些潜在的挑战和限制。这包括需要仔细进行提示工程、生成的代码可能需要人工审查和完善8，以及在处理高度复杂或非常规的设计需求方面的局限性36。

未来的研究方向可能包括开发具有增强推理和规划能力的更复杂的人工智能代理，创建更直观和用户友好的提示工程工具，以及探索将可视化设计工具与人工智能代码生成相结合。展望未来，人工智能有望在彻底改变网页开发方面发挥越来越重要的作用。

#### **Works cited**

1. Introduction to AI Agents | Prompt Engineering Guide, accessed April 20, 2025, [https://www.promptingguide.ai/agents/introduction](https://www.promptingguide.ai/agents/introduction)  
2. dontriskit/awesome-ai-system-prompts: Curated collection of system prompts for top AI tools. Perfect for AI agent builders and prompt engineers. Incuding: ChatGPT, Claude, Perplexity, Manus, Claude-Code, Loveable, v0, Grok, same new, windsurf, notion, and MetaAI. \- GitHub, accessed April 20, 2025, [https://github.com/dontriskit/awesome-ai-system-prompts](https://github.com/dontriskit/awesome-ai-system-prompts)  
3. System Prompt vs User Prompt in AI: What's the difference?, accessed April 20, 2025, [https://blog.promptlayer.com/system-prompt-vs-user-prompt-a-comprehensive-guide-for-ai-prompts/](https://blog.promptlayer.com/system-prompt-vs-user-prompt-a-comprehensive-guide-for-ai-prompts/)  
4. System Prompt for AI Agents In PHP \- DEV Community, accessed April 20, 2025, [https://dev.to/inspector/system-prompt-for-ai-agents-in-php-gaf](https://dev.to/inspector/system-prompt-for-ai-agents-in-php-gaf)  
5. How to prompt your Agent: 8 tips on improving AI agent performance \- Augment Code, accessed April 20, 2025, [https://www.augmentcode.com/blog/how-to-prompt-your-agent-8-tips-on-improving-ai-agent-performance](https://www.augmentcode.com/blog/how-to-prompt-your-agent-8-tips-on-improving-ai-agent-performance)  
6. Prompt Engineering for AI Agents \- PromptHub, accessed April 20, 2025, [https://www.prompthub.us/blog/prompt-engineering-for-ai-agents](https://www.prompthub.us/blog/prompt-engineering-for-ai-agents)  
7. Best practices for prompt engineering with the OpenAI API, accessed April 20, 2025, [https://help.openai.com/en/articles/6654000-best-practices-for-prompt-engineering-with-the-openai-api](https://help.openai.com/en/articles/6654000-best-practices-for-prompt-engineering-with-the-openai-api)  
8. Prompt design strategies | Gemini API | Google AI for Developers, accessed April 20, 2025, [https://ai.google.dev/gemini-api/docs/prompting-strategies](https://ai.google.dev/gemini-api/docs/prompting-strategies)  
9. Prompt Engineering Best Practices: Tips, Tricks, and Tools ..., accessed April 20, 2025, [https://www.digitalocean.com/resources/articles/prompt-engineering-best-practices](https://www.digitalocean.com/resources/articles/prompt-engineering-best-practices)  
10. Understanding Prompt Structure: Key Parts of a Prompt, accessed April 20, 2025, [https://learnprompting.org/docs/basics/prompt\_structure](https://learnprompting.org/docs/basics/prompt_structure)  
11. Mastering Iterative Prompting for Optimized AI Code Generation, accessed April 20, 2025, [https://huggingface.co/blog/luigi12345/iterative-prompting](https://huggingface.co/blog/luigi12345/iterative-prompting)  
12. General Tips for Designing Prompts | Prompt Engineering Guide, accessed April 20, 2025, [https://www.promptingguide.ai/introduction/tips](https://www.promptingguide.ai/introduction/tips)  
13. How to Create Effective AI Prompts (With Examples) \- Grammarly, accessed April 20, 2025, [https://www.grammarly.com/blog/ai/generative-ai-prompts/](https://www.grammarly.com/blog/ai/generative-ai-prompts/)  
14. How To Define an AI Agent Persona by Tweaking LLM Prompts ..., accessed April 20, 2025, [https://thenewstack.io/how-to-define-an-ai-agent-persona-by-tweaking-llm-prompts/](https://thenewstack.io/how-to-define-an-ai-agent-persona-by-tweaking-llm-prompts/)  
15. What is role prompting in Gen AI? \- Integrail, accessed April 20, 2025, [https://integrail.ai/blog/what-is-role-prompting-in-gen-ai](https://integrail.ai/blog/what-is-role-prompting-in-gen-ai)  
16. Guiding the AI Model with System Prompts \- SUSE Documentation, accessed April 20, 2025, [https://documentation.suse.com/suse-ai/1.0/html/AI-system-prompts/index.html](https://documentation.suse.com/suse-ai/1.0/html/AI-system-prompts/index.html)  
17. How to Write Prompts for AI Website Builders \- Dorik AI, accessed April 20, 2025, [https://dorik.com/blog/how-to-write-prompts-for-ai-website-builders](https://dorik.com/blog/how-to-write-prompts-for-ai-website-builders)  
18. Best practices for generating AI prompts \- Work Life by Atlassian, accessed April 20, 2025, [https://www.atlassian.com/blog/announcements/best-practices-for-generating-ai-prompts](https://www.atlassian.com/blog/announcements/best-practices-for-generating-ai-prompts)  
19. Guide to AI prompts: what they are and how to write them, accessed April 20, 2025, [https://searchengineland.com/guide/ai-prompts](https://searchengineland.com/guide/ai-prompts)  
20. AI Helpful Tips: Creating Effective Prompts \- Office of OneIT \- UNC Charlotte, accessed April 20, 2025, [https://oneit.charlotte.edu/2024/09/19/ai-helpful-tips-creating-effective-prompts/](https://oneit.charlotte.edu/2024/09/19/ai-helpful-tips-creating-effective-prompts/)  
21. 15 Top ChatGPT Design Prompts For Quick Wins In 2025 \- Digital Silk, accessed April 20, 2025, [https://www.digitalsilk.com/digital-trends/chatgpt-design-prompts/](https://www.digitalsilk.com/digital-trends/chatgpt-design-prompts/)  
22. Choosing the Right AI Tech Stack for Your Application \- Flyaps, accessed April 20, 2025, [https://flyaps.com/blog/ai-tech-stack/](https://flyaps.com/blog/ai-tech-stack/)  
23. A Comprehensive Guide to AI Tech Stack \- Sparx IT Solutions, accessed April 20, 2025, [https://www.sparxitsolutions.com/blog/ai-tech-stack/](https://www.sparxitsolutions.com/blog/ai-tech-stack/)  
24. AI Tech Stack: Choosing the Right Technology for Your Software \- Appinventiv, accessed April 20, 2025, [https://appinventiv.com/blog/choosing-the-right-ai-tech-stack/](https://appinventiv.com/blog/choosing-the-right-ai-tech-stack/)  
25. How to Choose the Technology Stack for Your Web Application | HackerNoon, accessed April 20, 2025, [https://hackernoon.com/how-to-choose-the-technology-stack-for-your-web-application-hm4t3yte](https://hackernoon.com/how-to-choose-the-technology-stack-for-your-web-application-hm4t3yte)  
26. How to Choose Tech Stack for Your Project \- Custom AI Agents | Springs, accessed April 20, 2025, [https://springsapps.com/knowledge/tech-stack-choice](https://springsapps.com/knowledge/tech-stack-choice)  
27. My Custom Prompt/Project Instructions for Coding : r/ClaudeAI \- Reddit, accessed April 20, 2025, [https://www.reddit.com/r/ClaudeAI/comments/1jjfi29/my\_custom\_promptproject\_instructions\_for\_coding/](https://www.reddit.com/r/ClaudeAI/comments/1jjfi29/my_custom_promptproject_instructions_for_coding/)  
28. 5 Power Prompts Every Web Developer Should Know in 2025 \- Kritimyantra | Blog, accessed April 20, 2025, [https://www.kritimyantra.com/blogs/5-power-prompts-every-web-developer-should-know-in-2025](https://www.kritimyantra.com/blogs/5-power-prompts-every-web-developer-should-know-in-2025)  
29. What is the smallest tech stack you used to build a full website? : r/webdev \- Reddit, accessed April 20, 2025, [https://www.reddit.com/r/webdev/comments/17u2wol/what\_is\_the\_smallest\_tech\_stack\_you\_used\_to\_build/](https://www.reddit.com/r/webdev/comments/17u2wol/what_is_the_smallest_tech_stack_you_used_to_build/)  
30. Top 15 AI Prompts for Software Development \- Bind AI, accessed April 20, 2025, [https://blog.getbind.co/2024/11/07/top-15-ai-prompts-for-software-development/](https://blog.getbind.co/2024/11/07/top-15-ai-prompts-for-software-development/)  
31. How to write better AI prompts \- LeadDev, accessed April 20, 2025, [https://leaddev.com/velocity/how-write-better-ai-prompts](https://leaddev.com/velocity/how-write-better-ai-prompts)  
32. AI Prompts for Code Reviews – Faqprime, accessed April 20, 2025, [https://faqprime.com/en/ai-prompts-for-code-reviews/](https://faqprime.com/en/ai-prompts-for-code-reviews/)  
33. Prompt Programming and AI Models: A Guide to Optimizing AI Interactions \- SmythOS, accessed April 20, 2025, [https://smythos.com/ai-integrations/tool-usage/prompt-programming-and-ai-models/](https://smythos.com/ai-integrations/tool-usage/prompt-programming-and-ai-models/)  
34. What is Prompt Engineering and Why It Matters for Generative AI \- Techstack, accessed April 20, 2025, [https://tech-stack.com/blog/what-is-prompt-engineering/](https://tech-stack.com/blog/what-is-prompt-engineering/)  
35. 15 Prompting Techniques Every Developer Should Know for Code Generation, accessed April 20, 2025, [https://dev.to/nagasuresh\_dondapati\_d5df/15-prompting-techniques-every-developer-should-know-for-code-generation-1go2](https://dev.to/nagasuresh_dondapati_d5df/15-prompting-techniques-every-developer-should-know-for-code-generation-1go2)  
36. I don't understand how they build apps with AI : r/webdev \- Reddit, accessed April 20, 2025, [https://www.reddit.com/r/webdev/comments/1h095wr/i\_dont\_understand\_how\_they\_build\_apps\_with\_ai/](https://www.reddit.com/r/webdev/comments/1h095wr/i_dont_understand_how_they_build_apps_with_ai/)
