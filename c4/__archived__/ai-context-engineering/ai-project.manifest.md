# Project Manifest

## Project Overview

> Write the project basic information here

* production goal / values
* production features

## Project Tech Stack

> Write the project tech stack here

* tech1: brief introduction
* tech2: brief introduction

@package.json

## Dev Notice

> Write the dev notice here

* dev1: brief introduction
* dev2: brief introduction

## Project Structure

> Write the project structure here

* `src/`

## Rules & Design Principles

> Write the rules and design principles here

@quote-architecture-guide

* rule1: brief introduction
* rule2: brief introduction


# Example

```markdown
# Project Manifest

## Project Overview

This project is named `Yida Plugin Platform`

It is a fron-end project with SPA technology, which finally generate CDN js to run client render.

* it supports the plugin development of Yida

## Project Tech Stack

It is `react` powered.

The following is the tech info.

@package.json
@yida.config.json

* `@ali/deep` is the alias to Alibaba's Fusion Design UI Library, you can use Fusion Design's Components API here to build UI.
* `@ali/yc-utils` is the utils library provided by Yida's FE team, you can cheek the APIs declartion in `node_modules`.
* `@ali/yida-ui` is the UI libs of Yida's FE team, some of customized UI can be refed and used here.

---

* use `zustand` to manage state (REQUIRED)
* use `ahooks` to reuseful hooks
* use `loadash`, `classnames`, `moment`

## Dev Notice

### Project guide

* `src/pages/market` marketplace page for plugins to display / install similar to app store
* `src/pages/plugin` manage and development process for Yida Plugin
  * `/dev`: dev route for plugin development, contains FE, backend dev of Yida Plugin

### I18n

* just use chinese words in source code we shall use tools to transform them into `i18n` friendly content, do not output $i18n() code

## Project Structure

* `src/api`: OAPI Spec compiled typescript client files. **Make sure to use those APIs** to request the remote if the API is registered here.
* `scr/components`: BizComponents Here

## Rules & Design Principles

* react best practice: @react.md
```