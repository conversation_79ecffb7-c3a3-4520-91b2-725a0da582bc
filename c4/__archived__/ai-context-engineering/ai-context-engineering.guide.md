# AI Context Engineering Guide

## Basic Rules

* `.context` folder -> submodules for each context
* `.cursor` folder -> quote the content from `.context` folder

## Context Engineering

* `prompts`: typical prompts reference
* `examples`: few shots examples
  * `code`: code examples
  * `types`: types declaration examples or interfaces declaration examples
* `docs`: docs reference
* `architecture`: architecture reference /guide
* `projects`: projects reference for sub-apps or sub-modules

## MCP

* use MCP for your private tools usage, try to encapsulate your tools into MCP server and provide extra context for coding related things to Cursor.
* example MCP server can be found in [awesome-mcp-server](https://mcp.so/servers) and some github awesome lists.

### Yida's Context

> Try to provide those context for AI to learn and use.

* AI + SDK：Setter  支持联动、条件隐藏、配置项分组、配置项嵌套、单例、设计器判断等
* 适配移动端的一些模式：darkQuery / mobileQuery 以及移动端差异化样式表达 .vc-page-mobile 补充该结构的上下文
* 补充 ServiceInvocation 的 Case 基于 Fuzzy Search 组件的实现：两种组件复用代码的混沌、表单默认 Setters 和属性的适配、部分 onChange 以及 field 的特殊适配（值、筛选、元数据覆盖等等，需要独立清晰的 case 让 AI 研习），serviceInvoke 等等，问题很多
