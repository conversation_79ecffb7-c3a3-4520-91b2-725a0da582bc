# AI Yida x Private Deployment

## Background

* targets, other potential clients -> 100W contract
* project status: front-end and back-end workloads -> 520 / 530 project iteration for delivery
* full-stack project architecture -> Dify(Python) x React New Project
* front-end architecture -> <PERSON><PERSON> in charge of *Chat Module* integration
* opportunities of code-gen and AI -> YES

## implement strategy

* quick iteration, not for quality and maintainability, focus on delivering features quickly
* ai generated code first, then refactor, and do the other important jobs for `AI Code Gen` and `EStudio`
* everyday feature list need to be provided

- [x] [yida-private] upgrade the code scanfold to `rsbuild` for dev experience

---

modules in charge:

* [chat] basic chat module
* [agent-editing] agent editing module

- [x] [yida-private] scan the yida private projects and find your role in parts
- [x] [yida-private] upgrade the basic std tech-stack and tools for the private project of FE
- [x] [yida-private] chat module and ai module upgrade for better management and stability
  - chat module
  - agent editing / detail module

- [x] [yida-private] editing agent optimization and bugfix according to the demands of 520 requirements -> MVP done
  - [x] zustand store refactor
  - [x] ui-style ai driven refactor
  - [x] agent info editing
  - [x] terms logic support @YouZe

---

be the passive follower. - 2025-05-19

## trace

short term to support, but long term to other scrum teams.