# AI Print Design

## Question FIRST

Q: How print template is formed in server? `ejs` or `html`, how server is rendering?

HTML template + variable values

---

Q: How to ensure the quality and stability of template generation?

cooperate with QA team to ensure the quality and stability of template generation.

---

Q: Style variants ensure the print result is good-looking?

* **20** most-used client styles
* **10** designer driven styles for different scenarios

---

Q: For chat / preview feature, how to use ai-driven print feature?

[PRD](https://mgdone.alibaba-inc.com/file/156496146758799?page_id=M&shareId=c06456f1-d82a-4924-95bb-b411faadbe69&devMode=true&layer_id=1:32353)

* user-oriented design -> ordinary
* just use LUI to create and chat with the AI to generate the print template

---

Q: How to make user try this feature by using the `plugin` mechanism to respond and react quickly in faster iterations?

* AI Canvas is plugin-based architecture

---

Q: How to collaborate with the related teams to ensure the print feature is working well?

* @YanXin
  * prepare the print prompt basic structure
* @Arno
  * add new PrintCodeGen in CodeGen project
* @XiaYang

---


## Solution & Design

PROBLEM -> HSF rendering service is not supported the browser CSS runtime, causing the print result is not good-looking or the print content is shown correctly.