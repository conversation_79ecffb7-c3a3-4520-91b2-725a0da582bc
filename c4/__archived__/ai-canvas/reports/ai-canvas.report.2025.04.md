Final Report -> https://alidocs.dingtalk.com/i/nodes/R1zknDm0WR6XzZ4LtRQyklLlWBQEx5rG

## 我们做了什么？

宜搭中小客户的共性：「中低预算」、「存在个性化诉求」、「技术能力较弱」。

页面/应用级别的代码生成（Yida Code Agent），并做了 MVP 的场景与能力验证。

目前具备的能力：

* 三周左右的实验性研发，基于 Yida 平台能力，使用自然语言快速生成页面
* 支持以 Tool / MCP Server 的方式被钉钉的类 Manus 或 DingTalk Agent 作为技能 / ToolCall 调用，在 Agent 工具加持下，提供更丰富的诸如：图片、搜索结果、文件、媒体资源上下文，以实现更好、更准确的生成效果。

技术特质：

「低成本」

基于 DeepSeekV3Chat 进行工程化深度探索，简单场景仅「浏览器技术」而非依赖 Web Dev Container（服务端容器），速度快，生成成本降低成本到 1/100 集团  OneDay / 商业产品 Loveable 的生成成本（模型价格、生成代码量、生成次数综合效应），目前平均单次价格 0.01 元 / 次。

---

「高效率」

追求提示词工程的极致化，「依赖 Yida aPaaS，一键生成，一键发布」可以公开使用静态页面（平均 2 分钟以内），修改也可以控制在 1 min 以内。

---

「优体验」

「支持模板召回」、「预制风格召回」、「预制场景召回」的能力，生成的页面具备多风格化特质，可以比肩 Claude 3.7 Sonnet 等旗舰模型的 UI 生成能力。

---

## 未来的规划？

核心的编码心智：PROCode -> LOWCode -> VIBECoding -> AIMandatoryCoding

「组件级生成」

联合 组件开放场景 & 诉求，突破复杂的业务场景如 BOM （物料清单管理系统）系统组件等，可以通过本地 x 线上 AI Coding 的方式生成后作为积木进入宜搭在各个场域内消费，让组件成为：数据 x 逻辑 x UI 的产物。让 AI 生产组件的渗透比持续增加。

---

「页面级生成」

单页工程化拆解，适配并行开发（子任务组合拆解），适配多端，适配多风格，适配多场景，做深「体验」和「工程效率」，降低成本，提升效果。

---

「应用级生成」

* 对于复杂的 SaaS 应用级别的源代码 & 低代码组合生成，让 AI 具备应用级别的「生成」以及「二开」能力：复杂的多页面工程拆解、任务调度。
* 做薄低代码，做厚底层能力引擎，由表单驱动、Schema 驱动 -> AI First 的元数据、业务流引擎驱动。
* 「业务 FaaS」， AI 代劳 服务端部分业务逻辑与流程自动化的编写，以 FaaS x 中间件（Yida 能力封装）解决定制化的业务功能，前后一体，无缝生成。

---
