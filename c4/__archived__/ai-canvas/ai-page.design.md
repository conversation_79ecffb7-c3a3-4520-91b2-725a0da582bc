# AI Canvas Composer

## AI Canvas Design Goal

* [primary] Agent output (data + context + user prompt) -> AI Canvas Composer -> Interactive WebPage
* [secondary] Production page is reusable anywhere
* [secondary] Do good for `INFORMATION VISUALIZATION` + `DATA STORYTELLING` + `LIGHT WEIGHT INTERACTIVE TOOL`
* [secondary] On the long run for more Dynamic UI AIGC scenarios (persisted and dynamic UI) -> Data Model Driven UI

Values is described as:

* [primary] `Man<PERSON>` liked UI generative tool
* [secondary] Make AI Canvas fun and easy for user to show their information and spread so fast that it becomes a new trend to guide user interact with AI.
* [secondary] Make AI Canvas integrate with Yida's data / service / product ecosystem

## AI Canvas Design Elaborations

### Product Design

Conceptual Design see `Apple Notes.app/Composable UI`.

* TARGET USER: normal agent user which need `canvas` to better or make full use of AI Result for information display and interactions
* USER JOURNEY: write professional requirements -> `AI Agentic` process to generated pre-requested information -> call `AI Canvas Composer` to generate interactive web page -> use the page as needed

### Key Target Scenarios

> Find the CORE scenarios to do the PMF works of DingTalk AI Assistant

* `Data Visualization` -> Financial Report, Performance Report, etc.
* `Teacher's Teaching Material` -> Interactive diagram and widgets to help teacher to teach and explain the knowledge
* `Resume / Portfolio` -> AI Canvas Composer as a new trend to show user's information
* `Product Introduction` -> AI Canvas Composer as a new trend to show product features and benefits
* `Storytelling` -> AI Canvas Composer as a new trend to tell a story
* `Key Ideas and Diagrams Info-graphics` -> AI Canvas Composer as a new trend to show key ideas and diagrams
* `Small Personal Dedicated Tools` -> Solve small domain problems and generate ui for instant and shareable use cases
* `Headless CMS` -> AI Canvas Composer as a new trend to show headless cms data

ToC thoughts: 金字塔原理、Markdown 卡片生成器（小红书、读书笔记等）、团队 Outing 旅行计划、个性化房贷计算器、一键抽奖系统、宜搭首页构筑

## AI Canvas Technology Guide

### GGenerative UI Tech factors overview

Basic Tech Adoption

* html: `JSX` v.s. `HTML`
* css: `TailwindCSS` v.s. `CSS Native`
* javascript framework: `Lit` (WebComponents) v.s. `React` (VDOM + JSX) v.s. JS Native
* rendering: `Server-side rendering` v.s. `Client-side rendering`

Engineering Complexity

* js modular: `ESM` v.s. `CJS` v.s. `AMD / UMD` v.s `Module Federation`(webpack powered)
* packaging: `Bundler` v.s. `No Bundler`
* artifact organization: `single-file` v.s. `multi-files` v.s. `monorepo / project-structure` (workspace level)

UI / UX Quality

* ui-component library: `MDC` v.s. `AntD` v.s. `RadixUI`

Light Technology Stack Choice

* Light JS = JavaScript + ES Import module
* Light DOM = Simple HTML or HTML light template engine (Server-side rendering | Web Components)
* Light CSS = TailwindCSS (online interpreter CSS)

### Design Principles

* AI Easy to understand context
* AI Easy to output content (highly **abstracted** and in **condensed** form) as **ARTIFACT**
  * CSS abstraction: `TailwindCSS` -> runtime compiler
  * JS / WebComponents abstraction: `Lit` basic powered by Google WebComponents technology
  * ...
* YidaLess + TechLess (No Bundler, No Compiler, No Build, just pure form of HTML, CSS, JS)
* Try **Modular** system: `umd` or `es` or `mfs` standard to do dynamic import functionality modules
  * umd: `react` / `lodash` / `moment` / `antd` / `chart.js` / ...
    * animation lib. / canvas engine
  * esm: `lit` / ...
  * ✅ mf: module-federation runtime loader -> load Yida's federation component module
* Stay FOCUS on `VISUALIZATION` and `LIGHT_INTERACTION` scenarios, pick the most impacted ones to do PMF works

### Core Technology Strategies

> For better experience

* 🆒 support `Progressively` render the generated UI blocks & components
* template driven RAG for 2/8 high-frequent scenarios
  * AI Block is `RAGed` for `aesthetic` and `interactivity` pre-defined blocks
* use agentic skills to instruct AI to expand user's prompt into professional prompt to better use AIGC tools for web-pages / apps
* integrate more high-quality libs and tools for web pages -> Headless CMS is a way out
* engineering optimization to divide a problem into multiple steps: use different tools, agents, models(LLM) to digest

### Prompting Strategies

* [prompting guide via Google DS](./core/ai-page-gen.core.prompt.google.md)

### Browser Powered Bundler Tech Design

[Web Container and Web Console](core/web-container.tech.design.md)

we choose `esbuild-wasm` to do the job of `esbuild` in the browser.

## One Scenario Dive Deeper

> Every scenario actually require precised prepare

### Basic libs. import for basic skills of WebPage Artifacts

basic:

* ✅ `React` -> JSX based for HTML abstraction
* ✅ `AntDesign` -> React Component Library -> `ShadCN` derived community widgets
* ✅ `TailwindCSS` online interpreter -> atomic css (reduce output size and clean style)
* ✅ `Chart.js / ReCharts` -> have better encapsulated one? like AntV family?
  * `AntV` family libs.
* canvas 2d-context APIs abstraction -> Native Canvas v.s. lib encapsulation?
  * ✅ `d3.js` -> math focused diagram rendering or other diagram rendering libs.
* animation libs. to enrich the expression of our Canvas -> what is the best choice for AI to use and understand
  * UI Animation
    * ✅ `Motion.js` -> a production-ready motion library for React
    * `Framer Motion` -> a production-ready motion library for React
    * `anime.js` -> Clear API, good for both simple and complex animations
  * Graphic based Animation -> see Math or Physics chapter
* Visual Effects -> cool / awesome effects
* `Diagram Render` -> PlantUML x ✅ Mermaid
* **3D Rendering Scenario** -> create 3D Material to load / work and in a better way -> this may actually make impactions on the effect.
  * `three.js` -> encapsulation
    * `react-three/fiber` -> react integration
    * ECS Frameworks x `three.js` -> 3d rendering engine
  * `Babylon.js` -> 3d rendering engine
  * online 3d-models or 3d-materials loading for crafting

biz modules / federated modules:

* ✅ `markdown` plugin component -> federated module
* `AMap` plugin component to render and collaborate (server token exchange)
* 🤩 `Landing Page Sections` plugin to abstract production page -> UIPaaS replacement design

### Math Teacher's Teaching Assistant

* ✅ Math Expression Render (LateX)
* MathBox -> <https://github.com/unconed/mathbox?tab=readme-ov-file> -> math visualization tool
* `f(x)` plot and painting
  * `Manim.js` -> <https://github.com/JazonJiao/Manim.js/> + `P5.js` powered math focused diagram animation / rendering
  * `Function Plot` -> math function plot
* `Math.js` x `Plotly.js` -> math function plot
* `JSXGraph` -> math function plot
* `GeoGebra` -> <https://www.geogebra.org/> -> math visualization tool

## Research and Development

> Show the TODO list for the next phase of the project

### Stability

* [ai-canvas] support history check-point and revert to the previous version & rollback
* [ai-canvas] consider to run eslint to avoid the runtime error and auto-fix the error by default -> [eslint-browserify](https://www.npmjs.com/package/eslint-linter-browserify)
* [ai-canvas] more stable `rendering` or `transpile` or `eslint` error to guide ai to fix the error
* [ai-canvas] runtime-error patch support auto-fix **5** or more times when multiple types of errors encountered

### Workspace

> `WebC` to make this fully possible!

* [ui-logic-separation] separate UI and logic into two virtual file for speed up the compile and runtime
* [browser-workspace] use browser to organize multiple files and folders as the workspace and provide tools for ai to operate on `store` / `pages` / `components` and more

### Effects

* [ai-canvas] apply Designer @MiWei's strategy to make the UI more beautiful and professional

### Container Tech

@XiaYang try the MVP for `virtual-workspace` or WebC for files based editing -> [Containers](core/web-container.tech.design.md) x [AI Workspace Page Editing Design](./core/ai-workspace.design.md)

* [tailwind-css] make sure tailwind-css can compiled via dev-server in browser runtime and also via `WebC` to compile the css in the browser runtime

### Aesthetics

* [aesthetics] styles and aesthetics UX for AI to generate @Arno
* [RAG] multiple RAG engineer based on `Aesthetics` to generate the best UI @XiaYang
* [tool-calls] for chain of API invocations to fetch meta-data / create-form and more @XiaYang

### More Components / Plugins Driven UI Elementary Stuffs

* Yida's basic form components [Assets](core/ai-canvas.assets.design.md)
* Yida's Plugin system to support more components and plugins to be used in the UI @Arno

### Business Logic / Flows

@ChenHan & @ZhouQuan & @YueFei to find better production scenarios to do the PMF works.

* [Fullstack_x_UI] `Fullstack & Serverless` to streaming the UI into `THE Canvas` of agentic age.

## Production Research

* [loveable] learn prompts and principles from `loveable` to make the AI-generated code better
* [21-first-century] research on `21-first-century` to make the AI-generated code dev UX friendly
* [ezsite](https://ezsite.ai/) for site-generation
