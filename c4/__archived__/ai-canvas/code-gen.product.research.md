# AI Code Gen App Production Research

## Loveable

2025-04-05 ~ 08

Research dimensions:

Metrics see -> [ai-app.design.md](./ai-app.design.md) Metrics section.

use a unified table to show multiple production effects in both production and tech dimensions.

* production ability boundary -> <https://lovable.dev/> features tab

## Questions guide to think

* how does this do landing page so professional?
  * sectional template?
  * style variants?
  * professional prompts guiding the latest design trends and techniques?
    * glassmorphism
* what is the core scenarios that this product truly good at?
  * landing-page?
  * unified app?
* where is the visual pictures and resources come from?
  * images
  * fonts
  * videos
  * icons
  * ...
* what is the technology UI stack they use?
  * tailwindcss
  * shadcn
  * radix-ui

## Dev In

2025-04-07

* dev-in parallel execution
* focus on simple but dedicated scenarios
* online quotable agent
* ...
