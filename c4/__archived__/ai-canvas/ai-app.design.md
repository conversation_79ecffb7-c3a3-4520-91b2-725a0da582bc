# AI App Design

* [diagram](https://alidocs.dingtalk.com/i/nodes/qnYMoO1rWxrkmoj2IkpPjz69J47Z3je9) for better illustration
* [production comparison](https://alidocs.dingtalk.com/i/nodes/m9bN7RYPWdyrPBREcQn3RbdXVZd1wyK0)

---

## Values

* **BREAKING boundary** -> what things can not be done before, but can be done now with AI
* **ENRICH abilities** -> enrich the abilities / features / user-experience for long-tailed custom scenarios
* **EFFICIENT boosts** -> improve the productivity of doing things

---

## Production / Business

* Personal Assistant Sc<PERSON>rio
* Enterprise Assistant Scenario
* Yida Native App Scenario

> 526 Dingtalk Release Session

---

## Artifacts

* Component (plugin / block) -> [ai-component.design.md](./ai-component.design.md)
* Page (focus ✅) -> [ai-page.design.md](./ai-page.design.md)
* App -> [ai-app.design.md](./ai-app.design.md)

---

## Metrics

* ability-boundary -> boundary of its ability
* accuracy -> accuracy of its ability
* latency -> fast / faster / fastest
* cost -> relatively low cost
* user-experience -> atheistic design / ease of use / smooth experience

---

## Tech Dimensions

* `Agentic` runtime -> Schedule tools and tasks to complete the goal of user's intension
* `Prompt` selector / optimizer -> user's intension accuracy
* `Template` / `Solution` selector -> nice by default
* `TechStack` selector -> DX -> [ai-canvas.design.md](ai-page.design.md) Generative UI Tech factors overview
* `VisualDesignGuide` selector -> UX
* `Tools` selector -> system tools + user tools -> Abilities Enrichment

Compose above elements into an `Agent` flow to generate ai-related things.

---

## Architecture Design

* basic abilities encapsulation
  * agentic runtime / services
  * prompting debugger / manager -> visualize the prompt and its result
  * RAG services
  * llm services
  * tools / MCP services
    * dev resources -> (media-files) images / icon / fonts / videos / audios / etc.
    * related content tools -> problem related content tools -> search / user-data / professional domain tools / knowledge-base
* core code-gen architecture
  * designer / generator runtime -> [Web Container and Web Console](core/web-container.tech.design.md)
    * designer compile / bundler architecture
  * yida runtime -> leverage yida's runtime pre-existed abilities
    * rich and dynamic tech-stack runtime
    * dynamic Yida Assets loader
  * server runtime -> serverless
  * context-engineering -> organize context for AI to understand and generate
    * prompt-engineering: ai-understand-context
    * long-term and short-term memory
  * file edit -> basic re-edit skill
  * workspace edit -> virtual multiple-file workspace skill
  * progressively / parallel-execution -> faster and smoother user experience
* data / logic services architecture
  * metadata encapsulation (server)
  * business rules / logic encapsulation (server)
  * third party services integration (server)

---

## Key Scenarios

[key scenarios](https://alidocs.dingtalk.com/i/nodes/EpGBa2Lm8aZxe5myCoXDP7AnWgN7R35y?corpId=dingd8e1123006514592&iframeQuery=sheetId%3DeIuTLu5%26viewId%3D1RWTqBO&sideCollapsed=true)

* one-page scenario -> find scenarios on [ai-canvas.design.md](ai-page.design.md)
* app scenario -> WIP
  * chat with @JiuShen -> Heavy task / solution scenarios
  * chat with @MeiJi @ShanJin @YiCheng -> CRM, Marketing, Sale, Service, HR, Tasks, Reports, etc.
  * chat with Yida's Service Vendors / Developers / KOL -> about their requirements / feedback / suggestions / etc.

---

## Strategies & Ideas

* provide `Normal` mode and `Expert` mode
  * `Normal` mode -> nice by default 2/8 high-frequency scenarios with RAG and preset complex prompting engineering
  * `Expert` mode -> least pre-set content in prompt and user dominated all generation

---
