## First Principles 深度思考问题集

### 本质层面问题

1. **抽象的本质是什么？** 如果Block的本质是抽象 -> Component -> 完成特定 SaaS 的特定功能的组合。

> 抽象的本质是信息压缩与复杂度管理。Block 作为抽象不仅是功能组合，更是：
>
> * 语义单元：Block 代表了一个完整的业务语义，而非仅是技术实现
> * 知识封装：每个 Block 都是领域知识的物化表达，包含了解决特定问题的智慧
> * 认知边界：Block 定义了人类和 AI 理解系统的边界单元，是双方交流的"通用语言"

2. **代码生成的底层原理是什么？** 高内聚的，可复用的，可组合的，可扩展的，可维护的。符合单一职责原则，符合软件工程的原理。

> 代码生成的本质是从意图到实现的映射转换。更深层次是：
>
> * 语义到语法的转换：将人类表达的意图（模糊、高级）转换为计算机理解的语法（精确、低级）
> * 约束满足问题：在满足多维度约束条件下（性能、可读性、安全性等）寻找最优解
> * 知识蒸馏：将领域专家隐性知识提炼为显性规则和模式

3. **什么是最小可行Block？** 如果将Block简化到极致，它的最小功能单元是什么？这与传统组件有何不同？

* AI Understandable / Composable -> AI 可理解的，AI 可生成的，AI 可维护的。
* Human Understandable -> 人类可理解的，人类可生成的，人类可维护的。

> 最小可行 Block 是同时满足以下条件的单元：
>
> * 自包含性：所有依赖被明确声明且可被满足
> * 目的单一性：解决一个且仅一个特定问题
> * 接口确定性：输入输出边界清晰定义
> * 状态有限性：内部状态可被完全理解和预测
> * 演化潜力：能够在保持接口稳定的前提下内部迭代优化

Q: 如何确保AI生成的Block与人工创建的Block在质量和可维护性上达到同等水平？

### 用户价值问题

4. **为什么用户需要Block而非直接使用代码？** Block提供的核心价值是否超越了代码可读性和维护性？

* 抽象后，认知负荷的降低；知识和经验的复用；降低人机沟通的成本。
* Block 是 Yida 单元化的能力封装，能够更好地被上层的 AI 所理解，并且能够被 AI 所生城和消费。
* 它内聚了基于 Yida 的 API + 功能组合，它是 Yida 的积木。
* 具备生态和网络效应的扩张：Block 越多，组合可能性呈指数级增长，系统整体价值超过部分之和

5. **生成式AI与块状设计的矛盾点在哪里？** AI倾向于连续生成，而Block追求离散组合，如何调和这一矛盾？

* 增加了特定 Yida 业务上下文的 Block
* 适当的限制，让 AI 生成更符合预期
* **找到限制的边界**，是非常重要的

这一矛盾本质是连续流与离散单元的辩证关系，调和方法包括：

* 粒度自适应：允许 AI 在不同复杂度层级自由切换，简单任务整体生成，复杂任务块状组合
* 动态边界：Block 边界不必固定，可根据上下文动态确定
* 渐进式重构：初始连续生成，随后识别模式并重构为离散 Block
* 语义引导生成：用高级语义描述引导 AI 生成符合 Block 范式的代码，而非直接约束生成过程

6. **如果没有技术限制，用户理想中的Block创建体验是什么样的？** 不考虑当前AI能力限制，最理想状态是什么？

* 在复杂度的临界点之前，完全 NL 驱动，通过 AI 来生成 Block 并按照专业性质的需求进行持续的迭代和改良
* 思想具象化：用户只需表达意图，系统自动将其转化为实现
* 上下文自感知：系统自动理解项目历史、领域背景和用户习惯，无需显式说明
* 在复杂度的临界点之后，FullCode + NL 驱动，通过 AI 辅助编码的模式

### 系统架构问题

7. **如果只用五个API设计整个系统，它们会是什么？** 从最精简角度思考系统的核心接口设计。

若只能设计五个 API，它们应该是：

* Intent API：接收用户意图，转换为系统理解的语义表示
* Composition API：定义 Block 之间的组合规则和交互方式
* Block Generation API：管理 Block 内部状态和跨 Block 状态共享
* Context API：提供 Block 运行所需的环境和上下文信息
* Evolution API：支持 Block 的版本管理、迭代优化和兼容性维护

8. **Block之间的"粘合剂"本质是什么？** 使Block能够有机组合的基础机制是什么？是数据流、事件、上下文还是其他？

* 上层 Composer 通过 RuntimeFramework（目前是低代码引擎，未来会非常不同）来驱动 Block 的状态管理和逻辑（复杂度就可以进行控制了）
* React 目前提供了主要的 UI 和渲染能力，通过生态的繁荣，可以快速地进行扩展
* 包括：dataDriven -> props | events | inner-state | outer-state | apis | error protocol

9. **如何实现Block的自我进化？** 若Block能够基于使用情况自我优化，其机制应该是什么？

* 需求驱动的自我模式进化 -> Requirements -> Iteration -> 组件迭代
* 足够原子化，足够小，足够简单，职责和边界足够清晰，才足以驱动未来范式更新之后，存量体系的持续演化，这是非常符合演进式的思维和技术模式

### 创新思路问题

10. **如果没有现有组件库作为参考，AI会如何从零设计组件体系？** 摆脱已有设计模式的限制会带来什么可能？

* 技术无关，是非常重要的驱动模式
* 多模态组件：打破代码与其他形式（如自然语言、图形）的界限
* 自适应粒度：组件大小非固定，根据问题复杂度动态调整

11. **Block与生物系统有何相似之处？** 从生物学中的细胞组织、器官协作中能获得哪些启发？

* 细胞组织 -> 器官协作 -> 系统进化，非常相似的，只有足够多，才可以形成系统，才可以实现量变引发质变的模式

AI Answer:

* 适应性与特化：特定环境中的高度特化 vs 多环境的通用适应
* 共生关系：不同 Block 间形成互利共生，增强整体适应性
* 免疫机制：自动识别和修复系统中的"异常"
* 梯度信号：通过微妙的信号梯度而非硬编码规则引导系统行为
* 代谢网络：资源和信息在系统中的循环流动，形成自我维持的网络

12. **反向思考：用户不需要什么样的Block？** 明确界定不需要AI生成的内容，可能反而更清晰定位产品方向。

* 非常底层粒度的功能 block，应该沉淀为原子化组件能力的 Block，比如：AntDesign 的 Button 组件，或者 Shadcn 的 Button 组件
* 需要大量和周边的模块互动且紧密耦合的 Block， 比如：Yida 的 Form 组件，或者 Shadcn 的 Form 组件，需要吃大量周边配置的组件模式

---

* 过度通用 Block：太过抽象以至于配置成本超过直接实现成本
* 不可理解 Block：内部逻辑过于复杂，无法被人类或 AI 理解和调试
* 高耦合 Block：对特定环境或其他 Block 有强依赖，难以独立使用
* 状态不可控 Block：内部状态无法预测或受控，导致系统行为不可靠
* 价值模糊 Block：难以清晰描述其价值和适用场景的 Block

### 技术边界问题

13. **AI生成的代码与人工代码的本质区别是什么？** 除了生成方式不同，结构和质量上有何根本差异？

* AI 需要解决的最难问题的「恰好正确的 Context」，这是 AI 需要解决的最难的问题，怎么找到对应的领域知识 & 私域知识，这是 AI 需要解决的最难的问题

14. **当前AI理解"好代码"的局限性在哪里？** AI模型对代码质量的理解维度与人类专业开发者有何差距？

* Private Domain Knowledge 和 Public Domain Knowledge 的 Gap，AI 对私域上下文的理解和吸收能力，以及对上下文的自组织收拾能力比较差，long-term 和 short-term 的 Memory 自主规划和记忆能力还是明显有匮乏的
* AI 使用工具的局限性，效率和准确性
* AI Context 窗口的限制，以及调用成本和速度

15. **Block的物理隐喻是什么？** 如果将Block类比为现实世界中的物体，最贴切的类比是什么？这种类比能启发怎样的设计思路？

* 积木，乐高，乐高积木，乐高积木的组合

### 增长路径问题

16. **如何设计Block系统的指数级增长机制？** 遵循什么原则能使系统价值随Block数量增加而呈指数增长而非线性增长？

增长策略：

* 开源 OpenSource -> 代码编写的水位问题
* AI Booster -> AIGC 平地起高楼
* 野蛮发展 -> 早期传播插件的成本低到可以忽略不计
* 商业利益驱动 -> 需要找到驱动大家动手创作 Block 的机制，无论是解决问题的动机还是从生态获取价值之动机 -> 赚钱嘛，不寒碜

---

* 组合价值法则：确保 Block 组合的价值大于各部分之和
* 网络效应：每个新 Block 都增加与现有 Block 的潜在组合可能
* 多维度增长：同时在数量、质量、多样性和连接性上增长
* 降低边际成本：使创建第 n+1 个 Block 的成本低于第 n 个
* 自我增强循环：Block 使用促进改进，改进促进更多使用

17. **Block复杂度与可用性的最佳平衡点在哪？** 复杂度与易用性的权衡曲线是否存在拐点？

* 可复用性越高（一般性），往往意味着复杂度和封装的一般性成本会增高
* 业务的复杂度（领域深度），往往会控制 AI 可以进入协助完成的成本

18. **如果Block系统成为一种新的编程范式，它与现有范式的根本区别是什么？** 它可能如何改变软件开发的本质？

> Block系统与传统组件库的根本区别是什么？是否仅仅是生成方式的不同？

* 【意图驱动】：从意图出发，到实现，再到迭代
* 【组合优先】：通过组合现有 Block 解决问题，而非从头构建
* 【双向翻译】：在人类理解的语义层和机器执行的实现层间自由转换
* 【演化连续性】：代码不是静态产物而是持续演化的有机体
* 【集体智慧】：个体贡献汇聚为集体知识库，服务于整个生态系统

---

* 【参与者视角】：纯人类研发 -> AI 与人类协同研发 -> AI 独立自主研发
* 【创作视角】：AI 辅助创作 -> AI 独立自主创作
* 【能力供应的视角】：纯粹 UI -> 数据 + 逻辑 + UI -> 前后一体的业务逻辑单元

19. **Block系统如何实现"自下而上"的进化？** 从用户需求出发，如何推动系统不断演进？

* 权重系统，为优质的 Block 提供更多的曝光机会以及 Compose 的机会
* 商业 / 创作激励系统，为优质的 Block 提供更多的创作激励
* 模型水位，持续提升 AI 的基础创作能力

---

* 使用数据驱动：收集细粒度使用数据，识别实际需求和模式
* 社区共创：建立贡献者生态，众包 Block 创新和优化
* 变异机制：鼓励 Block 的多样化变体实验
* 自然选择：通过实际使用频率和效果进行优胜劣汰
* 知识提取：从成功 Block 中提炼设计原则，指导生态发展
