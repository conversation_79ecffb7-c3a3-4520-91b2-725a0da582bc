/**
 * Example implementation of an AIBlock for a countdown timer
 * 
 * This demonstrates how to use the AIBlock specification to create a simple block
 */

import { AIBlock, AIBlockType, PropDefinition } from './ai-block.spec';

/**
 * CountdownTimer AIBlock specification
 */
const countdownTimerSpec: AIBlock<CountdownTimerProps, CountdownTimerState> = {
  id: 'countdown-timer',
  name: 'Countdown Timer',
  description: 'A customizable countdown timer that displays time remaining until a target date',
  version: '1.0.0',
  type: AIBlockType.UI,
  tags: ['timer', 'countdown', 'ui', 'time'],
  author: {
    name: '<PERSON><PERSON>',
    organization: 'Yida',
    email: '<EMAIL>'
  },
  license: 'MIT',
  
  // Block input properties
  props: {
    schema: {
      properties: {
        targetDate: {
          type: 'string',
          format: 'date-time',
          description: 'The target date to count down to'
        },
        showDays: {
          type: 'boolean',
          description: 'Whether to show days in the countdown'
        },
        showHours: {
          type: 'boolean',
          description: 'Whether to show hours in the countdown'
        },
        showMinutes: {
          type: 'boolean',
          description: 'Whether to show minutes in the countdown'
        },
        showSeconds: {
          type: 'boolean',
          description: 'Whether to show seconds in the countdown'
        },
        format: {
          type: 'string',
          description: 'Format of the countdown display',
          enum: ['compact', 'full', 'custom'],
          default: 'full'
        },
        customFormat: {
          type: 'string',
          description: 'Custom format string for the countdown (only used when format is "custom")'
        },
        onComplete: {
          type: 'object',
          description: 'Callback function when countdown reaches zero'
        }
      },
      required: ['targetDate']
    },
    defaultValues: {
      targetDate: new Date(Date.now() + 86400000).toISOString(), // 24 hours from now
      showDays: true,
      showHours: true,
      showMinutes: true,
      showSeconds: true,
      format: 'full'
    },
    validation: {
      rules: {
        targetDate: [
          {
            validator: (value) => !!Date.parse(value),
            message: 'Target date must be a valid date string'
          }
        ],
        customFormat: [
          {
            validator: (value) => {
              const format = (countdownTimerSpec.props.defaultValues as any).format;
              return format !== 'custom' || !!value;
            },
            message: 'Custom format is required when format is set to "custom"'
          }
        ]
      }
    }
  },
  
  // Block state management
  state: {
    inner: {
      schema: {
        remainingTime: {
          type: 'object',
          properties: {
            days: { type: 'number' },
            hours: { type: 'number' },
            minutes: { type: 'number' },
            seconds: { type: 'number' },
            total: { type: 'number' }
          }
        },
        intervalId: { type: 'number' },
        isPaused: { type: 'boolean' }
      },
      initialState: {
        remainingTime: { days: 0, hours: 0, minutes: 0, seconds: 0, total: 0 },
        intervalId: 0,
        isPaused: false
      }
    },
    outer: {
      schema: {
        isRunning: { type: 'boolean' },
        timeRemaining: { type: 'number' }
      },
      initialState: {
        isRunning: true,
        timeRemaining: 0
      }
    },
    persistence: {
      storage: 'memory'
    }
  },
  
  // Events
  events: {
    emits: {
      'countdown:complete': {
        description: 'Fired when the countdown reaches zero',
        payload: { type: 'null' }
      },
      'countdown:tick': {
        description: 'Fired on each countdown tick',
        payload: {
          type: 'object',
          properties: {
            remainingTime: {
              type: 'object',
              properties: {
                days: { type: 'number' },
                hours: { type: 'number' },
                minutes: { type: 'number' },
                seconds: { type: 'number' },
                total: { type: 'number' }
              }
            }
          }
        }
      },
      'countdown:start': {
        description: 'Fired when countdown starts',
        payload: { type: 'null' }
      },
      'countdown:pause': {
        description: 'Fired when countdown is paused',
        payload: { type: 'null' }
      },
      'countdown:resume': {
        description: 'Fired when countdown is resumed',
        payload: { type: 'null' }
      },
      'countdown:reset': {
        description: 'Fired when countdown is reset',
        payload: {
          type: 'object',
          properties: {
            targetDate: { type: 'string' }
          }
        }
      }
    },
    listens: {
      'countdown:pause': {
        description: 'Pauses the countdown',
        handler: 'handlePause'
      },
      'countdown:resume': {
        description: 'Resumes the countdown',
        handler: 'handleResume'
      },
      'countdown:reset': {
        description: 'Resets the countdown',
        handler: 'handleReset'
      }
    }
  },
  
  // Public APIs
  apis: {
    methods: {
      start: {
        description: 'Starts the countdown',
        parameters: [],
        returnType: { type: 'null' },
        isAsync: false
      },
      pause: {
        description: 'Pauses the countdown',
        parameters: [],
        returnType: { type: 'null' },
        isAsync: false
      },
      resume: {
        description: 'Resumes the countdown',
        parameters: [],
        returnType: { type: 'null' },
        isAsync: false
      },
      reset: {
        description: 'Resets the countdown',
        parameters: [
          {
            type: 'string',
            description: 'Optional new target date'
          }
        ],
        returnType: { type: 'null' },
        isAsync: false
      },
      getRemainingTime: {
        description: 'Gets the current remaining time',
        parameters: [],
        returnType: {
          type: 'object',
          properties: {
            days: { type: 'number' },
            hours: { type: 'number' },
            minutes: { type: 'number' },
            seconds: { type: 'number' },
            total: { type: 'number' }
          }
        },
        isAsync: false
      }
    }
  },
  
  // Service Provider Interfaces
  spis: {
    requires: {
      timeService: {
        description: 'Service for time-related operations',
        methods: {
          getCurrentTime: {
            description: 'Gets the current time',
            parameters: [],
            returnType: { type: 'number' },
            isRequired: true,
            isAsync: false
          },
          formatTime: {
            description: 'Formats a time value',
            parameters: [
              {
                type: 'number',
                description: 'Time value in milliseconds'
              },
              {
                type: 'string',
                description: 'Format string'
              }
            ],
            returnType: { type: 'string' },
            isRequired: false,
            isAsync: false
          }
        }
      }
    }
  },
  
  // Error handling
  errorHandling: {
    errors: {
      'invalid-date': {
        description: 'Invalid target date',
        recoverable: true,
        defaultMessage: 'The provided target date is invalid'
      },
      'time-service-unavailable': {
        description: 'Time service is unavailable',
        recoverable: false,
        defaultMessage: 'Time service is unavailable'
      }
    },
    fallback: {
      ui: 'countdown-fallback',
      retry: true,
      maxRetries: 3
    }
  },
  
  // UI specifications
  ui: {
    type: 'component',
    framework: 'react',
    styling: {
      tailwind: true,
      styleFramework: 'shadcn'
    },
    responsive: {
      mobile: true,
      tablet: true,
      desktop: true,
      breakpoints: {
        mobile: '320px',
        tablet: '768px',
        desktop: '1024px'
      }
    },
    a11y: {
      ariaRoles: {
        root: 'timer',
        days: 'text',
        hours: 'text',
        minutes: 'text',
        seconds: 'text'
      },
      tabIndex: 0,
      focusable: true
    },
    ssr: {
      supported: true,
      hydration: true
    },
    slots: {
      prefix: {
        description: 'Content to display before the countdown',
        required: false
      },
      suffix: {
        description: 'Content to display after the countdown',
        required: false
      },
      separator: {
        description: 'Custom separator between time units',
        required: false
      },
      timeUnit: {
        description: 'Custom time unit display',
        required: false
      }
    }
  },
  
  // Dependencies
  dependencies: [
    {
      name: 'date-fns',
      version: '^2.30.0',
      type: 'npm',
      isOptional: false
    },
    {
      name: 'react',
      version: '^18.0.0',
      type: 'npm',
      isOptional: false
    }
  ],
  
  // Lifecycle hooks
  lifecycle: {
    init: {
      handler: 'initialize',
      async: false
    },
    mount: {
      handler: 'startCountdown',
      async: false
    },
    update: {
      handler: 'updateCountdown',
      async: false
    },
    unmount: {
      handler: 'cleanupCountdown',
      async: false
    },
    error: {
      handler: 'handleError',
      async: false
    }
  },
  
  // Integration with platforms
  integration: {
    platforms: {
      yida: {
        adapter: 'yida-countdown-adapter',
        config: {
          renderMode: 'client'
        }
      },
      dingtalk: {
        adapter: 'dingtalk-countdown-adapter',
        config: {
          cardType: 'interactive'
        }
      }
    },
    data: {
      sources: ['timeService'],
      transformations: ['formatTimeRemaining']
    }
  },
  
  // Context requirements
  context: {
    requires: ['theme', 'locale', 'timeZone'],
    provides: ['countdown'],
    engineering: {
      contextDirectory: '.context',
      configFiles: ['countdown.config.json']
    }
  },
  
  // Additional settings
  settings: {
    tickInterval: 1000, // Update interval in milliseconds
    warnThreshold: 60000, // Warn when less than this time remains (ms)
    criticalThreshold: 10000 // Critical styling when less than this time remains (ms)
  },
  
  // Metadata
  metadata: {
    createdAt: '2023-12-15T12:00:00Z',
    updatedAt: '2023-12-15T12:00:00Z',
    creationContext: {
      aiAssisted: true,
      aiModel: 'Claude 3.7 Sonnet',
      prompt: 'Create a countdown timer block'
    },
    usage: {
      installCount: 0,
      rating: 0,
      feedbackCount: 0
    },
    docs: {
      readme: 'A customizable countdown timer component',
      examples: [
        'Basic usage: <CountdownTimer targetDate="2023-12-31T23:59:59Z" />',
        'Custom format: <CountdownTimer targetDate="2023-12-31T23:59:59Z" format="custom" customFormat="DD:HH:MM:SS" />'
      ],
      apiDocs: 'See documentation for all available props and methods'
    }
  }
};

/**
 * Type definition for CountdownTimer props
 */
interface CountdownTimerProps {
  targetDate: string;
  showDays?: boolean;
  showHours?: boolean;
  showMinutes?: boolean;
  showSeconds?: boolean;
  format?: 'compact' | 'full' | 'custom';
  customFormat?: string;
  onComplete?: () => void;
}

/**
 * Type definition for CountdownTimer state
 */
interface CountdownTimerState {
  remainingTime: {
    days: number;
    hours: number;
    minutes: number;
    seconds: number;
    total: number;
  };
  intervalId: number;
  isPaused: boolean;
}

/**
 * Example React implementation of the CountdownTimer AIBlock
 */
export class CountdownTimerImplementation {
  private spec: AIBlock<CountdownTimerProps, CountdownTimerState>;
  private props: CountdownTimerProps;
  private state: CountdownTimerState;
  private timeService: any;
  private eventEmitter: any;
  
  constructor(props: CountdownTimerProps, timeService: any, eventEmitter: any) {
    this.spec = countdownTimerSpec;
    this.props = { ...this.spec.props.defaultValues, ...props };
    this.state = {
      remainingTime: { days: 0, hours: 0, minutes: 0, seconds: 0, total: 0 },
      intervalId: 0,
      isPaused: false,
      ...this.spec.state.inner.initialState
    };
    this.timeService = timeService;
    this.eventEmitter = eventEmitter;
    
    // Initialize component
    this.initialize();
  }
  
  /**
   * Initialize the component
   */
  private initialize(): void {
    // Validate props
    this.validateProps();
    
    // Calculate initial remaining time
    this.calculateRemainingTime();
    
    // Register event listeners
    this.registerEventListeners();
  }
  
  /**
   * Validate component props
   */
  private validateProps(): void {
    // Check if target date is valid
    if (!Date.parse(this.props.targetDate)) {
      throw new Error('Invalid target date');
    }
    
    // Check if custom format is provided when format is 'custom'
    if (this.props.format === 'custom' && !this.props.customFormat) {
      throw new Error('Custom format is required when format is set to "custom"');
    }
  }
  
  /**
   * Calculate remaining time
   */
  private calculateRemainingTime(): void {
    const now = this.timeService.getCurrentTime();
    const targetTime = new Date(this.props.targetDate).getTime();
    const difference = Math.max(0, targetTime - now);
    
    // Calculate time units
    const days = Math.floor(difference / (1000 * 60 * 60 * 24));
    const hours = Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((difference % (1000 * 60)) / 1000);
    
    // Update state
    this.state.remainingTime = {
      days,
      hours,
      minutes,
      seconds,
      total: difference
    };
    
    // Check if countdown is complete
    if (difference === 0) {
      this.handleComplete();
    }
  }
  
  /**
   * Start the countdown
   */
  public start(): void {
    if (this.state.intervalId) {
      return; // Already running
    }
    
    // Start interval
    const intervalId = setInterval(() => {
      this.tick();
    }, this.spec.settings.tickInterval) as unknown as number;
    
    // Update state
    this.state.intervalId = intervalId;
    this.state.isPaused = false;
    
    // Emit event
    this.eventEmitter.emit('countdown:start');
  }
  
  /**
   * Handle interval tick
   */
  private tick(): void {
    if (this.state.isPaused) {
      return;
    }
    
    // Calculate remaining time
    this.calculateRemainingTime();
    
    // Emit tick event
    this.eventEmitter.emit('countdown:tick', {
      remainingTime: this.state.remainingTime
    });
  }
  
  /**
   * Pause the countdown
   */
  public pause(): void {
    this.state.isPaused = true;
    
    // Emit event
    this.eventEmitter.emit('countdown:pause');
  }
  
  /**
   * Resume the countdown
   */
  public resume(): void {
    this.state.isPaused = false;
    
    // Emit event
    this.eventEmitter.emit('countdown:resume');
  }
  
  /**
   * Reset the countdown
   */
  public reset(newTargetDate?: string): void {
    // Update target date if provided
    if (newTargetDate) {
      this.props.targetDate = newTargetDate;
    }
    
    // Recalculate remaining time
    this.calculateRemainingTime();
    
    // Emit event
    this.eventEmitter.emit('countdown:reset', {
      targetDate: this.props.targetDate
    });
  }
  
  /**
   * Get the current remaining time
   */
  public getRemainingTime(): CountdownTimerState['remainingTime'] {
    return { ...this.state.remainingTime };
  }
  
  /**
   * Handle countdown completion
   */
  private handleComplete(): void {
    // Clear interval
    if (this.state.intervalId) {
      clearInterval(this.state.intervalId);
      this.state.intervalId = 0;
    }
    
    // Call onComplete callback if provided
    if (this.props.onComplete) {
      this.props.onComplete();
    }
    
    // Emit event
    this.eventEmitter.emit('countdown:complete');
  }
  
  /**
   * Register event listeners
   */
  private registerEventListeners(): void {
    // Register event handlers
    this.eventEmitter.on('countdown:pause', this.handlePause.bind(this));
    this.eventEmitter.on('countdown:resume', this.handleResume.bind(this));
    this.eventEmitter.on('countdown:reset', this.handleReset.bind(this));
  }
  
  /**
   * Handle pause event
   */
  private handlePause(): void {
    this.pause();
  }
  
  /**
   * Handle resume event
   */
  private handleResume(): void {
    this.resume();
  }
  
  /**
   * Handle reset event
   */
  private handleReset(data: { targetDate?: string }): void {
    this.reset(data.targetDate);
  }
  
  /**
   * Start countdown (lifecycle mount hook)
   */
  public startCountdown(): void {
    this.start();
  }
  
  /**
   * Update countdown when props change (lifecycle update hook)
   */
  public updateCountdown(newProps: Partial<CountdownTimerProps>): void {
    // Update props
    this.props = { ...this.props, ...newProps };
    
    // Validate new props
    this.validateProps();
    
    // Reset with new target date if changed
    if (newProps.targetDate) {
      this.reset(newProps.targetDate);
    }
  }
  
  /**
   * Clean up countdown (lifecycle unmount hook)
   */
  public cleanupCountdown(): void {
    // Clear interval
    if (this.state.intervalId) {
      clearInterval(this.state.intervalId);
      this.state.intervalId = 0;
    }
    
    // Remove event listeners
    this.eventEmitter.off('countdown:pause', this.handlePause);
    this.eventEmitter.off('countdown:resume', this.handleResume);
    this.eventEmitter.off('countdown:reset', this.handleReset);
  }
  
  /**
   * Handle errors (lifecycle error hook)
   */
  public handleError(error: Error): void {
    console.error('CountdownTimer error:', error);
    
    // Clean up resources
    this.cleanupCountdown();
  }
  
  /**
   * Format the countdown display based on settings
   */
  public formatDisplay(): string {
    const { days, hours, minutes, seconds } = this.state.remainingTime;
    const { showDays, showHours, showMinutes, showSeconds, format, customFormat } = this.props;
    
    // Return empty string if all units are hidden
    if (!showDays && !showHours && !showMinutes && !showSeconds) {
      return '';
    }
    
    // Custom format
    if (format === 'custom' && customFormat) {
      return this.applyCustomFormat(customFormat);
    }
    
    // Build display string
    const parts: string[] = [];
    
    if (showDays && (days > 0 || format === 'full')) {
      parts.push(`${days}d`);
    }
    
    if (showHours && (hours > 0 || days > 0 || format === 'full')) {
      parts.push(`${hours.toString().padStart(2, '0')}h`);
    }
    
    if (showMinutes && (minutes > 0 || hours > 0 || days > 0 || format === 'full')) {
      parts.push(`${minutes.toString().padStart(2, '0')}m`);
    }
    
    if (showSeconds && (format === 'full' || parts.length === 0)) {
      parts.push(`${seconds.toString().padStart(2, '0')}s`);
    }
    
    return parts.join(' ');
  }
  
  /**
   * Apply custom format to countdown
   */
  private applyCustomFormat(customFormat: string): string {
    const { days, hours, minutes, seconds } = this.state.remainingTime;
    
    return customFormat
      .replace(/DD/g, days.toString().padStart(2, '0'))
      .replace(/HH/g, hours.toString().padStart(2, '0'))
      .replace(/MM/g, minutes.toString().padStart(2, '0'))
      .replace(/SS/g, seconds.toString().padStart(2, '0'));
  }
  
  /**
   * Get component specification
   */
  public getSpec(): AIBlock<CountdownTimerProps, CountdownTimerState> {
    return this.spec;
  }
}

// Example of using the countdown timer
/*
// Create dependencies
const timeService = {
  getCurrentTime: () => Date.now(),
  formatTime: (time, format) => new Date(time).toLocaleString()
};

const eventEmitter = {
  events: {},
  on(event, listener) {
    if (!this.events[event]) {
      this.events[event] = [];
    }
    this.events[event].push(listener);
  },
  off(event, listener) {
    if (!this.events[event]) return;
    this.events[event] = this.events[event].filter(l => l !== listener);
  },
  emit(event, data) {
    if (!this.events[event]) return;
    this.events[event].forEach(listener => listener(data));
  }
};

// Create countdown timer
const countdown = new CountdownTimerImplementation(
  {
    targetDate: '2023-12-31T23:59:59Z',
    showDays: true,
    showHours: true,
    showMinutes: true,
    showSeconds: true,
    format: 'full',
    onComplete: () => {
      console.log('Countdown complete!');
    }
  },
  timeService,
  eventEmitter
);

// Start the countdown
countdown.startCountdown();

// Get formatted display
console.log(countdown.formatDisplay());

// Later: clean up
countdown.cleanupCountdown();
*/

export default countdownTimerSpec; 