# Yida AI Block Specification

This document provides a comprehensive specification for AIBlocks - modular, composable components that can be generated using AI and used in both frontend and backend scenarios.

## Design Goals

The AIBlock specification aims to:

1. Enable professional and non-professional developers to create reusable components via AI
2. Support both frontend and backend use cases
3. Work seamlessly in Yida and AI Assistant scenarios
4. Provide a clear, consistent interface that both humans and AI can understand
5. Follow open source ecosystem standards and patterns

## Core Principles

An AIBlock follows these core principles:

- **Modular**: Each Block is an independent functional unit
- **Composable**: Blocks can be freely combined
- **Consistent**: Maintains design language and interaction consistency
- **Extensible**: Supports custom Blocks and styles
- **Technology-agnostic**: Can be implemented in various frameworks

## Minimal Viable Block

A minimal viable AIBlock satisfies these criteria:

- **Self-contained**: All dependencies are clearly declared and satisfied
- **Single-purpose**: Solves one and only one specific problem
- **Interface determinism**: Clear definition of input/output boundaries
- **Finite state**: Internal state can be completely understood and predicted
- **Evolution potential**: Can iterate and optimize internally while maintaining stable interfaces

## Block Structure

An AIBlock consists of the following key dimensions:

### 1. Basic Information

- `id`: Unique identifier
- `name`: Display name
- `description`: Functional description
- `version`: Semantic version (MAJOR.MINOR.PATCH)
- `type`: Block categorization (UI, LOGIC, DATA, INTEGRATION, AI, COMPOSITE)
- `tags`: Categorization and discovery tags
- `author`: Creator information
- `license`: License information

### 2. Input Properties (Props)

Properties define the input data accepted by the Block:

- `schema`: Property type definitions using JSON Schema format
- `defaultValues`: Default values for properties
- `validation`: Property validation rules

Example:

```typescript
props: {
  schema: {
    properties: {
      title: {
        type: 'string',
        description: 'The title text',
      },
      showIcon: {
        type: 'boolean',
        default: true,
      }
    },
    required: ['title']
  },
  defaultValues: {
    title: 'Default Title',
    showIcon: true
  }
}
```

### 3. State Management

Blocks manage two types of state:

- `inner`: Internal state managed within the Block
- `outer`: External state exposed to parent components
- `persistence`: State persistence configuration

### 4. Events

Events define the messaging interface:

- `emits`: Events emitted by the Block
- `listens`: Events listened to by the Block

### 5. APIs

APIs define the methods exposed by the Block:

- `methods`: Public methods with parameters and return types

### 6. Service Provider Interfaces (SPIs)

SPIs define external services required by the Block:

- `requires`: Required service interfaces

### 7. Error Handling

Error handling defines how the Block deals with failures:

- `errors`: Error definitions
- `fallback`: Fallback UI or behavior

### 8. UI Specification

UI definition describes how the Block renders:

- `type`: Rendering type (component, template, function, stream)
- `framework`: Framework specifics (React, Vue, etc.)
- `styling`: Styling information
- `responsive`: Responsive design configuration
- `a11y`: Accessibility configuration
- `ssr`: Server-side rendering support
- `slots`: UI slot definitions for composable UI

### 9. Dependencies

Dependencies define what the Block needs to operate:

- `name`: Dependency name
- `version`: Version specification
- `type`: Dependency type (npm, block, service, other)
- `isOptional`: Whether the dependency is optional

### 10. Lifecycle Hooks

Lifecycle hooks define when code executes:

- `init`: Initialization hook
- `mount`: Mount/render hook
- `update`: Update hook
- `unmount`: Unmount/destroy hook
- `error`: Error hook

### 11. Integration Points

Integration defines how the Block connects to platforms:

- `platforms`: Platform adapters (Yida, DingTalk, etc.)
- `data`: Data integrations

### 12. Context Requirements

Context defines the environment needed by the Block:

- `requires`: Required context providers
- `provides`: Context values provided by this Block
- `engineering`: Context engineering details

### 13. Block-specific Settings

- `settings`: Additional configuration options

### 14. Metadata

Metadata provides information about the Block itself:

- `createdAt`: Creation timestamp
- `updatedAt`: Last updated timestamp
- `creationContext`: Creation context (AI-assisted, model, prompt)
- `usage`: Usage statistics
- `docs`: Documentation
- `custom`: Additional custom metadata

## Supporting Systems

The AIBlock ecosystem includes these key systems:

### AIBlock Factory

Creates and manages Block instances:

- `create`: Create a new Block instance
- `register`: Register a Block in the registry
- `get`: Get a Block from the registry

### AIBlock Registry

Manages Block registrations and discovery:

- `register`: Register a Block
- `unregister`: Unregister a Block
- `getById`: Get a Block by ID
- `find`: Find Blocks by criteria
- `getAll`: Get all registered Blocks

### AIBlock Renderer

Renders a Block in a target environment:

- `render`: Render a Block
- `unmount`: Unmount a rendered Block
- `update`: Update a rendered Block with new props

### AIBlock Composer

Creates and composes Blocks using AI:

- `generateSpec`: Generate a Block specification based on a description
- `generateImplementation`: Generate the implementation code for a Block
- `compose`: Compose multiple Blocks into a composite Block

## Example Usage

Here's a simple example of how a Block specification might look:

```typescript
const countdownBlockSpec: AIBlock = {
  id: 'countdown-timer',
  name: 'Countdown Timer',
  description: 'A simple countdown timer block',
  version: '1.0.0',
  type: AIBlockType.UI,
  tags: ['timer', 'countdown', 'ui'],
  author: {
    name: 'Yida AI',
    organization: 'Yida'
  },
  license: 'MIT',
  
  props: {
    schema: {
      properties: {
        targetDate: {
          type: 'string',
          format: 'date-time',
          description: 'The target date to count down to'
        },
        showDays: {
          type: 'boolean',
          description: 'Whether to show days in the countdown'
        },
        showHours: {
          type: 'boolean',
          description: 'Whether to show hours in the countdown'
        }
      },
      required: ['targetDate']
    },
    defaultValues: {
      targetDate: new Date(Date.now() + 86400000).toISOString(), // 24 hours from now
      showDays: true,
      showHours: true
    }
  },
  
  state: {
    inner: {
      schema: {
        remainingTime: {
          type: 'object',
          properties: {
            days: { type: 'number' },
            hours: { type: 'number' },
            minutes: { type: 'number' },
            seconds: { type: 'number' }
          }
        },
        intervalId: { type: 'number' }
      },
      initialState: {
        remainingTime: { days: 0, hours: 0, minutes: 0, seconds: 0 },
        intervalId: 0
      }
    },
    outer: {
      schema: {
        isRunning: { type: 'boolean' }
      },
      initialState: {
        isRunning: true
      }
    }
  },
  
  events: {
    emits: {
      'countdown:complete': {
        description: 'Fired when the countdown reaches zero',
        payload: { type: 'null' }
      },
      'countdown:tick': {
        description: 'Fired on each countdown tick',
        payload: {
          type: 'object',
          properties: {
            remainingTime: {
              type: 'object',
              properties: {
                days: { type: 'number' },
                hours: { type: 'number' },
                minutes: { type: 'number' },
                seconds: { type: 'number' }
              }
            }
          }
        }
      }
    },
    listens: {
      'countdown:pause': {
        description: 'Pauses the countdown',
        handler: 'handlePause'
      },
      'countdown:resume': {
        description: 'Resumes the countdown',
        handler: 'handleResume'
      }
    }
  },
  
  apis: {
    methods: {
      start: {
        description: 'Starts the countdown',
        parameters: [],
        returnType: { type: 'void' },
        isAsync: false
      },
      pause: {
        description: 'Pauses the countdown',
        parameters: [],
        returnType: { type: 'void' },
        isAsync: false
      },
      reset: {
        description: 'Resets the countdown',
        parameters: [{
          type: 'string',
          description: 'Optional new target date'
        }],
        returnType: { type: 'void' },
        isAsync: false
      }
    }
  },
  
  ui: {
    type: 'component',
    framework: 'react',
    styling: {
      tailwind: true,
      styleFramework: 'shadcn'
    },
    responsive: {
      mobile: true,
      tablet: true,
      desktop: true
    }
  },
  
  lifecycle: {
    init: {
      handler: 'initialize',
      async: false
    },
    mount: {
      handler: 'startCountdown',
      async: false
    },
    unmount: {
      handler: 'clearCountdown',
      async: false
    }
  }
};
```

## Benefits of the AIBlock Specification

1. **Standardization**: Provides a consistent format for blocks across different frameworks and technologies
2. **AI Compatibility**: Designed for easy generation and interpretation by AI systems
3. **Developer Experience**: Clear structure makes it easy for developers to understand and work with blocks
4. **Composability**: Enables blocks to be combined in powerful ways
5. **Platform Independence**: Works across different platforms and frameworks
6. **Discoverability**: Rich metadata supports finding and reusing blocks

## Implementation Considerations

When implementing the AIBlock specification:

1. Start with the minimal viable block definition
2. Add only the dimensions necessary for your use case
3. Prefer composition over complex monolithic blocks
4. Design blocks with reusability in mind
5. Document blocks thoroughly, especially their inputs and outputs
6. Consider state management carefully, keeping blocks focused on their core functionality

## Future Directions

The AIBlock specification can evolve in these directions:

1. **Extended Validation**: More sophisticated validation of block configurations
2. **Versioning**: Support for block versioning and upgrades
3. **Analytics**: Built-in usage tracking and insights
4. **Testing Framework**: Standardized testing methodologies for blocks
5. **Performance Optimizations**: Guidelines for high-performance blocks
6. **Accessibility Enhancements**: Further a11y improvements
7. **Designer Tooling**: Visual design tools for creating and configuring blocks

## References and Related Standards

The AIBlock specification draws inspiration from:

- React/Vue component patterns
- Web Components specifications
- JSON Schema
- OpenAPI
- Material Design and Ant Design patterns
- Atomic Design Methodology
