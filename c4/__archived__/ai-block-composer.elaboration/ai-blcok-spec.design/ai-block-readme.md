# Yida AI Block Specification

A comprehensive specification for AIBlocks - modular, composable components that can be created using AI and used in both frontend and backend scenarios.

## Overview

The Yida AI Block Specification defines a standardized way to create, distribute, and use blocks of functionality that can be easily composed together. It's designed to be both human and AI readable, enabling effortless creation of new blocks by professional and non-professional developers alike.

## Files in This Repository

- `ai-block.spec.d.ts` - TypeScript declaration file defining the AIBlock interfaces
- `ai-block.spec.design.md` - Human-readable documentation of the specification
- `ai-block-example.ts` - Example implementation of a countdown timer block

## Key Concepts

### What is an AIBlock?

An AIBlock is a self-contained unit of functionality with well-defined interfaces, properties, and behaviors. It follows these core principles:

- **Modular**: Each Block is an independent functional unit
- **Composable**: Blocks can be freely combined
- **Consistent**: Maintains design language and interaction consistency
- **Extensible**: Supports custom Blocks and styles
- **Technology-agnostic**: Can be implemented in various frameworks

### Minimal Viable Block

A minimal viable AIBlock satisfies these criteria:

- **Self-contained**: All dependencies are clearly declared and satisfied
- **Single-purpose**: Solves one and only one specific problem
- **Interface determinism**: Clear definition of input/output boundaries
- **Finite state**: Internal state can be completely understood and predicted
- **Evolution potential**: Can iterate and optimize internally while maintaining stable interfaces

## Getting Started

### Creating a New Block

1. Define your block specification following the `AIBlock` interface
2. Implement the block functionality
3. Register the block with the AIBlock registry
4. Use the block in your application

### Example: Countdown Timer Block

The `ai-block-example.ts` file contains a complete implementation of a countdown timer block. Key parts include:

```typescript
// Define the block specification
const countdownTimerSpec: AIBlock<CountdownTimerProps, CountdownTimerState> = {
  id: 'countdown-timer',
  name: 'Countdown Timer',
  description: 'A customizable countdown timer',
  // ... other properties
};

// Implement the block functionality
export class CountdownTimerImplementation {
  // ... implementation details
}

// Use the block
const countdown = new CountdownTimerImplementation(
  {
    targetDate: '2023-12-31T23:59:59Z',
    showDays: true,
    // ... other props
  },
  timeService,
  eventEmitter
);

// Start the countdown
countdown.startCountdown();
```

## Block Structure

Each AIBlock consists of these key dimensions:

1. **Basic Information**: ID, name, description, version, type, tags, author, license
2. **Input Properties (Props)**: Define the data accepted by the block
3. **State Management**: Internal and external state
4. **Events**: Messages emitted and received by the block
5. **APIs**: Methods exposed by the block
6. **Service Provider Interfaces (SPIs)**: External services required
7. **Error Handling**: How to handle failures
8. **UI Specification**: Rendering details
9. **Dependencies**: External requirements
10. **Lifecycle Hooks**: When code executes
11. **Integration Points**: How to connect to platforms
12. **Context Requirements**: Environmental needs
13. **Block-specific Settings**: Additional configuration
14. **Metadata**: Information about the block itself

## Using with AI

The AIBlock specification is designed to be easily generated and interpreted by AI systems. The clear structure makes it straightforward for AI to:

1. Understand existing blocks
2. Generate new block specifications
3. Implement blocks based on specifications
4. Compose multiple blocks together

## Block Ecosystem

The AIBlock ecosystem includes these key systems:

- **AIBlock Factory**: Creates and manages block instances
- **AIBlock Registry**: Manages block registrations and discovery
- **AIBlock Renderer**: Renders blocks in target environments
- **AIBlock Composer**: Creates and composes blocks using AI

## Best Practices

When working with AIBlocks:

1. Start with the minimal viable block definition
2. Add only the dimensions necessary for your use case
3. Prefer composition over complex monolithic blocks
4. Design blocks with reusability in mind
5. Document blocks thoroughly, especially their inputs and outputs
6. Consider state management carefully, keeping blocks focused on their core functionality

## Contributing

Contributions to the AIBlock specification are welcome! Please follow these steps:

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Submit a pull request

## License

MIT
