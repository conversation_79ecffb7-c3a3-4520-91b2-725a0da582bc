/**
 * Yida AI Block Specification
 * TypeScript declaration file for AI Block Composer
 * 
 * This specification defines the structure and interfaces for AIBlocks
 * that can be used in both frontend and backend scenarios.
 * 
 * Design principles:
 * - Modular: Each Block is an independent functional unit
 * - Composable: Blocks can be freely combined
 * - Consistent: Maintains design language and interaction consistency
 * - Extensible: Supports custom Blocks and styles
 * - Technology-agnostic: Can be implemented in various frameworks
 */

/**
 * Core Block Specification
 * 
 * Defines the minimal viable Block that satisfies:
 * - Self-contained: All dependencies are clearly declared and satisfied
 * - Single-purpose: Solves one and only one specific problem
 * - Interface determinism: Clear definition of input/output boundaries
 * - Finite state: Internal state can be completely understood and predicted
 * - Evolution potential: Can iterate and optimize internally while maintaining stable interfaces
 */
export interface AIBlock<P = any, S = any> {
  /**
   * Unique identifier for the Block
   */
  id: string;
  
  /**
   * Display name of the Block
   */
  name: string;
  
  /**
   * Description of the Block's functionality
   */
  description: string;
  
  /**
   * Version of the Block
   * Following semantic versioning (MAJOR.MINOR.PATCH)
   */
  version: string;
  
  /**
   * Block type categorization
   */
  type: AIBlockType;
  
  /**
   * Tags for categorization and discovery
   */
  tags: string[];
  
  /**
   * Author information
   */
  author: BlockAuthor;
  
  /**
   * License information
   */
  license: string;
  
  /**
   * Block input properties
   */
  props: AIBlockProps<P>;
  
  /**
   * Block state management
   */
  state: AIBlockState<S>;
  
  /**
   * Events emitted by the Block
   */
  events: AIBlockEvents;
  
  /**
   * APIs exposed by the Block
   */
  apis: AIBlockAPIs;
  
  /**
   * Service Provider Interfaces required by the Block
   */
  spis: AIBlockSPIs;
  
  /**
   * Error handling configuration
   */
  errorHandling: AIBlockErrorHandling;
  
  /**
   * UI rendering specifications
   */
  ui: AIBlockUI;
  
  /**
   * Block dependencies
   */
  dependencies: AIBlockDependency[];
  
  /**
   * Lifecycle hooks
   */
  lifecycle: AIBlockLifecycle;
  
  /**
   * Integration points with other systems
   */
  integration: AIBlockIntegration;
  
  /**
   * Context requirements
   */
  context: AIBlockContext;
  
  /**
   * Block-specific settings
   */
  settings: Record<string, any>;
  
  /**
   * Block metadata
   */
  metadata: AIBlockMetadata;
}

/**
 * Block Type Categorization
 */
export enum AIBlockType {
  UI = 'ui',               // Pure UI components
  LOGIC = 'logic',         // Business logic components
  DATA = 'data',           // Data processing components
  INTEGRATION = 'integration', // Integration components
  AI = 'ai',               // AI-powered components
  COMPOSITE = 'composite', // Combination of multiple blocks
}

/**
 * Block Author Information
 */
export interface BlockAuthor {
  name: string;
  email?: string;
  url?: string;
  organization?: string;
}

/**
 * Block Properties Specification
 */
export interface AIBlockProps<P = any> {
  /**
   * Property definitions with types and validation
   */
  schema: PropSchema<P>;
  
  /**
   * Default values for properties
   */
  defaultValues: Partial<P>;
  
  /**
   * Property validation rules
   */
  validation?: PropValidation<P>;
}

/**
 * Property Schema Definition
 */
export interface PropSchema<P = any> {
  /**
   * Property definitions using JSON Schema format
   */
  properties: Record<keyof P, PropDefinition>;
  
  /**
   * Required property keys
   */
  required?: Array<keyof P>;
}

/**
 * Property Definition
 */
export interface PropDefinition {
  type: 'string' | 'number' | 'boolean' | 'object' | 'array' | 'null' | 'any';
  description?: string;
  enum?: any[];
  default?: any;
  format?: string;
  pattern?: string;
  minimum?: number;
  maximum?: number;
  minLength?: number;
  maxLength?: number;
  examples?: any[];
  [key: string]: any; // Additional JSON Schema properties
}

/**
 * Property Validation
 */
export interface PropValidation<P = any> {
  /**
   * Validation rules by property key
   */
  rules: {
    [K in keyof P]?: ValidationRule[];
  };
  
  /**
   * Custom validation function
   */
  customValidator?: (props: P) => ValidationResult;
}

/**
 * Validation Rule
 */
export interface ValidationRule {
  validator: (value: any) => boolean;
  message: string;
}

/**
 * Validation Result
 */
export interface ValidationResult {
  valid: boolean;
  errors?: string[];
}

/**
 * Block State Management
 */
export interface AIBlockState<S = any> {
  /**
   * Internal state (managed within the Block)
   */
  inner: {
    schema: Record<string, PropDefinition>;
    initialState: Partial<S>;
  };
  
  /**
   * External state (exposed to parent components)
   */
  outer: {
    schema: Record<string, PropDefinition>;
    initialState: Partial<any>;
  };
  
  /**
   * State persistence configuration
   */
  persistence?: {
    storage: 'local' | 'session' | 'memory' | 'custom';
    key?: string;
    customStorage?: any;
  };
}

/**
 * Block Events
 */
export interface AIBlockEvents {
  /**
   * Events emitted by the Block
   */
  emits: {
    [eventName: string]: {
      description: string;
      payload: PropDefinition;
    };
  };
  
  /**
   * Events listened to by the Block
   */
  listens: {
    [eventName: string]: {
      description: string;
      handler: string;
    };
  };
}

/**
 * Block APIs (methods exposed by the Block)
 */
export interface AIBlockAPIs {
  /**
   * Public methods exposed by the Block
   */
  methods: {
    [methodName: string]: {
      description: string;
      parameters: PropDefinition[];
      returnType: PropDefinition;
      isAsync: boolean;
    };
  };
}

/**
 * Block Service Provider Interfaces
 * (external services required by the Block)
 */
export interface AIBlockSPIs {
  /**
   * Required service interfaces
   */
  requires: {
    [serviceName: string]: {
      description: string;
      methods: {
        [methodName: string]: {
          description: string;
          parameters: PropDefinition[];
          returnType: PropDefinition;
          isRequired: boolean;
          isAsync: boolean;
        };
      };
    };
  };
}

/**
 * Block Error Handling
 */
export interface AIBlockErrorHandling {
  /**
   * Error definitions
   */
  errors: {
    [errorType: string]: {
      description: string;
      recoverable: boolean;
      defaultMessage: string;
    };
  };
  
  /**
   * Fallback UI or behavior
   */
  fallback?: {
    ui?: string;
    retry?: boolean;
    maxRetries?: number;
  };
}

/**
 * Block UI Definition
 */
export interface AIBlockUI {
  /**
   * Rendering type
   */
  type: 'component' | 'template' | 'function' | 'stream';
  
  /**
   * Framework specifics
   */
  framework?: 'react' | 'vue' | 'angular' | 'svelte' | 'native' | 'any';
  
  /**
   * Styling information
   */
  styling?: {
    css?: string;
    tailwind?: boolean;
    styleFramework?: 'antd' | 'material-ui' | 'bootstrap' | 'shadcn' | 'custom';
  };
  
  /**
   * Responsive design configuration
   */
  responsive?: {
    mobile?: boolean;
    tablet?: boolean;
    desktop?: boolean;
    breakpoints?: Record<string, string>;
  };
  
  /**
   * Accessibility configuration
   */
  a11y?: {
    ariaRoles?: Record<string, string>;
    tabIndex?: number;
    focusable?: boolean;
  };
  
  /**
   * Server-side rendering support
   */
  ssr?: {
    supported: boolean;
    hydration?: boolean;
  };
  
  /**
   * UI slot definitions for composable UI
   */
  slots?: {
    [slotName: string]: {
      description: string;
      required: boolean;
    };
  };
}

/**
 * Block Dependency
 */
export interface AIBlockDependency {
  name: string;
  version: string;
  type: 'npm' | 'block' | 'service' | 'other';
  isOptional: boolean;
  url?: string;
}

/**
 * Block Lifecycle Hooks
 */
export interface AIBlockLifecycle {
  /**
   * Initialization hook
   */
  init?: {
    handler: string;
    async: boolean;
  };
  
  /**
   * Mount/render hook
   */
  mount?: {
    handler: string;
    async: boolean;
  };
  
  /**
   * Update hook
   */
  update?: {
    handler: string;
    async: boolean;
  };
  
  /**
   * Unmount/destroy hook
   */
  unmount?: {
    handler: string;
    async: boolean;
  };
  
  /**
   * Error hook
   */
  error?: {
    handler: string;
    async: boolean;
  };
}

/**
 * Block Integration
 */
export interface AIBlockIntegration {
  /**
   * Platform adapters
   */
  platforms: {
    yida?: {
      adapter: string;
      config?: Record<string, any>;
    };
    dingtalkAssistant?: {
      adapter: string;
      config?: Record<string, any>;
    };
    [platform: string]: {
      adapter: string;
      config?: Record<string, any>;
    } | undefined;
  };
  
  /**
   * Data integrations
   */
  data?: {
    sources?: string[];
    sinks?: string[];
    transformations?: string[];
  };
}

/**
 * Block Context Requirements
 */
export interface AIBlockContext {
  /**
   * Required context providers
   */
  requires?: string[];
  
  /**
   * Context values provided by this Block
   */
  provides?: string[];
  
  /**
   * Context engineering details
   */
  engineering?: {
    contextDirectory?: string;
    configFiles?: string[];
  };
}

/**
 * Block Metadata
 */
export interface AIBlockMetadata {
  /**
   * Creation timestamp
   */
  createdAt: string;
  
  /**
   * Last updated timestamp
   */
  updatedAt: string;
  
  /**
   * Creation context
   */
  creationContext?: {
    aiAssisted: boolean;
    aiModel?: string;
    prompt?: string;
  };
  
  /**
   * Usage statistics
   */
  usage?: {
    installCount: number;
    rating?: number;
    feedbackCount?: number;
  };
  
  /**
   * Documentation
   */
  docs?: {
    readme?: string;
    examples?: string[];
    apiDocs?: string;
  };
  
  /**
   * Additional custom metadata
   */
  custom?: Record<string, any>;
}

/**
 * AIBlock Factory Interface
 * Used to create new Block instances
 */
export interface AIBlockFactory<P = any, S = any> {
  /**
   * Create a new Block instance
   */
  create(config: Partial<AIBlock<P, S>>): AIBlock<P, S>;
  
  /**
   * Register a Block in the registry
   */
  register(block: AIBlock<P, S>): void;
  
  /**
   * Get a Block from the registry
   */
  get(id: string): AIBlock<P, S> | undefined;
}

/**
 * AIBlock Registry Interface
 * Manages Block registrations and discovery
 */
export interface AIBlockRegistry {
  /**
   * Register a Block
   */
  register(block: AIBlock): void;
  
  /**
   * Unregister a Block
   */
  unregister(id: string): boolean;
  
  /**
   * Get a Block by ID
   */
  getById(id: string): AIBlock | undefined;
  
  /**
   * Find Blocks by criteria
   */
  find(criteria: Partial<AIBlock> | ((block: AIBlock) => boolean)): AIBlock[];
  
  /**
   * Get all registered Blocks
   */
  getAll(): AIBlock[];
}

/**
 * AIBlock Renderer Interface
 * Renders a Block in a target environment
 */
export interface AIBlockRenderer<P = any> {
  /**
   * Render a Block
   */
  render(block: AIBlock, props?: P, container?: any): any;
  
  /**
   * Unmount a rendered Block
   */
  unmount(renderedBlock: any): void;
  
  /**
   * Update a rendered Block with new props
   */
  update(renderedBlock: any, newProps: P): void;
}

/**
 * AIBlock Composer Interface
 * Creates and composes Blocks using AI
 */
export interface AIBlockComposer {
  /**
   * Generate a Block specification based on a description
   */
  generateSpec(description: string, context?: any): Promise<AIBlock>;
  
  /**
   * Generate the implementation code for a Block
   */
  generateImplementation(spec: AIBlock, framework?: string): Promise<Record<string, string>>;
  
  /**
   * Compose multiple Blocks into a composite Block
   */
  compose(blocks: AIBlock[], composition: any): Promise<AIBlock>;
} 