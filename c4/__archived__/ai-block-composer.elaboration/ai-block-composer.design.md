# Yida AI Composer - AI Block Composer

## Design Goal

* AI Block Composer can create a block by both professional developer and non-professional developer via AI.
* The block can be used in both frontend and backend.
* The block can be used in Yida and AI Assistant scenarios.

This lower the barrier to create a block which can increase the productivity of both professional developer and non-professional developer to produce reusable blocks and enrich the Yida ecosystem both in `width` and `depth` dimensions.

## Elaborations

基于目标，我们的演绎可以从以下几个方面展开：

### Mental-models & Theories

* 🤩🤩🤩 **6-hats Thinking**（六顶思考帽）`./ai-block-composer.elaboration/ai-block-composer.6hat.md`
* 🤩🤩🤩🤩🤩 **First Principles Thinking**（第一性原理）: `./ai-block-composer.elaboration/ai-block-composer.first-principle.md`
* 🤩🤩 **Systems Thinking**（系统思维）: in this document
* 🤩🤩 **Inversion Thinking**（逆向思维）: `./ai-block-composer.elaboration/ai-block-composer.inverse.md`
* 🤩🤩 **5W2H** (What, Why, Who, When, Where, How) thinking: `./ai-block-composer.elaboration/ai-block-composer-5w2h-analysis.md`

WIP in the later execution layer:

* **SCAMPER Technique**:
  * Substitute: 现有Block元素替换方案
  * Combine: 跨领域设计模式的组合
  * Adapt: 适应不同技术栈的调整策略
  * Modify: 渐进式优化路径
  * Put to other uses: 非预期使用场景挖掘
  * Eliminate: 简化不必要的设计元素
  * Reverse: 交互流程的逆向验证
* **OODA Loop**（观察-定向-决策-行动）:
  * Observe: 实时收集用户操作数据
  * Orient: 分析当前设计上下文
  * Decide: AI生成策略选择
  * Act: 快速输出设计方案

### Assumptions

* 用户具备基本的设计和开发知识
* AI 模型能够理解设计意图并生成合适的代码
* 系统需要支持主流的设计规范和开发框架

## 市场竞争分析

### 主要竞争产品

* `V0` -> Vercel
* `Framer AI` / `UiPaaS` -> @康师傅
* `Claude Artifact` -> HTML 驱动的 Playground
* `Blot.new` -> OpenSource -> https://ata.atatech.org/articles/11020375215

## Design Principles

### Design Patterns

* **模块化设计**: 每个 Block 都是独立的功能单元
* **可组合性**: Blocks 之间可以自由组合
* **一致性**: 保持设计语言和交互方式的一致
* **可扩展性**: 支持自定义 Block 和样式

### Design Constraints

* 剥离对 Yida 平台的依赖，打造一个通用的 Block 标准，尽可能少的依赖 Yida 平台的上下文

* **Technical Constraints**
  * 需要支持主流前端框架 (React, Vue 等)
  * AI 模型的响应时间要求
  * 浏览器兼容性要求

* **Business Constraints**
  * 需要考虑 AI API 的调用成本
  * 用户的学习成本

* **Resource Constraints**
  * 开发团队规模
  * 项目时间线

## Design Functionalities

### Target Users

* 宜搭的服务商
* 宜搭的低代码开发者
* 有基础编程思路的 UI/UX 设计师
* 全栈开发者
* 产品经理

### User Needs & Pain Points

**面对低代码 / 0 代码 / 智能体的底层可复用的 AI Logic UI Block 的快速生产**

能够让生产过程具备下面的性质：

* 高效率
* 低门槛
* 高可用
* 高稳定
* 高扩展

Fully AIPowered and AIBoosted!

### User Journey

1. 准备 Block 的上下文信息
2. 使用 AI 生成 Block 的代码
3. 使用 Block 编辑器 / 设计器进行调整
4. 使用 Block 渲染服务进行渲染
5. 使用 Block 集成适配器进行集成到 Yida / DingTalk 平台
6. 发布 Block 到 Block 市场 / 组织并使用

### Scenarios Driven Design

* 简单：生成一个倒计时 Block
* 简单：生成一个音乐播放器，可以自动播放声音来源
* 中等：生成一个 AI 按钮，点击后可以根据 xx 编写的参数，生成 xx 图片、视频
* 中等：根据 xxx 的数据和模型信息，生成一个复工复产企业信息展示界面，具备 Apple 的风格
* 中等：根据 xxx 场景，生成一个评论和回复组件，要求 xxx
* 中等：生成一个白板组件，用户可以自由绘制，并可以导出为图片，支持移动端、iPad 使用（主要）
* 中等：根据 xxx 数据信息，设计和展示一个锅炉的温度、压力、流量、液位等参数，并使用图表绘制，温度要求使用温度计的 UI 形式做设计展示
* 中等：设计一个模拟汽车驾驶舱的控制按钮界面，模拟汽车驾驶面板
* 复杂：设计一个模拟飞机驾驶舱的控制按钮界面，模拟飞机驾驶面板
* 复杂：根据 xxx 上下文，生成一个病例展示系统要求 xxx
* 复杂：根据 xxx 行业的信息，生成一个 BOM 树，用于展示物料层次堆叠信息
* 复杂：根据 xxx 领域 & 行业上下文，生成一个物流订单信息管理界面，要求 xxx

## Design Structure

### Core Components

* **Block Specification**: 定义 Block 的规范
  * **OpenSource Specification**: 提供开源的 Block 规范 -> 最好有开源的杠杆可以借鉴
    * props
    * state
      * inner
      * outer
    * events
    * APIs
    * SPIs
    * error
  
> 最小可行 Block 是同时满足以下条件的单元：
>
> * 自包含性：所有依赖被明确声明且可被满足
> * 目的单一性：解决一个且仅一个特定问题
> * 接口确定性：输入输出边界清晰定义
> * 状态有限性：内部状态可被完全理解和预测
> * 演化潜力：能够在保持接口稳定的前提下内部迭代优化

* **Block Composer**: 根据规范生成 Block
  * **Block Editor / Designer**: 提供 Block 编辑器 / 设计器
  * **Block Context Engineering**: 提供 Block 上下文工程服务
    * **.context** 目录：提供 Block 的上下文信息 -> 开源范式
    * **MCP Server**: 提供 MCP 服务扩展上下文
    * **User Context**: 提供用户上下文信息
      * fully boosted quotable context
* **Block Rendering Server**: 提供 Block 渲染服务 SSR
* **Block Rendering Runtime**: 提供 Block Client 渲染框架
* **Block Integration Adapter**: 提供 Block 集成适配器
  * **Yida LowCode Block Adapter**： 提供 Yida 低代码体系的适配器

## Design Highlights / Breakthroughs

* 【意图驱动】：从意图出发，到实现，再到迭代
* 【AI 能力 x Yida 平台能力 x Block 高度的 AI 创作能力】= AI + 前后一体的应用模式的诞生，绝非简单 UI，更多的像是一个前后一体的 Business Logic Block，用于解决特定场景的问题
* 【前后一体的应用模式】：Serverless 驱动的 SSE / Streaming UI 的模式

## Design Implementation

### Core Design Principles

* 【意图驱动】：从意图出发，到实现，再到迭代
* 【组合优先】：通过组合现有 Block 解决问题，而非从头构建
* 【双向翻译】：在人类理解的语义层和机器执行的实现层间自由转换
* 【演化连续性】：代码不是静态产物而是持续演化的有机体
* 【集体智慧】：个体贡献汇聚为集体知识库，服务于整个生态系统

and

* 【组合价值法则】：确保 Block 组合的价值大于各部分之和
* 【网络效应】：每个新 Block 都增加与现有 Block 的潜在组合可能
* 【多维度增长】：同时在数量、质量、多样性和连接性上增长
* 【降低边际成本】：使创建第 n+1 个 Block 的成本低于第 n 个
* 【自我增强循环】：Block 使用促进改进，改进促进更多使用

and

* 【量变引发质变】：10 个、100 个、1000 个、10000 个、100000 个的时候建立策略观测点

and

* 【技术无关性】：技术无关，是非常重要的驱动模式，长远来看，我们要做到技术无关，也就是技术栈无关，这样才能做到真正的通用和扩展 in our AI driven world

### DeepSeek And Ask Questions

* Why not use 钉钉动态卡片技术？
  * limitations problem: large and complex UI design
  * platform binding problem: 钉钉的动态卡片技术是钉钉的，不是通用的
  * too many configs and constraints: 钉钉的动态卡片技术有太多的配置和限制，不利于通用和扩展
  * no AI generative support: 钉钉的动态卡片技术没有 AI 生成支持，DSL 不标准且不开放
  * no streaming AI response: 钉钉的动态卡片技术没有流式 UI 生成支持
  * large dependency: 钉钉的动态卡片技术有较大的依赖，香蕉森林问题

### Technical Stack

* **Frontend**
  * React/Next.js
  * TypeScript
  * Tailwind CSS
  
* **Backend**
  * Node.js
  * AI-SDK of Vercel + Adapter to Yida / DingTalk AIPaaS
  
* **Infrastructure**
  * Yida Infra.

### Dependencies

* AI 模型服务
* UI 组件库
  * AntDesign
  * Shadcn UI

## Testing & Validation

> 建立有效的设计量化指标，来证明模式的有效性。

### Validation Methods

* 用户测试
* A/B Testing
* 性能测试
* 兼容性测试

### Success Metrics

* Block 生成成功率
* 用户满意度
* 代码质量指标
* 系统响应时间

### Feedback Collection

* 用户反馈表单
* 使用数据分析
* 错误报告系统

## Risks & Mitigations

### Potential Risks

* AI 生成结果不稳定
* AI 生成结果的安全性
* AI 生成结果的可靠性
  
### Risk Management

* 完善的错误处理机制
* 性能优化策略
* 详细的文档和教程
* 成本监控系统

**碎片化风险**：如果生态中出现大量质量参差不齐的Block，如何避免形成"Block地狱"，反而增加了用户的选择成本和学习负担？

Block 是 AI 筛选的，并且是 AI 生成的。自产自销经过验证后再做组合，模式本身并非问题。

深度思考：AI筛选和自产自销的闭环虽然理论上可行，但缺乏具体实施机制。建议引入：

* 基于使用频率和用户满意度的Block排名算法
* 自动合并相似功能Block的推荐机制
* 建立Block生命周期管理，包括废弃过程
* 设计Block分类与发现系统，而非简单列表
* AI需要双重角色：既是创造者也是管理者，确保生态健康。

---

**技术债务累积**：如果生成的Block没有良好的可维护性设计，长期来看会不会创造出大量无人维护的"僵尸Block"，反而增加了系统的技术债务？

> 通过市场和私有插件的模式来解决，市场是最好的老师，符合经济学原理。

* 实施Block健康指标监控（使用频率、错误率、兼容性问题）
* 建立自动化Block升级和迁移路径
* 设计Block依赖关系图，识别关键节点
* 引入Block生命周期管理，包括正式的废弃过程和存档政策
* 防止技术债务的关键是前瞻性设计和持续监控，而非完全依靠市场淘汰。

## Feedback Loops

see -> AIGC version -> `./ai-block-composer.elaboration/ai-block-ecosystem-feedback-loops.md`

* 高质量 Blocks 数量 + -> AI 学习更容易生成 -> 高质量 Blocks 数量 + -> 更多用户和生态进入贡献 -> 高质量 Blocks 数量 +
* 开源 / 社区运营 -> 高质量的 Blocks 数量 + -> 更多用户和生态进入贡献 -> 高质量 Blocks 数量 +
* 商业模式激发 / 创作者奖励机制 -> 更多用户和生态进入贡献 -> 高质量 Blocks 数量 +
* AI 能力 & 模型水位 / 工具层 Code Engineering + -> 高质量 Blocks 数量 +
* 野蛮发展和推广安装 -> 更多用户和生态进入贡献 -> 高质量 Blocks 数量 +
* 生成准确度提升（Context Engineering）+ -> 高质量 Blocks 数量 +

## Iterations & Future Work

start the project with repo in public registry -> `https://github.com/dingtalk-yida/ai-composers`

## References

* Material Design System
* Ant Design
* OpenAI Documentation
* React Component Patterns
* Atomic Design Methodology
