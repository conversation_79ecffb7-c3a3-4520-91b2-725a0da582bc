# AI Block Composer

* tool call for AI but can also work in stand-alone mode
* similar to the Claude Artifact, which can use llm & agent to coordinate the block generation from series of tech stack
* similar to V0, component or page generation

## Structure

### I/O

* INPUT -> prompt with useful context
* PROCESS -> agent with llm & tools to call and generate the block (artifact)
* OUTPUT -> ui block host in Yida and can be visited by url

### Architecture

* server architecture first
* display not as constraints
