# AI Code Generation Design

- [production comparison](https://alidocs.dingtalk.com/i/nodes/m9bN7RYPWdyrPBREcQn3RbdXVZd1wyK0)

## Design Goals

Artifacts Generator:

- gen app
- gen page
- gen component

### Key Metrics

- ability-boundary -> boundary of its ability to cope with key scenarios
- accuracy -> accuracy of its ability
- latency -> fast / faster / fastest
- cost -> relatively low cost
- user-experience -> atheistic design / ease of use / smooth experience
- aha-moments -> great impression on generated artifacts in surprising way

## Product Design

### Target Users

- normal business users -> no-code / low-code
- developers (delivery oriented) -> low-code / source-code
- pro developers -> source-code

### Target Scenarios

scenario target is used `meta/template/prd.user.story.mdx`.

e.g. As a [type of user], I want to [perform an action/achieve a goal] so that [I get this benefit/value].

> Find the CORE scenarios to do the PMF works of DingTalk AI Assistant

2C Scenarios:

- `Headless CMS` build content management system
- `Landing Page` build landing page
- `Long-tailed App` build long-tailed app, such as draw-lottery, vote, survey, resume collection etc.

2B Scenarios:

- `Delivery based Customization Pages` based on <PERSON><PERSON>'s existing system, to evolve new customized pages for customers' needs

---

customers co-creation scenarios:

[key scenarios](https://alidocs.dingtalk.com/i/nodes/EpGBa2Lm8aZxe5myCoXDP7AnWgN7R35y?corpId=dingd8e1123006514592&iframeQuery=sheetId%3DeIuTLu5%26viewId%3D1RWTqBO&sideCollapsed=true)

## Technology Design

### Tech Dimensions

- `Agentic` runtime -> Schedule tools and tasks to complete the goal of user's intension
- `Prompt` selector / optimizer -> user's intension accuracy
- `Template` / `Solution` / `Scenario` selector -> nice by default
- `TechStack` selector -> DX -> Generative UI Tech factors overview
- `VisualDesignGuide` selector -> UX
- `Tools` selector -> system tools + user tools -> Abilities Enrichment

Compose above elements into an `Agent` flow to generate ai-related things.

### Architecture Design

- basic abilities encapsulation
  - agentic runtime / services
  - prompting debugger / manager -> visualize the prompt and its result
  - RAG services
  - llm services
  - tools / MCP services
    - dev resources -> (media-files) images / icon / fonts / videos / audios / etc.
    - related content tools -> problem related content tools -> search / user-data / professional domain tools / knowledge-base
- core code-gen architecture
  - designer / generator runtime -> [Web Container and Web Console](./web-container.tech.design.md)
    - designer compile / bundler architecture
  - yida runtime -> leverage yida's runtime pre-existed abilities
    - rich and dynamic tech-stack runtime
    - dynamic Yida Assets loader
      - [yida-assets](./ai-canvas.assets.design.md)
  - server runtime -> serverless
  - context-engineering -> organize context for AI to understand and generate
    - prompt-engineering: ai-understand-context
    - long-term and short-term memory
  - file edit -> basic re-edit skill
  - workspace edit -> virtual multiple-file workspace skill
  - progressively / parallel-execution -> faster and smoother user experience
- data / logic services architecture
  - metadata encapsulation (server)
  - business rules / logic encapsulation (server)
  - third party services integration (server)

### Generative UI Tech Factors Comparison

#### 1. Basic Tech Adoption

| Category             | Option 1                | Option 2                | Option 3          | Selected Option            |
| -------------------- | ----------------------- | ----------------------- | ----------------- | -------------------------- |
| HTML Approach        | `JSX`                   | `HTML`                  | -                 | `JSX` ✅                   |
| CSS Strategy         | `TailwindCSS`           | `CSS Native`            | `CSS in JS`       | `TailwindCSS` ✅           |
| JavaScript Framework | `Lit` (WebComponents)   | `React` (VDOM + JSX)    | `JS Native`       | `React` ✅                 |
| Rendering Strategy   | `Server-side rendering` | `Client-side rendering` | `HybridRendering` | `Client-side rendering` ✅ |

**HTML Approach Comparison:**

- **AI Friendliness**: JSX (⭐⭐⭐⭐) offers better structure and component composition than HTML (⭐⭐⭐), making it easier for AI to generate coherent UI code.
- **Performance**: JSX (⭐⭐⭐⭐) transforms efficiently into optimized JavaScript, while HTML (⭐⭐⭐) requires additional parsing.
- **Developer Experience**: JSX (⭐⭐⭐⭐) provides superior component composition and type safety compared to HTML (⭐⭐⭐).

**CSS Strategy Comparison:**

- **AI Friendliness**: TailwindCSS (⭐⭐⭐⭐⭐) with its utility classes is highly predictable for AI generation compared to CSS Native (⭐⭐⭐) or CSS in JS approaches.
- **Performance**: TailwindCSS (⭐⭐⭐⭐) provides good runtime performance with its optimized stylesheets, outperforming traditional CSS (⭐⭐⭐).
- **Developer Experience**: TailwindCSS (⭐⭐⭐⭐) offers rapid development with predictable styling patterns, better than native CSS (⭐⭐⭐).

**JavaScript Framework Comparison:**

- **AI Friendliness**: React (⭐⭐⭐⭐⭐) has extensive documentation and patterns that AI understands well, compared to Lit (⭐⭐⭐) and JS Native (⭐⭐).
- **Performance**: JS Native (⭐⭐⭐⭐⭐) has the best raw performance, followed by Lit (⭐⭐⭐⭐), with React (⭐⭐⭐) having more overhead.
- **Developer Experience**: React (⭐⭐⭐⭐⭐) offers the best ecosystem and tooling, followed by Lit (⭐⭐⭐), with JS Native (⭐⭐) requiring more manual work.

**Rendering Strategy Comparison:**

- **AI Friendliness**: Client-side rendering (⭐⭐⭐⭐) is more straightforward for AI to generate compared to SSR (⭐⭐⭐).
- **Performance**: Server-side rendering (⭐⭐⭐⭐⭐) provides better initial load performance and SEO compared to CSR (⭐⭐⭐).
- **User Experience**: Server-side rendering (⭐⭐⭐⭐) offers better perceived performance on initial load, while CSR (⭐⭐⭐) excels in interactivity after load.

#### 2. Engineering Complexity

| Category              | Option 1        | Option 2                     | Option 3                       | Option 4            |
| --------------------- | --------------- | ---------------------------- | ------------------------------ | ------------------- |
| JS Module System      | `ESM`           | `CJS`                        | `AMD / UMD`                    | `Module Federation` |
| Packaging Strategy    | `Bundler`       | `No Bundler`                 | -                              | -                   |
| Artifact Organization | `single-file`   | `multi-files`                | `monorepo / project-structure` | -                   |
| Dependency Management | `umd injection` | `dynamic install and bundle` | `pre-bundled`                  | hybrid              |

**JS Module System Analysis:**

- **Complexity**: ESM (⭐⭐) is simpler than CJS (⭐⭐⭐), while AMD/UMD (⭐⭐⭐⭐) and Module Federation (⭐⭐⭐⭐⭐) are more complex.
- **Scalability**: Both ESM (⭐⭐⭐⭐⭐) and Module Federation (⭐⭐⭐⭐⭐) excel at scaling applications, with CJS (⭐⭐⭐) and AMD/UMD (⭐⭐⭐) being less optimal.
- **AI Generation Ease**: ESM (⭐⭐⭐⭐) is most familiar to AI systems, followed by CJS (⭐⭐⭐), AMD/UMD (⭐⭐⭐), with Module Federation (⭐⭐) being challenging.

**Packaging Strategy Analysis:**

- **Complexity**: Using a bundler (⭐⭐⭐⭐) introduces more complexity than going bundler-free (⭐⭐).
- **Scalability**: Bundlers (⭐⭐⭐⭐⭐) offer better code organization for large projects versus no bundler (⭐⭐).
- **AI Generation Ease**: No bundler (⭐⭐⭐⭐⭐) simplifies AI code generation compared to requiring bundler configuration (⭐⭐⭐).

**Artifact Organization Analysis:**

- **Complexity**: Single-file (⭐) is simplest, multi-files (⭐⭐⭐) adds moderate complexity, and monorepos (⭐⭐⭐⭐⭐) are most complex.
- **Scalability**: Monorepo structures (⭐⭐⭐⭐⭐) and multi-file (⭐⭐⭐) approaches scale better than single-file (⭐) organizations.
- **AI Generation Ease**: Single-file (⭐⭐⭐⭐⭐) is easiest for AI to generate, followed by multi-files (⭐⭐⭐), with monorepos (⭐⭐) being most challenging.

#### 3. UI / UX Quality

| Category             | Option 1 | Option 2 | Option 3  |
| -------------------- | -------- | -------- | --------- |
| UI Component Library | `MDC`    | `AntD`   | `RadixUI` |

**UI Component Library Analysis:**

- **Feature Richness**: AntD (⭐⭐⭐⭐⭐) offers the most comprehensive component set, followed by MDC (⭐⭐⭐⭐), with RadixUI (⭐⭐⭐) being more minimalist.
- **Customization**: RadixUI (⭐⭐⭐⭐⭐) provides the highest level of customization, compared to both MDC (⭐⭐⭐) and AntD (⭐⭐⭐).
- **AI Compatibility**: AntD (⭐⭐⭐⭐⭐) has better documentation and patterns that AI can understand and generate, with MDC (⭐⭐⭐⭐) following closely and RadixUI (⭐⭐⭐) being less represented in training data.

#### 4. Light Technology Stack Choice

| Tech Area | Recommended Approach                                              |
| --------- | ----------------------------------------------------------------- |
| Light JS  | JavaScript + ES Import module                                     |
| Light DOM | Simple HTML or HTML light template engine (SSR or Web Components) |
| Light CSS | TailwindCSS (online interpreter CSS)                              |

**Light Technology Stack Benefits:**

- **Light JS**: Provides simplicity, browser compatibility, and requires no build step. AI Generation Efficiency: ⭐⭐⭐⭐⭐
- **Light DOM**: Offers better performance, progressive enhancement, and SEO friendliness. AI Generation Efficiency: ⭐⭐⭐⭐
- **Light CSS**: Delivers utility-first approach, consistent design system, and reduced output size. AI Generation Efficiency: ⭐⭐⭐⭐⭐

### CodeGen Design Principles

- AI Easy to understand context
- AI Easy to output content (highly **abstracted** and in **condensed** form) as **ARTIFACT**
  - CSS abstraction: `TailwindCSS` -> runtime compiler
  - JS / WebComponents abstraction: `Lit` basic powered by Google WebComponents technology
  - ...
- YidaLess + TechLess (No Bundler, No Compiler, No Build, just pure form of HTML, CSS, JS)
- Try **Modular** system: `umd` or `es` or `mfs` standard to do dynamic import functionality modules
  - umd: `react` / `lodash` / `moment` / `antd` / `chart.js` / ...
    - animation lib. / canvas engine
  - esm: `lit` / ...
  - ✅ mf: module-federation runtime loader -> load Yida's federation component module
- Stay FOCUS on `VISUALIZATION` and `LIGHT_INTERACTION` scenarios, pick the most impacted ones to do PMF works
- Compile or not is based on the specific scenario

### Core Technology Strategies

> For better experience

- 🆒 support `Progressively` render the generated UI blocks & components
- template driven RAG for 2/8 high-frequent scenarios
  - AI Block is `RAGed` for `aesthetic` and `interactivity` pre-defined blocks
- use agentic skills to instruct AI to expand user's prompt into professional prompt to better use AIGC tools for web-pages / apps
- integrate more high-quality libs and tools for web pages -> Headless CMS is a way out
- engineering optimization to divide a problem into multiple steps: use different tools, agents, models(LLM) to digest

### Browser Powered Bundler Tech Design

[Web Container and Web Console](../ai-canvas/core/web-container.tech.design.md)

we choose `esbuild-wasm` to do the job of `esbuild` in the browser.

### Prompting Engineering

see code-gen source code.

## Implementation Details

### Basic libs. import for basic skills of WebPage Artifacts

basic:

- ✅ `React` -> JSX based for HTML abstraction
- ✅ `AntDesign` -> React Component Library -> `ShadCN` derived community widgets
- ✅ `TailwindCSS` online interpreter -> atomic css (reduce output size and clean style)
- ✅ `Chart.js / ReCharts` -> have better encapsulated one? like AntV family?
  - `AntV` family libs.
- canvas 2d-context APIs abstraction -> Native Canvas v.s. lib encapsulation?
  - ✅ `d3.js` -> math focused diagram rendering or other diagram rendering libs.
- animation libs. to enrich the expression of our Canvas -> what is the best choice for AI to use and understand
  - UI Animation
    - ✅ `Motion.js` -> a production-ready motion library for React
    - `Framer Motion` -> a production-ready motion library for React
    - `anime.js` -> Clear API, good for both simple and complex animations
  - Graphic based Animation -> see Math or Physics chapter
- Visual Effects -> cool / awesome effects
- `Diagram Render` -> PlantUML x ✅ Mermaid
- **3D Rendering Scenario** -> create 3D Material to load / work and in a better way -> this may actually make impactions on the effect.
  - `three.js` -> encapsulation
    - `react-three/fiber` -> react integration
    - ECS Frameworks x `three.js` -> 3d rendering engine
  - `Babylon.js` -> 3d rendering engine
  - online 3d-models or 3d-materials loading for crafting

biz modules / federated modules:

- ✅ `markdown` plugin component -> federated module
- `AMap` plugin component to render and collaborate (server token exchange)
- 🤩 `Landing Page Sections` plugin to abstract production page -> UIPaaS replacement design

### Math Teacher's Teaching Assistant

- ✅ Math Expression Render (LateX)
- MathBox -> <https://github.com/unconed/mathbox?tab=readme-ov-file> -> math visualization tool
- `f(x)` plot and painting
  - `Manim.js` -> <https://github.com/JazonJiao/Manim.js/> + `P5.js` powered math focused diagram animation / rendering
  - `Function Plot` -> math function plot
- `Math.js` x `Plotly.js` -> math function plot
- `JSXGraph` -> math function plot
- `GeoGebra` -> <https://www.geogebra.org/> -> math visualization tool
