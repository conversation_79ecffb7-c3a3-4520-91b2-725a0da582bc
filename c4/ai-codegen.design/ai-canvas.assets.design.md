# AI Assets Design

## Asset Specs

> AI Understandable Components and Assets

Use low-code schema driven components to make AI understandable code.

solution here: https://xmtrf1.aliwork.com/APP_DZNV9RF3026B47VCKD1Z/workbench/FORM-8C33F75657DD41C7A91C899842A537EDQ4I1

## VC Components

Existed yida's `vc-components` and they are injected into window so you can `reflection` it to generate the prototype and related spec to make AI understandable code.

Core strategies: *Runtime Prototype Reflection* @XiaYang.


## LowCode Components

Schema driven custom components.

* user or organization defined components
* ai components / connector components

## Plugin (SourceCode) Components

Plugin driven form-based components share the same architecture with `vc-components`.
