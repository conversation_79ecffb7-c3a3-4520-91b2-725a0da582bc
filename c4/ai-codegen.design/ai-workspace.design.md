# AI Workspace Page Editing Design

## Context Design

* scenario context -> page context from `AI Canvas` main system-prompt
* file-dir in flatten structure
* current editing file
* related file / fragments contents as full-context

### File Organization Design

* `page.jsx` final page component output which stands for the entry file
* `*.component.jsx` -> component for `page.jsx` to compose and use, can be generated in parallel

## Tool Design

* create file
* edit file
* delete file

## Tech Specs

* [optional] use CodeGen planning agent architecture
* [optional] use <PERSON><PERSON> to run parallel tasks in agentic process for mandatory steps
* use `deepSeek v3` as main LLM to generate code
* use `deepSeek R1` as Reasoning LLM to plan and design
* use `WebContainer` x webpack as in-browser bundler [webc](./web-container.tech.design.md)

### workflow

first time code generation:

* accept context / user original input
* generate internal tool operations / tasks
  * generate the page PRD and composed plan
    * design the page spec (ruled here to guide AI generation for task division)
    * design the component spec list (props / state / context / hook / etc.) how to be used and composed
    * design the sub-tasks execution plan in sequence (serial or parallel)
  * generate each component code with plan context / scenario context / component generator context
  * generate the page code with component code and plan context / scenario context / each component source code together
* `webc` compile the page code and generate the bundle file with webpack
* call existed preview page to render the page and call existed flow

code editing:

* quote page and add modification instructions
* generate internal tool operations / tasks
  * full existed ctx extraction as context
  * generate the modification plan
* apply diff or add new component code in different file or module

### Cases

* each component compile error / eslint error and wrapped in Error Boundary

## Scenario Design

* generate / editing complex `production` landing page
