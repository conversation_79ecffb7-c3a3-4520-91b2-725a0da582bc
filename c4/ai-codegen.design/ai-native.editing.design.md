# AI Native Editing Design

Make editing AI generated code more friendly and natural just use double-click to edit the content.

## Principles

* LLM is a the best `diff-editing` tool, much better than traditional ast, or code tag mechanism, prompting the VIBE coding experience
* leverage the browser's native ability for the editing, `contenteditable` is the best

## Core Design

### Patterns for Modifications

* **direct chat**: for complex whole page modifications, and general modifications, such as theme, language, etc.
* **select area and chat**: for partial modifications, such as code editing, content editing, etc. When user start the selector mode, cursor can highlight the selected area, like Chrome DevTools. inspect element feature, when select the area, lt is highlighted in the page. Quick chat with LLM to do the modifications. Using area's elements info as context.
* **static text editable**: for static text, such as title, description, editable-area, with direct editing and cursor-based editing. No double-click needed. Just enable the editing mode, and do operations, and then save the changes via LLM to source code.

## Technical Implementation

### Direct Chat

Already implemented in the `ai-code-gen` main application use normal and editing approach in LLM file handling.

### Select Area and Chat

INFORMATION provided to the LLM:

Selected Element: <div class=​"container mx-auto px-6" data-spm-anchor-id=​"a2q5p.26710651.0.i2.2a31f06dkmuLzx">​…​</div>​ <- full dom tree is printed recursively
Element TagName: DIV
Element Classes: container mx-auto px-6 element-highlight
Element ID:
Element Text: 客户评价值得信赖的平台数千家企业正在使用宜搭，他们的成功就是我们的目标宜搭帮助我们将HR系统开发周期从3个月缩短至2周，极大提升了人力资源管理效率。张经理人力资源总监科技创新集团作为非技术人员，我也能轻松使用宜搭创建业务应用，这彻底改变了我们的工作方式。李总监运营负责人未来零售有限公司宜搭的数据分析能力让我们能够实时监控业务运行情况，为决策提供了有力支持。王董事首席执行官智慧医疗科技数千家企业的共同选择阿里巴巴腾讯百度华为京东字节跳动
Element Path: div#App > div.vc-shell-without-nav.vc-page-content-1180.is-desktop > div.render-engine-container > div.vc-page-yida-page.vc-page.page_m8o41dj2.vc-page-yida-container-origin > div#vc-procode-comp > div.radix-themes.dark > div.min-h-screen.bg-black.text-white > div.bg-gradient-to-b.from-black.to-blue-900.text-white.py-20 > div.container.mx-auto.px-6.element-highlight

* DOM information cleaning recursively

---

* user enter the selecting mode
* user select the area like chrome devtools
* user stop selecting and get the information
* user ask for modifications
* LLM apply the changes to the source code

### Static Text Editable

* only make the text-element editable
* filtered dynamic elements in code-gen with marked properties

INFORMATION provided to the LLM:

* user enter the editing mode
* user hover to highlight the editable text-element
* user double-click to edit the text-element
* logger track the changes and diffs
* user stop editing and sync the changes to LLM
* LLM apply the changes to the source code

## Reference

* Google DeepResearch file -> see <https://docs.google.com/document/d/1V_GtzkMyPkX6KNRibdgvAzWVYfkTDQFpnbIZaZlSeGU/edit?tab=t.0>
