# Web Container and Web Console

* [Web VM Technology](https://github.com/leaningtech/webvm?tab=readme-ov-file) try to run the webvm in the browser like a truly POSIX-like runtime x Linux-like runtime
* [Web Container](https://webcontainers.io/guides/introduction) try to run the web container and node.js in the browser
* [FaaS] for one-time es-build / babel compile for source-code to runtime-code in browser
* 👍🏻 [Alibaba-Web-Container] by using Alibaba's web container to run the web container and node.js in the browser -> `webc`
* ✅ [esbuild-wasm] use esbuild wasm to compile the source-code in browser (build x transform x bundle)
* ✅ [babel-standalone] use online babel-transform mechanism (final fallback when no esbuild-wasm available)

## WebC

* @XiaYang offer the docs and solutions for the compiler to work with the webc

## ESBuild-WASM

```tsx
import esbuild from "esbuild-wasm";

// Initialize esbuild-wasm before using it
let initialized = false;
let isInitializing = false;
let pendingPromise: Promise<void> | null = null;

async function initializeEsbuild() {
  if (isInitializing) {
    return pendingPromise;
  }
  if (!initialized) {
    isInitializing = true;
    pendingPromise = esbuild.initialize({
      wasmURL: "https://unpkg.com/esbuild-wasm@0.25.1/esbuild.wasm",
      worker: true,
    });
    await pendingPromise;
    initialized = true;
    isInitializing = false;
  }
}

export async function transformJSX(jsx: string) {
  await initializeEsbuild();
  const result = await esbuild.transform(jsx, {
    // jsx: 'preserve',
    jsxFragment: "Fragment",
    loader: "jsx",
    target: "es2015",
  });
  return result.code;
}

export async function buildJSXBundle(jsx: string) {
  await initializeEsbuild();
  const result = await esbuild.build({
    stdin: {
      contents: jsx,
      loader: "jsx",
      sourcefile: "index.jsx",
    },
    bundle: true,
    external: [
      "react",
      "react-dom",
      "antd",
      "dayjs",
      "@ant-design/icons",
      "chart.js",
      "lodash",
    ],
    outfile: "bundle.js",
    format: "esm",
    define: {
      "process.env.NODE_ENV": "'production'",
    },
    plugins: [
      {
        name: "external-globals",
        setup(build) {
          // Map imports to global variables
          build.onResolve({ filter: /^react$/ }, (args) => {
            return { path: args.path, namespace: "external-globals" };
          });
          build.onResolve({ filter: /^react-dom$/ }, (args) => {
            return { path: args.path, namespace: "external-globals" };
          });
          build.onResolve({ filter: /^antd$/ }, (args) => {
            return { path: args.path, namespace: "external-globals" };
          });
          build.onResolve({ filter: /^@ant-design\/icons$/ }, (args) => {
            return { path: args.path, namespace: "external-globals" };
          });
          build.onResolve({ filter: /^chart\.js$/ }, (args) => {
            return { path: args.path, namespace: "external-globals" };
          });
          build.onResolve({ filter: /^lodash$/ }, (args) => {
            return { path: args.path, namespace: "external-globals" };
          });
          build.onResolve({ filter: /^dayjs$/ }, (args) => {
            return { path: args.path, namespace: "external-globals" };
          });

          // Provide global variable references
          build.onLoad(
            { filter: /.*/, namespace: "external-globals" },
            (args) => {
              let contents = "";
              if (args.path === "react") {
                contents = "module.exports = window.React;";
              } else if (args.path === "react-dom") {
                contents = "module.exports = window.ReactDOM;";
              } else if (args.path === "antd") {
                contents = "module.exports = window.antd;";
              } else if (args.path === "@ant-design/icons") {
                contents = "module.exports = window.icons;";
              } else if (args.path === "chart.js") {
                contents = "module.exports = window.Chart;";
              } else if (args.path === "lodash") {
                contents = "module.exports = window._;";
              } else if (args.path === "dayjs") {
                contents = "module.exports = window.dayjs;";
              }
              return { contents, loader: "js" };
            },
          );
        },
      },
    ],
  });
  console.log(result);
  return result.outputFiles;
}
```