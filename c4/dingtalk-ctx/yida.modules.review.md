# Yida 运行时重点回归的模块

## 模式回归

* 多语言：中文 / English
* Pod 化：Pod 化版本（集群版本）
* 海外：海外版本（新加坡节点）
* 元数据：2.x 版本 / 3.x 版本
* 集团版本：2.0 版本 / 3.0 版本
* 公式：公式 2.x 版本 / 公式 3.x 版本
* SaaS 应用：SaaS 版本（只安装，不可修改模式）
* 客户端：客户端内（普通）/ 客户端内（专属） / Web 浏览器内
* 存储模式：专属存储 / 公有云存储
* 组件包加载：`Mini` 组件包 / 标准组件包

## 模块回归

* 设计器
  * 表单设计器
  * 流程设计器
  * 表单详情设计器
  * 流程详情设计器
  * 表单自定义详情设计器
  * 流程自定义详情设计器
  * 门户设计器
  * 空间设计器
  * 自定义组件设计器
  * 报表设计器
  * 聚合表设计器
* 运行时
  * 表单提交页面
  * 表单详情页面
  * 表单自定义详情页面
  * 表单数据管理页面
  * 流程提交页面
  * 流程详情页面
  * 流程自定义详情页面
  * 流程数据管理页面
  * 自定义页面
  * 门户页面
  * 空间页面
  * 自定义组件预览页面
  * 报表页面 / 预览
  * 聚合表页面
* 管理后台
  * 插件模块
    * 插件市场
    * 插件开发
    * 插件管理