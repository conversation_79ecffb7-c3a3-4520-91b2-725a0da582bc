# AI Team Infra Design

## UX Components

* AI Button (Floating / Inline / Icon / MagicWand)
* AI Loading (Spinner / Skeleton / Loading)
* AI Prompt Input / Editor -> support Fields Referencing
* AI Generative Rendering (Markdown powered) -> support `Table` & complex rendering view & event the future tool and other invocations
* AI Field Picker / Field List Picker -> support Fields Referencing
* AI Preset Prompt List -> Provide Prompting for different scenarios

---

* AI Chatting -> support chat UI scenario

## Context Engineering

* `.cursor` principles
* `.context` folders for context files

## Yida AI Scenarios APIs

* AI FormData Writer
* AI FormData Reader -> use traversal to get the form data
  * traverse apis -> support slots and more complex data structures

## AI Request

* LLM direct call
  * models
  * modelsParams
  * streaming
  * generate images / videos / music
* LLM call with tools
  * with files
  * with images
  * with web-search
  * with voice
  * with video
* Agent API Call
  * call agent directly

## AI Server APIs

* LLMOps provided APIs

## Basic Functionality

* RAG Services (doc2bot encapsulation)

## MCP Extensions

* Yida MCP Extensions -> AI tool invocation encapsulation
  * Yida MetaData Fetcher
* Text 2 Image MCP
* Agent as MCP
* Types declaration file as MCP

## Metadata Context Utils

* Yida FormFields MetaData Provider -> schema -> AI well-known fields
* Yida Workflow MetaData Provider -> schema -> AI well-known flow-nodes
* Yida Formula MetaData Provider -> schema -> AI well-known formula-nodes
* ...

## Usage Scenarios

* AI UI Test Automation
* AI UnitTest Generations
* AI `Agentic` FrontEnd Mechanism
  * AI Release Master
  * AI Release Notes
