# AI CodeGen Task Board

## Production Design & Thinking

- provide `Normal` mode and `Expert` mode for gradient product experience
  - `Normal` mode -> nice by default 2/8 high-frequency scenarios with RAG and preset complex prompting engineering
  - `Expert` mode -> least pre-set content in prompt and user dominated all generation

## Prompting Engineering

- [3] try to learn from main-stream [AI code-gen tools](https://github.com/x1xhlol/system-prompts-and-models-of-ai-tools/tree/main)

## AI Native Editing Experience

[AI Editing](./ai-codegen.design/ai-native.editing.design.md)

## Complex Page Generation & Workspace

[AI Workspace](./ai-codegen.design/ai-workspace.design.md)

## Stability Concerns

- [2] [page-type-variations] may support more types of page generation in Yida
- [3] [html] pure HTML based code-gen

## Aha Moments

- [3] [21-first-century] research on `21-first-century` to make the AI-generated code dev UX friendly
- [3] [libs] unleash the power umd packages insert to DOM for the AI to use

## Trace

[2025.05]

- [x] [code-gen] re-integrate diagram data query to custom-page generator
- [x] [code-gen] bugfix stable and reliable for code gen
- [x] [code-gen] prepare stable runtime / designer for code-gen test / debug
- [x] [code-gen] PRD support modification in exec-flow
- [x] [code-gen] screen recording for 3-most scenarios of code-gen -> high-quality x find bugs x effects -> info-collection, quiz
- [x] [code-gen] bugfix for reliable and stable code-gen in progress, have the internal Claude / qwen-3 models for testing
- [x] [code-gen] ✨ explore the selective editing and apply editing to the selected area with AI for possibility and try to implement it in MVP demo
- [x] [code-gen] ai-native editing MVP feature for demo and sample usages
  - [x] apply and see patched diff result in preview mode
  - [x] connect and communicate with parent window for selecting and static-content editing
    - [x] simple page chat-mode
    - [x] complex agentic-mode
  - [x] dynamic data-class and often-used data-class for dynamic-content disabled and meaningful locator
- [x] [code-gen] demo-show and communication for ai with open-platform tech team and become the core character in the team
- [x] [code-gen] dive and elaborate the future forms of ai-code-gen of current condition
  - fully elaborate the production & SWOT analysis
  - deep think about the production core of code-gen
- [x] [code-gen] optimize modification not-saved issue and optimize the code-gen save logic

---

* [x] move prompting sys. into backend next.js driven system
* [x] stable the `code-gen` dingtalk-qwen-max step_complete call (function call stabilities)
* [x] try to use new tool `UpdateCode` to do code modification - 05-22
* [x] add tests for key-server ssr code
* [🤣] fix qwen3 dingtalk invocation issue: **double-transform** and **tool-call** format bugs
- [x][code-gen] online release prepare: agentic and tool-prompt system-prompt hiding - 5.26
- [1c][code-gen] dev-panel and log-panel optimization for MVP version online
- [x][code-gen] prepare the release doc for online release of code-gen system V0.1
- [x][code-gen] more tests, more stability for code-gen system, and prepare for the online release - 5.28
- [x][code-gen] online release prepare: React 18 as another instance in the same page experiment
- [x][code-gen] release the ai-canvas, assets, and deploy the online services
