@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('./posts.css');
@import url('./background.css');

body {
  font-family: var(--font-inter), system-ui, -apple-system, BlinkMacSystemFont,
    'Segoe UI', Robot<PERSON>, 'Helvetica Neue', <PERSON><PERSON>, 'Noto Sans', sans-serif,
    'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
  -webkit-font-smoothing: subpixel-antialiased;
  font-feature-settings: 'case' 1, 'cpsp' 1, 'dlig' 1, 'cv01' 1, 'cv02',
    'cv03' 1, 'cv04' 1;
  font-variation-settings: 'wght' 450;
  font-variant: common-ligatures contextual;
  letter-spacing: -0.02em;

  /* Define CSS Variables */
}
b,
strong,
h3,
h4,
h5,
h6 {
  font-variation-settings: 'wght' 650;
}
h1 {
  background-image: linear-gradient(55deg, #38b7f2 0%,#833cf6 20%);
  background-clip: text;
  text-fill-color: transparent;
  background-size: 100%;
  background-repeat: repeat;
  font-variation-settings: 'wght' 850;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent; 
  -moz-background-clip: text;
  -moz-text-fill-color: transparent;
}
h2 {
  font-variation-settings: 'wght' 750;
}

@media screen and (min-device-pixel-ratio: 1.5),
  screen and (min-resolution: 1.5dppx) {
  body {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
}

details summary {
  cursor: pointer;
}

img.next-image {
  margin: 0;
}

.prose a {
  color: #0074de;
}

.nav-line .nav-link {
  color: #69778c;
}


/* Basic Card */

@media screen and (width <= 600px) {
  .basic-card-list {
    justify-content: center;
  }
}

.basic-card-list {
  display: flex;
  flex-wrap: wrap;
  flex-direction: row;
  align-items: center;
}
.basic-card {
  display: inline-block;
  margin: 0 24px 24px 0;
}
