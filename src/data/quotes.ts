import { QuoteCardProps } from "@/components/QuoteCard/QuoteCard";

export const quotes: QuoteCardProps[] = [
  {
    id: "1",
    text: "The best way to predict the future is to invent it.",
    author: "<PERSON>",
    date: "2023-05-15",
    link: "/quotes/1"
  },
  {
    id: "2",
    text: "Simplicity is the ultimate sophistication.",
    author: "<PERSON> da <PERSON>",
    date: "2023-06-20",
    image: "https://images.unsplash.com/photo-1618331835717-801e976710b2?q=80&w=2000&auto=format&fit=crop",
    link: "/quotes/2"
  },
  {
    id: "3",
    text: "Design is not just what it looks like and feels like. Design is how it works.",
    author: "<PERSON>",
    date: "2023-07-10",
    youtubeUrl: "https://www.youtube.com/watch?v=GEPhLqwKo6g",
    link: "/quotes/3"
  },
  {
    id: "4",
    text: "It's not a bug, it's a feature.",
    author: "Anonymous Programmer",
    date: "2023-08-05",
    link: "/quotes/4"
  },
  {
    id: "5",
    text: "The most disastrous thing that you can ever learn is your first programming language.",
    author: "<PERSON>",
    date: "2023-09-12",
    image: "https://images.unsplash.com/photo-1516259762381-22954d7d3ad2?q=80&w=2000&auto=format&fit=crop",
    link: "/quotes/5"
  },
  {
    id: "6",
    text: "The function of good software is to make the complex appear to be simple.",
    author: "Grady Booch",
    date: "2023-10-18",
    youtubeUrl: "https://www.youtube.com/watch?v=8pTEmbeENF4",
    link: "/quotes/6"
  },
  {
    id: "7",
    text: "Any fool can write code that a computer can understand. Good programmers write code that humans can understand.",
    author: "Martin Fowler",
    date: "2023-11-22",
    link: "/quotes/7"
  },
  {
    id: "8",
    text: "First, solve the problem. Then, write the code.",
    author: "John Johnson",
    date: "2023-12-15",
    image: "https://images.unsplash.com/photo-1550439062-609e1531270e?q=80&w=2000&auto=format&fit=crop",
    link: "/quotes/8"
  },
  {
    id: "9",
    text: "The only way to learn a new programming language is by writing programs in it.",
    author: "Dennis Ritchie",
    date: "2024-01-05",
    link: "/quotes/9"
  },
  {
    id: "10",
    text: "Programming isn't about what you know; it's about what you can figure out.",
    author: "Chris Pine",
    date: "2024-01-18",
    image: "https://images.unsplash.com/photo-1555066931-4365d14bab8c?q=80&w=2000&auto=format&fit=crop",
    link: "/quotes/10"
  },
  {
    id: "11",
    text: "Code is like humor. When you have to explain it, it's bad.",
    author: "Cory House",
    date: "2024-02-10",
    link: "/quotes/11"
  },
  {
    id: "12",
    text: "Perfection is achieved not when there is nothing more to add, but rather when there is nothing more to take away.",
    author: "Antoine de Saint-Exupery",
    date: "2024-03-01",
    image: "https://images.unsplash.com/photo-1555949963-ff9fe0c870eb?q=80&w=2000&auto=format&fit=crop",
    link: "/quotes/12"
  },
  {
    id: "13",
    text: "It is a nice day! Cherish and enjoy the moments when you are in the sun!",
    author: "Arno",
    date: "2024-04-01",
    image: "https://images.unsplash.com/photo-1470252649378-9c29740c9fa8?q=80&w=2000&auto=format&fit=crop",
    link: "/quotes/13"
  },
  {
    id: "14",
    text: "Every Taste I Want To Try! (ETIWTT)",
    author: "Arno",
    date: "2024-04-05",
    link: "/quotes/14"
  },
  {
    id: "15",
    text: "May the force be with you!",
    author: "E3OS",
    date: "2024-04-10",
    image: "https://images.unsplash.com/photo-1506318137071-a8e063b4bec0?q=80&w=2000&auto=format&fit=crop",
    link: "/quotes/15"
  },
  {
    id: "16",
    text: "ORDERIFY - We can never achieve perfection, but we can infinitely approach it.",
    author: "Arno",
    date: "2024-04-15",
    link: "/quotes/16"
  },
  {
    id: "17",
    text: "Power resides where men believe it resides.",
    author: "George R.R. Martin",
    date: "2024-04-20",
    image: "https://images.unsplash.com/photo-1569701813229-33284b643e3c?q=80&w=2000&auto=format&fit=crop",
    link: "/quotes/17"
  },
  {
    id: "18",
    text: "Chaos is a ladder.",
    author: "George R.R. Martin",
    date: "2024-04-25",
    link: "/quotes/18"
  },
  {
    id: "19",
    text: "Humble = Getting rid of money, power, and fame.",
    author: "Arno",
    date: "2024-05-01",
    image: "https://images.unsplash.com/photo-1484627147104-f5197bcd6651?q=80&w=2000&auto=format&fit=crop",
    link: "/quotes/19"
  },
  {
    id: "20",
    text: "Stay Hungry, Stay Foolish.",
    author: "Steve Jobs",
    date: "2024-05-05",
    link: "/quotes/20"
  },
  {
    id: "21",
    text: "The Tao produces one, one produces two, two produces three, three produces all things.",
    author: "Lao Tzu",
    date: "2024-05-10",
    image: "https://images.unsplash.com/photo-1519681393784-d120267933ba?q=80&w=2000&auto=format&fit=crop",
    link: "/quotes/21"
  },
  {
    id: "22",
    text: "Finish today's work today.",
    author: "Arno",
    date: "2024-05-15",
    link: "/quotes/22"
  },
  {
    id: "23",
    text: "What is essential is invisible to the eye.",
    author: "Antoine de Saint-Exupéry",
    date: "2024-05-20",
    image: "https://images.unsplash.com/photo-1444703686981-a3abbc4d4fe3?q=80&w=2000&auto=format&fit=crop",
    link: "/quotes/23"
  },
  {
    id: "24",
    text: "Life could be simple.",
    author: "Flow",
    date: "2024-05-25",
    link: "/quotes/24"
  },
  {
    id: "25",
    text: "To build something truly different we need to work in a truly different way!",
    author: "Apple Think Different Campaign",
    date: "2024-06-01",
    image: "https://images.unsplash.com/photo-1542744173-8e7e53415bb0?q=80&w=2000&auto=format&fit=crop",
    link: "/quotes/25"
  },
  {
    id: "26",
    text: "All is well!",
    author: "Arno",
    date: "2024-06-05",
    link: "/quotes/26"
  },
  {
    id: "27",
    text: "All persistence in the world comes from love.",
    author: "Arno",
    date: "2024-06-10",
    image: "https://images.unsplash.com/photo-1518199266791-5375a83190b7?q=80&w=2000&auto=format&fit=crop",
    link: "/quotes/27"
  },
  {
    id: "28",
    text: "The most elegant departure is to run forward without looking back. And the most graceful acquisition is knowing when to let go.",
    author: "Gintama",
    date: "2024-06-15",
    link: "/quotes/28"
  },
  {
    id: "29",
    text: "Change an angle and the world has been changed!",
    author: "Arno",
    date: "2024-06-20",
    image: "https://images.unsplash.com/photo-1527239441953-caffd968d952?q=80&w=2000&auto=format&fit=crop",
    link: "/quotes/29"
  },
  {
    id: "30",
    text: "Self-discipline is the highest form of freedom!",
    author: "Arno",
    date: "2024-06-25",
    link: "/quotes/30"
  }
]; 