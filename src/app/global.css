@tailwind base;
@tailwind components;
@tailwind utilities;

::selection {
  background-color: #47a3f3;
  color: #fefefe;
}

html {
  min-width: 360px;
}

/* General FontSize config */
body {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas,
    Liberation Mono, Courier New, 'Microsoft YaHei', 'PingFang SC', monospace;
}

.prose {
  max-width: fit-content !important;
}

.prose .anchor {
  @apply absolute invisible no-underline;

  margin-left: -1em;
  padding-right: 0.5em;
  width: 80%;
  cursor: pointer;
}

.anchor:hover {
  @apply visible;
}

.prose a {
  @apply transition-all decoration-neutral-400 dark:decoration-neutral-600 underline-offset-2 decoration-[0.1em];
}

.prose .anchor:after {
  @apply text-neutral-300 dark:text-neutral-700;
  content: '#';
}

.prose *:hover > .anchor {
  @apply visible;
}

.prose pre {
  @apply border border-neutral-800 bg-neutral-900;
}

.prose code {
  @apply text-neutral-800 dark:text-neutral-200 px-1 py-0.5 border border-neutral-100 dark:border-neutral-800 rounded-lg bg-neutral-100 dark:bg-neutral-900;
}

.prose pre code {
  @apply text-neutral-800 dark:text-neutral-200 p-0;
  border: initial;
}

.prose img {
  /* Don't apply styles to next/image */
  @apply m-0;
}

.prose > :first-child {
  /* Override removing top margin, causing layout shift */
  margin-top: 1.25em !important;
  margin-bottom: 1.25em !important;
}

code[class*='language-'],
pre[class*='language-'] {
  @apply text-neutral-50;
}

pre::-webkit-scrollbar {
  display: none;
}

pre {
  -ms-overflow-style: none;
  /* IE and Edge */
  scrollbar-width: none;
  /* Firefox */
}

/* Remove Safari input shadow on mobile */
input[type='text'],
input[type='email'] {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

.prose .tweet a {
  text-decoration: inherit;
  font-weight: inherit;
}

table {
  display: block;
  max-width: fit-content;
  overflow-x: auto;
  white-space: nowrap;
}

.prose .callout > p {
  margin: 0 !important;
}

[data-rehype-pretty-code-fragment] code {
  @apply grid min-w-full break-words rounded-none border-0 bg-transparent p-0 text-sm text-black;
  counter-reset: line;
  box-decoration-break: clone;
}

[data-rehype-pretty-code-fragment] .line {
  @apply py-1;
}

[data-rehype-pretty-code-fragment] [data-line-numbers] > .line::before {
  counter-increment: line;
  content: counter(line);
  display: inline-block;
  width: 1rem;
  margin-right: 1rem;
  text-align: right;
  color: gray;
}

[data-rehype-pretty-code-fragment] .line--highlighted {
  @apply bg-slate-500 bg-opacity-10;
}

[data-rehype-pretty-code-fragment] .line-highlighted span {
  @apply relative;
}

[data-rehype-pretty-code-fragment] .word--highlighted {
  @apply rounded-md bg-slate-500 bg-opacity-10 p-1;
}

[data-rehype-pretty-code-title] {
  @apply px-4 py-3 font-mono text-xs font-medium border rounded-t-lg text-neutral-200 border-[#333333] bg-[#1c1c1c];
}

[data-rehype-pretty-code-title] + pre {
  @apply mt-0 rounded-t-none border-t-0;
}

/* Prose override */
.prose strong {
  @apply font-extrabold;
  @apply font-bold;
  @apply text-blue-600;
  @apply dark:text-blue-400;
}
