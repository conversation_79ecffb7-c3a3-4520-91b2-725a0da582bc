# AI AutoFill / AutoSuggestions  方案设计

## 思考草稿

**Yida AI  表单填写插件原型**

#Yida

最好的体验如何？最最最理想的体验如何定义？

- 基于  yida  已有的单据运作
- 最好基于表单  FormContainer  组件运作，这样改动成本是较低的，运作模式也会比较有趣
- 功能：用户输入一段自然语言或者基于已有的表单填写部分信息。随后  Yida  表单容器浮出填写建议、自动填写的按钮（魔法棒），随后用户点击自动填写，弹出输入框让用户输入如何智能生成。AI  开始运行，吐出字段，填入表单，完成全过程，最好适配  Streaming  输出。
- 交互：Form Container  上增加新的  Button  配置  AI，配置写入到属性上
- 业务：纠错和填写会非常有趣，极大程度上提升效率，创造  +  填写均具备
- 想法：未来应该尽可能适配多模态之能力
- 想法：可以支持点击填写，也可以支持主动生成后填写，比如用户上传了一个  pdf  等类似网红多模能力，然后做字段填写，图片也是如此，甚至一次性上传多个附件

配置包括：

- 技术：支持  quote  的方式索引字段，构筑  systemPrompt
- 技术：调用  deepseek  连接器做思考
- 技术：使用  FormContainer  源信息结构构筑上下文，早期只做支持特定的字段类型填写和提取
- 技术：FormContainer  提供完整的  LifeCycle Hook  或者值变化  Hook  来让表单和  Form  的状态关联起来
- 技术：FormContainer  提供  Slots UI  的能力进行槽位渲染，提供定制化的能力，目标下周二上生产作为预置能力埋入
- 技术：运行时使用  ctx  技术处理
- 技术：instruct  大模型生成特定的字段  id +  值的模式进行填写，随后使用  RenderEngining  的上下文进行动态赋值，完成单据的填写
- 技术：如果需要服务端中转也可以用  faas node.js  甚至用集团的  serverless  作为基础设施来做弹性转发

## 工程

[https://code.alibaba-inc.com/yida-components/yida-plugin-ai-form](https://code.alibaba-inc.com/yida-components/yida-plugin-ai-form)

## 细节设计

### 扩展点设计

扩展点：`yida.designer.form.container`
扩展点：`yida.app.form.container`

### 设计态属性  Setter  扩展

配置  AI  能力，点击之后打开对话框进行配置。

Lazy  模式：必须设计为按需加载之模式。

- `systemPrompt`  支持  Quote  设计器里面已有的表单字段
- `model` 可选配置为  deepSeek / `qwen 200B`
- `triggeredFields` 触发填写字段，当这些字段都被填写之后才触发  AI  按钮，不填默认触发
- `allowUserPrompt` 是否允许用户输入  prompt 如果支持的话，会以  prompt  作为  systemPrompt  的补充，弹层的方式让用户输入

### 运行时扩展

- 表单监听事件钩子  `onFormDataChange` Hooks，注入当前的运行时  ContextAPI
- 表单自动填值  API
- 表单渲染外部  Slot

  - wrapperSlot

- 表单校验 Hook
  - onValidate
  - onValidateSuccess
  - onValidateFail

- 表单提交 Hook

  - beforeSubmit
  - afterSubmit
  - submitFail
  - submitSuccess

- 表单渲染内部

  - 顶部  Slot: topSlot
  - 底部  Slot: bottomSlot
