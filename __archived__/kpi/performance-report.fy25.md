# 述职

## OKR 概览

FY25 S2 聚焦开放和插件技术，0 到 1 建设插件能力，提升交付的带宽和开放能力的上确界，降低原厂定制业务的投入

* 投入插件中心、插件前端研发能力的 MVP 建设，S2 完成主要产品链路和技术链路的落地，稳定 & 高效，无定级故障
* 实现多种类的插件类型，原厂贡献组件 10+，综合类型的插件 Y 个，重点支撑解决二方扩展定制的接入：引力波、多维表和 TB 以插件的形式完成互相能力的增强
* 三方发布后落地插件 K 个，安装 W 次，使用频次 Q，实现初步的插件规模落地

## 工作回顾

### 大客户战役

* 批量删除、修改 -> Data 证明高频率场次
* Yida JSAPI 封装 -> 给出数据，证明日常使用量
* 别名机制 -> 给出数据，证明日常使用量
* 官方项目 -> 动态化架构、插件化支持 & 适配
* 年报项目 -> UV 数据、转化数据

### 高交付战役

自 10 月份插件项目启动以来，经过多轮调研、设计和快速迭代，目前已完成插件生产底座&使用链路的建设，插件支持场域不断扩展，插件种类不断丰富。插件开发现已支持平台功能扩展、组件扩展等大类扩展方式，其中组件扩展支持支持5种组件开发模式以满足各类组件需求，此外即将上线自动化节点扩展。

* 【扩展建设+架构治理】：完成平台20+个场域开放，建设扩展点16个，视图覆盖类表单组件消费场域天然适配，结合架构治理，开发一个新组件的工作量从20人天到7人天（eg. 时区日期区间组件）；
* 【插件解高频问题】：高频需求驱动，上架10+款插件，累计安装量2000+，响应100+客户需求，插件解决的Top10需求占比30%，插件小具规模化；
* 【助力3大战役】：文档&TB&闪记&流程4大插件助力商业化战役完成OA心智升级；时区地址定位4大插件，助力国际化战役完成商务成单关键需求的能力建设；3大AI插件快速上线，助力AI战役开年热度打造；
* 【二三方开放提效】：智能财务深度共创，二方从灵活/高效/体验/兼容层面对插件能力点赞，插件能力支持复杂场景的效果已被验证；随着下周支持三方生态入局，小时级插件生产效率，插件潜力即将激活；

---

* 组件能力扩展：表单容器、附件等
* 平台能力扩展：打印、详情页操作、流程进度插槽等
* 自定义页面组件
* 门户组件
* 表单组件
  * 服务端基础元数据字段
  * 替换元数据字段
  * 组合元数据字段
* 连接组件
  * 6 家客户共创中 -> 低成本地上线更多能力插件 -> https://alidocs.dingtalk.com/i/nodes/QOG9lyrgJPPNL2rXI2xxdG6lJzN67Mw4?corpId=dingd8e1123006514592&iframeQuery=sheetId%3D19zR5RM%26viewId%3DXYdzoC9

---

通过一方、二方、多方合作等开发形式，现已上架10余款插件，累计安装量 2000+（租户），陆续还有多款插件即将上架。插件开发也即将面向三方开发者，届时开发者可以通过低代码方式开发连接类组件，2小时即可开发一个新组件。

插件项目锚定客户诉求，推动一二方共同进行插件研发，利用插件开发的开放性和灵活性，快速上架插件，助力四大战役成功，助力客户成功！

for 高交付战役

* CAD在线看图
* 钉钉云打印🔥
* 打印表单详情页🔥
* 智能财务组件（金额、收付款） -> 二方
* 企业模糊搜索组件 -> 一方 x AI

[向右] 解决交付卡点 & 高频VOC

for 海外战役

* 国际化时区日期组件 -> 一方
* 时区日期区间组件 -> 一方
* 国际地址组件
* 国际定位组件

[向右] 满足国际化协作需要，打通国际化能力包付费卡点

for 商业化战役

* 钉钉闪记查询
* 门户X钉钉文档组件
* 流程拓扑图
* TB门户插件

[向右] OA能力增强

for AI战役

* AI 生成按钮组件 🔥
* AI 智能填表组件
* 钉钉智能助理组件

[向右] 快速AIGC，应用AI化

for 物料增强

* Time 时间组件
* Markdown组件
* 分割面板组件
* Affix 铆钉组件
* 加载容器（Skeleton）
* 面包屑组件
* 移动端 Tabs 组件

[向右] 用户搭建自定义页面有更多选择

在插件开放建设过程中，完成了表单组件适配 20+ 场域，推动服务端和前端进行架构治理。基于此的插件开发模式灵活高效、迭代迅速，一方、二方、三方均可进行插件开发；插件可插拔，平台耦合小，相较传统开发模式，发布更加灵活。

### 前端基础架构

* 体验物料增加 -> AntD BreakThrough、K 个物料更新、安装频次 M 次
* yida 更新动态模块加载系统 module-federation -> 数据和价值证明（插件中心、数据源管理页面、vc-deep-yida）
* yida 插件开放架构：前端 SPI、多种组件开放、多场域开放（扩展点、场域）
* yida 插件动态运行时架构 -> 万物皆可动态化（Everything is Loadable）
* 插件的 composer context engineering -> 效率 Booster -> AI 模式的研发

## 个人总结 & 绩效自评

G:

* 【！插件速度！】插件 x AI 加持的速度 BOOSTER -> 小时级别插件（AGIBlocks）
* 【！深度开放！】开放的 SOP 有了，和 @yeyan 合作，X+ 个场域适配表单体系，实现 1 次开发，N 场域适配，配合服务端做了深度的架构治理，让表单组件的开放突破一方的限制 & 边界，一次适配 N 个场域消费 & 兼容
* 【！二方验证！】二方 PMF -> TB、智能财务，以复杂的表单组件验证开放能力

U:

* 【性能空间】性能的极致优化，目前还缺一些
* 【体验一致性】组件的体验一致性

B:

* 【Link 组件收益不明显】这个决策我觉得有问题（ROI 不高，且容易被 AGI 替代），而且为存量的产品引入了产品复杂度和架构复杂度 -> 其实还不如做三方开放，做个开头带来的收益高
* 【高频场域】短信节点稍微有点遗憾，没有在插件中心上架


## 未来规划

> 一张大图，谈理解与自身定位 📌

* 【开放 x AI 能力 x AI 编码 x 插件】 -> 三方下场，领域纵深，服务 Yida 应用的用户深度场景【AI 开发者 x AI 组织 -> AI 商业】深度共创 N 个可商业化、规模化的标杆场景，并可以让服务商做持续交付の模式
* 【AIBlock Composer】 -> Yida AI 生产力平台，用 AI 来产生 AI Composable 的积木【AI 开发者】-> 完全降低 Yida x 智能体新 UI Block 创建和消费的门槛 -> 向上可供更高层次的 Composer 消费

## 需要的帮助是什么？

* 【智能助理平台】找到杠杆点，撬动 Yida 的开放与能力体系的建设
* 【AI 编码能力】团队需要一起聚力打造 Yida 版本的 Cursor，让不同粒度的 Composer 基于一套架构实现
* 【服务商共创 ideas】优质的服务商共创开放的模式、标杆场景的塑造

---

# 最终录入系统的版本

从 0 到 1 将全代码的插件技术架构在 Yida 落地

职责角色：

插件的前端架构设计师

做了什么：

基于 Federation 动态化技术技术设计实现了插件的运行时框架，注入插件 SDK、解构场域（16+ 扩展点），按需加载。实现了如下的关键能力：

● 组件能力扩展：表单容器、附件等
● 平台能力扩展：打印、详情页操作、流程进度插槽等
● 自定义页面组件：Markdown、落地页、分隔面板组件等
● 门户组件：TB 日程、文档等
● 表单组件
    ○ 服务端基础元数据字段：时区日期、时区日期区间
    ○ 替换元数据字段：时间、收付款
    ○ 组合元数据字段：AI 生成、金额
● 连接组件：配套了低代码设计器，比如：闪记组件
    ○ 6 家客户共创中 -> 低成本地上线更多能力插件

关键产出：

建设扩展点 16+ 个涵盖了表单、流程、自定义页面、数据管理页面、门户等关键场域；视图覆盖类表单组件消费场域天然适配，结合架构治理，开发一个底层元数据自治的新组件的工作量从 20 人天到 7 人天（eg. 时区日期区间组件），开发复杂的表单组件的边际适配成本至少降低 50%+，自动适配 20+ 个场域开放。

---

支撑高交付战役，作为支撑位，利用插件支撑四大战役，提升需求满足效率

我的职责：

插件平台开发者、插件平台能力开发者、插件的开发者

做了什么：

● 【助力 3 大并行战役】：文档&TB&闪记&流程4大插件助力商业化战役完成 OA 心智升级；时区地址定位 4 大插件，助力国际化战役完成商务成单关键需求的能力建设；3大 AI 插件快速上线，助力 AI 战役开年热度打造；
● 【二三方开放提效】：智能财务深度共创，二方从灵活/高效/体验/兼容层面对插件能力点赞，插件能力支持复杂场景的效果已被验证；随着下周支持三方生态入局，小时级插件生产效率，插件潜力即将激活；

关键产出：

高频需求 + 体验物料丰富，双轮驱动，最终上架 20 款插件，组织累计安装量 3200+（截至 2025-03-06），响应 100+ 客户需求（VOC），插件解决的 Top10 需求占比 30%，插件小具规模化；

---

体验技术基础能力建设贡献

职责：为宜搭前端基础能力建设，贡献技术输出

做了什么：

* 通过插件，物料增加，AntDesign 等新的 UI 技术引入到了 Yida 体系中，正式服务客户
* Yida 更新适配了  Module-Federation 动态模块加载系统
插件中心、数据源管理页面（超多场域消费）、vc-deep-yida（基础组件库）
* 给出了 Yida 前端工程的 context-engineering 的雏形，用于辅助生成工程代码和插件代码，AI powered 模式探索

关键结果：

* 25 个前端物料 新上架，整体数量破百
* 数百次 -> 构建 & 发布解耦合，提升产研效率，推动了 Yida 主要的模块升级 yida-build 改善工程体验和治理
* 工单：3 个月， 处理工单 59 个，修复上线 21 个
* 内部 3+ 次分享，2 篇 ATA 产出

---

年度总结：

做得好的：

● 【插件速度】插件 x AI 加持的速度 BOOSTER -> 小时级别插件（AGIBlocks），让插件中心在没有三方进入的情况下，3 个月达到了 20 个，大部分均由我设计实现，造底层的同时，也贡献了 10+ 个高频插件，支撑三大战役的各类需求
● 【深度开放】开放的 SOP 有了，借国际化组件打磨适配场域，20+ 个场域适配表单体系，实现 1 次开发，20+ 场域适配，配合服务端做了深度的架构治理，让表单组件的开放突破一方的限制 & 边界，一次适配 20+ 个场域消费 & 兼容，会让未来复杂表单上架宜搭的「边际成本」大幅降低
● 【二方验证】二方表单组件的 PMF -> TB、智能财务，以复杂的表单组件验证开放能力（e 签宝级别的组件），充分验证了插件能力的有效性，也得到智能财务合作方的认可

还需改进的：

● 【Link 组件、年报项目收益不明显】对所做事情的价值的判断需要增强，需要深度思考的后再输出自己的价值判断，不做业务的 Follower，要做 Leader.
● 【高频场域插件】短信节点稍微有点遗憾，一托再拖，最终没有在插件中心上架，个人有点不甘
● 【速度 x 精品的矛盾】插件带来了速度，但是在场域性能、UI 一致性以及体验深度设计上，瑕疵还是有的，自己未来还需要调和，找到最好的平衡点输出。

总体来说，我认为 S2 三个多月，个人的效率和产出还是「有亮点的」，仅投入一个人的情况下，可以达到从开放的底座建设、各个场域的 SDK 设计和适配，以及最终产出 20+ 插件，整体安装量超过 3200+ 个组织的结果，因此 S2 自评 3.75， 年度 3.5+。

接下来希望在「开放」x「AI」的上一级领域持续发力，在 A.Lab. 的新组织下，发现、组合和创造更多的机会，在 AI 时代下实现 win / win / win !!!

---

年度价值观做得好的
在插件战役中，初始化的速度非常重要，如何快速响应，快速实现初始的启动速度变得非常重要。

我自身在做底层能力开发的同时，充分利用了 AIGC x Yida 插件的能力，将数十个前端驱动的插件，平均一一天一个的速度安装推进，并获得平均 100+ 组织安装的量。

用 AI 实现和推进赋能自身和团队的，主动拥抱 AI，并且在 DeepSeek 火热出圈的时候，第一时间站出来，不到一天的时间，从开发到上线，用插件接入了 DeepSeek，获得了一波流量，实现了超过 1000+ 组织安装 AI 生成组件。体现了「插件速度」也体现了个人主动出击快速应对市场变化，落地有价值的事情的主动性和决心。

再举例口味王的富文本打印的需求，个人为了解决富文本打印的难题，基于插件的模式，快速响应，解决了口味王对富文本类的内容打印的诉求，为破单奠定基础。

年度价值观需要继续努力的
跨团队，跨业务的影响力还需要增加，目前看下来主要闭环在了 Yida 的内部，接下来需要更好地走出去，去客户、去服务商、去友方团队创造 win-win 的机制，互利共赢。

---

Final Result:

3.5 / High Potential

---

思考：

* 整体结果，肯定不满意，因为自己对自己预期比较高，核心问题还是在业务结果的影响力和产出上吧，宜搭整体绩效是 `3.5-` （去年是 3.5+），所以年终的 package 肯定不高，略微遗憾。
* 给了高潜算是安慰了，肯定了潜力系数，但是当下的结果还是偏弱的，唯一的解释就是前半年缺少作品，可以理解，但是也反向客观地说明你的 Leader 在结果上还是摆正了。但是潜力上还是有一定扶正的。
* 看看最终的年终奖吧，以及无招时代周全 x AI 团队中我可以考虑贡献的价值吧。如果价值真的偏少，也没有倾斜的意象，那么就要考虑自己的未来了。
* 未来的模式无非是下面三个方向推演，但是要注意严格的风险控制，核心策略只有一个「不要裸辞」，用「平稳过渡」的策略来应对未来的不确定性。
  * 集团内转岗到别的团队，如果和晚秋进展顺利，那么向「上海」靠拢是一个策略。
  * 向「上海」或者「杭州」的 AI 公司靠拢，聚焦在 AI 领域的中等规模的创业公司（对冲风险，聚焦有价值的业务 & 领域）
  * 向「上海」的大型互联网公司，追求小 TL 的职位（虽然很难，但是可以物色），但是可以考虑尝试对机会的把握

---

值得一提的是：


