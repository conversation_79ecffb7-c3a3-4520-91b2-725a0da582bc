# Plugins that inspired

## AI powered plugins

> ALL IN AI.

* ⭐️⭐️ AI Form Validation -> AI powered Form Fields Validation in Yida in General Design
* ⭐️⭐️⭐️ AI Filling Form -> images / attachments (pdf files and more formats support) support in Yida

## Basic Plugins

* Temperature -> show / edit temperature in Yida (support kinds of temperature units) -> digits transformer example
* Footer -> show / edit footer in Yida -> LobeUI
* Drag and SortableList -> drag and sort elements in Yida
* ⭐️⭐️ AI driven Mermaid / PlantUML Diagram: user can generate mermaid diagram from text
* Alert -> show / edit alert in Yida
* ToC -> show table of contents in Yida pages with div simple block
* ProcessBar -> show / edit process bar in Yida as Number powered FormField

## High-frequency plugins

> High leverage plugins to boost our efficiency and our partner's efficiency

* ⭐️⭐️⭐️ Map Plugin -> AMap + Google Map
  * [AMap SDK](https://lbs.amap.com/api/javascript-api-v2/summary) <-> server + FE as a whole sample
  * 🧚🏻‍♀️ AI Generated SDK Code to make it easier to use AMap SDK
* ⭐️⭐️⭐️⭐️ AntV Diagram Library Integration + AI -> Diagram in Yida, consider use AntV interactive painting syntax to generate diagram with AI (one prompt to generate one diagram) with specific data samples

## Powerful Tools

* ⭐️ Mini MindMap + AI Driven MindMap -> show / edit mind map in Yida
* Monaco Code Editor -> code editor in Yida
* DingTalk App Link -> Deeply interact with DingTalk App

## Business MetaData driven Module

* ⭐️⭐️ Topic Comments: user can comment on a specific topic and see the comments in the topic page

## New Explorations

> Explore new frontiers

* Animated AI Button -> Animated AI Button in Yida of DingTalk Theme
* Background -> AuroraBackground / GaussianBackground / GridBackground -> Background in Yida for Landing Page
  * Spotlight -> Spotlight in Yida
* ⭐️ Animation Container -> arrange and use animation in Yida
  * Framer Motion Encapsulation
* Streaming UI Rendering with AI SSR SDK
  * Weather Widget SSR
* Aceternity UI -> Highly visual effect
* FluentEmoji -> Emoji & Emoji Animation in Yida
* 3D Render -> 3D Render in Yida
* Voice Input -> Voice Input in Yida as source for future integration
* AR interface -> AR interface in Yida for future integration

