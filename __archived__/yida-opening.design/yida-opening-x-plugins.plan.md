# 🧩 Yida x ALab. 开放 & 插件.sys

## 核心原则设计

- 图灵完备（Turing Complete）
- 长尾效应（Long Tail Effect）
- 微内核架构（Micro Kernel）

## 关键里程碑

- Phase I：内部效能问题，内部场景可以由插件完成，`MVP` / `PMF` 阶段，建议覆盖所有可以考虑到的场景（FY25 S2 的重点），夯实基础，建设能力
- Phase II：企业内部组织效能问题，三方插件可以开发内部提效的插件模式，降本增效，解决长尾（复杂定制）需求，重点在于链路和安全性的设计，联合 AI 锻造强大的「AI 插件（GUI 深度加速行业）」
- Phase III：插件市场，可以基于市场兜售有价值的解决方案（插件），实现商业价值，重点在于商业模式、商业链路的建设

## 数据价值论证

* 客户的长尾需求吞吐量、吞吐能力
* 插件数量（官方 v.s. 生态开发者）、官方核心插件的使用频次（AI 插件活跃度 x AI 含量）

## 重要的演绎思考

* 开源官方插件源代码 (三方开发)
* AI 辅助生产（AI 生成插件代码）
* 开放的能力：宜搭 OpenAPIs（客户数据、流程） x 扩展点（产品和技术思维的转变） x AI 能力 (Agentic API / Agent API / Skills API / LLM Model API / ...)
* 用好运营：推广位、开发者活动、开发者社区(Github)
* 商业关系设计（短期内不做设计，聚焦在对内插件的增强和使用上）
* 和 AI 战役创造连接：产物可以被上层调度和消费（AI 生成组件）

## 演绎

Arno 的核心价值 FY26 S1

* 复杂的 AI 场景（能力） x Yida 插件的发掘 x 服务商 -> 开放模式的标杆
* 长尾需求的消费 -> 完全开放，长尾需求可以由服务商来完成
* AI 插件的跨 Yida 场域消费 x 创新战役 の CodeGen -> 打通插件的边界，形成钉钉一体的闭环

关键的业务 & 技术架构图

https://alidocs.dingtalk.com/i/nodes/jb9Y4gmKWrx9eo4dCK0EmpMNJGXn6lpz

关键的成功画面 👆🏻 & 开放架构设计。

### 跟进的关键事项

* 插件底座 x 平台 x 能力增强
  * WIP: [Composite Fields](./yida-plugin-compoiste-fields.design.md) smart-fin. abilities enhancement for complex component dev
  * WIP: [AI Opening SDK](yida-opening-sdk-x-scanfold.design.md) more APIs to engaged for Opening for calling AI abilities in different level: skills, agent, agentic, etc.
  * WIP: [Platform Open for 3rd party plugin developers](./yida-opening-platform.design.md)
* AI 生成插件代码 x Cursor 的能力建设 -> AI Generated Yida Component -> 战役建立关联
* 宜搭平台本身 AI 能力的建设（?）

### 核心的场景塑造

> Arno 目前看来最关键的还是「场景」，深度塑就「场景」的价值，并且是开放 x Yida x 场景的深度结合，给出几个案例。

* 和 `@九神` 对一下是否有商或者模式可以进来共创解决问题，看是否有比较好的复杂场景可以进入
* 和 `@洛远` `@沉寒` 对焦一下 Yida X AI 融合的场景和机会，开放可否再里面扮演一定的角色来实现关键价值
* 和宜搭的存量团队 `@君保` 以及 `@安楠` & `@右则` 对焦一下 AI x 开放和宜搭的连接、机会和空间
* 和 AI 底层能力团队：`AIAgentSDK` / `AI` 开放相关的能力聚合，看有哪些能力可以给宜搭消费并使用，以创就价值 `@金喜` 以及 `@洛远` 对焦

> 我们需要找到共创的关键场景来塑就价值，但是开始之前，基本盘的能力得有！

## 问题

* ✅ 后续前端团队对 yida 的支持如何？工单、基础架构、必要的能力支撑等 -> 暂不动，支撑位不变
* AIApp 的定位是什么？产品形态是什么？ @洛远，后续低门槛创作 Agent 和宜搭融合的产品和关键形态到底是什么？基础能力 x 体验的显著提升在什么地方？
* Agent 构建好了之后，上层应用包括宜搭如何做快速消费和业务集成？我可否在这里扮演重要的场景 & 应用和桥梁？AgentSDK 到底是什么？形态是什么？是这个我可以深度使用和集成的关键要素么？
* AIAgent 感知外部系统能力的关键是什么？可以和 Yida 的存量系统进行深度融合么？可以产生怎么样的效果？
* AI 应用信息框架（8.0）是什么？是脱离宜搭的心得载体么？这个产品形态到底是什么？Coze 还是 Yida 的 AI 增强版本呢？
* AgentFramework 是否可以找到 DingAgent x UI 的模式？这个到底是什么？可否和 Yida 做深入连接和集成？

## 复盘

2025-03-21

三方插件是「YY」的刚需，真实情况是客户并不会因为此项来增加宜搭的商业续单率，因为核心需求都没有满足好的情况下，长尾的定制需求根本就不是影响成单和续约率的关键要素了。

## 持续跟进事项

* [plugin-loader] purely run plugin component style in the iframe simulator with style and running ctx.


