# Opening Demo for <PERSON><PERSON>

Write the minimal demo in Github for Yida's Opening Plugin System.

## Business Scenario & Governance

* Yida's third-party plugin dev. / release / governance
* AI Assistant - UI block opening technology
* Din<PERSON><PERSON> Doctor Assistant - UI block opening technology
* AIGC basic business modules support UI of Yida & A.Lab. applications
* AIGC content block / plugin / component / page generation

All shaped in `UIPluginFramework` of A.Lab.

* 🦄 module-federation architecture
* 🚀 highly optimized for performance [performance optimization of federation module](performance/federation-performance.optimization.md) -> Yida opening proved
* 🧩 easy to use and extend and is platform-agnostic

## Sample Project

* [github](https://github.com/dingtalk-yida/yida-plugin-sample)

## Platform

* support load `localhost` or user custom domain / pathname load

## Component

* write a component in `Custom Page Component` for demo in `Hello World`
* use the cleanest way to write the demo code, both in source-code and scan-fold
