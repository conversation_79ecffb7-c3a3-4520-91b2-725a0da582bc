# Yida Federation Performance Optimization

Strategies:

* ✅ `cache remoteEntry` and `cache chunk` file, make sure the file is cached and loaded from cache first
* ✅ `remoteEntry` load on demand according to the required scenarios
  * ✅ `app`
* `chunk` component file loaded by remoteEntry use lazy-load make sure the plugin chunk is loaded when component is rendered
  * `yida-plugin-ai-ugc`: AI generative button
  * `yida-plugin-markdown`: Markdown preview
  * ...
* `async plugin execution` plugin triggered by user's action, make sure the plugin is loaded asynchronously, use load on-demand strategy
  * `yida-plugin-cad-preview` this should be loaded on demand when user triggered the preview action
  * `yida-plugin-custom-actions` this should be loaded on demand when user triggered the custom action
* `combo remoteEntry` use `CDN combo` strategy to reduce the request number of `remoteEntry` and cache the combo file on the edge node
* `shared` common library in federation bundle, reduce the duplication of the same library in different plugins
  * add public shared `entries` like: `antd`, `moment`, `lodash`, etc. to reduce the duplication of the same library in different plugins -> **reduce the bundle size**
  * `yida-plugin-sdk-core.js` is over than 200kb, check the source code and do the **tree-shaking**

## Tech Impl. Details

* ✅ feat: plugin loading framework support load on demand in scenarios 
* feat: component list load on demand according to the Schema information provided by rendering ctx
* feat: getExt support totally lazy invocation mode and load, parse and run in the runtime
* feat: loader support merge the entry with one-single combo files like -> `https://g.alicdn.com/??yida-components/yida-plugin-time/0.0.2/remoteEntry.js,yida-components/yida-plugin-smart-finance-components/0.0.2/remoteEntry.js`
* feat: add `antd` as shared library to reduce the duplication of the same library in different plugins (when change icon requirements are on, do it together) or umd global external mechanism
* hotfix: duplicated `yida-runtime-mf-plugin plugin already exists in VM` fix