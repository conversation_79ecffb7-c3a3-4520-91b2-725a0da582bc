# Yida Composite Fields Design

## Design Details

### Schema

* user defined new ComponentType in plugin's standard schema
* should mark props in such component with `composedFieldProps` which should specify the `ComponentType` and new generated `fieldId` as unique identifier

### Designer

* when user drag and save the schema page, we should extract those `ComposedComponents` and generate a series of related `FieldSchema` into current page's `schema` and mark mark their props with `__viewName__` which is the ComponentType name and marked `__isComposedView__` as `true`
* when user enter the page, the designer shall ignored the `__isComposedView__` marked fields and not show them in the designer, but show the composed customized view as a whole
* use can set the group label for the composed fields, which result in the generation of `hidden` schema to have the prefix of label as `groupLabel.metadataPredefinedLabel`
* one more step, composed fields can control all of the fields in the group, from props to schema field, for its control the schema generated process if needed

### Render

* HIDE: the `__isComposedView__` marked fields before the schema render, and ignore rendering them
* INPUT: when initialize the form data, the flattened data shall be merged into the form data by fieldRelation stored in the ComponentType's `schema` props, and then submit to the backend just like the normal fields
* OUTPUT: when user submit the form, the submitted Customized View's data shall be merged into the form data by fieldRelation stored in the ComponentType's `schema` props, and then submit to the backend just like the normal fields
* VALIDATION: the form validation should be handled by the composed fields' validation rules as a whole

### Consume Scenarios

* when user need to consume the composed fields' data in the downstream scenarios, the just use the composed fields' label + fieldId as individual field's identifier to get the data and consumed them.