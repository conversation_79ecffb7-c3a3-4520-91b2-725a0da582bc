# 🧱 Yida 宜搭.sys 

> Build for your CRAEER. Arno with YIDA.

Notion Mirror → <https://www.notion.so/arno93/Yida-sys-f71974c925934be7aa847e9367c0a33e>

# **💠 目标**

## **长期目标**

5 年积累，推动宜搭的「**<u>范式变革</u>**」，寻找 AI 时代的「**<u>第二增长曲线</u>**」。

## **中期目标**

1\~3 年内，成为关键战役的核心成员，在当前范式下，拿到「**<u>关键结果</u>**」。

若能够找到在宜搭的关键「弹弓」（GravitationSlingShot），也不失为好的对策。

周全聊天对话的输入：

- **<mark data-color="rgba(0, 0, 0, 0)" style="background-color: rgba(0, 0, 0, 0); color: inherit">Yida 未来的三驾马车</mark>**：AI（投资与未来）、低代码（当下与基本盘）、多维表（竞争压力来源）；
- **<mark data-color="rgba(0, 0, 0, 0)" style="background-color: rgba(0, 0, 0, 0); color: inherit">要投资的属性</mark>**：当下的现金流和未来的愿景，增长的空间和动力（巴布线贡献的核心价值论证）
- **<mark data-color="rgba(0, 0, 0, 0)" style="background-color: rgba(0, 0, 0, 0); color: inherit">AI 是必须重投入的</mark>**：但是多维表最好 OXM 的方式无论是钉钉文档还是维格表，都是投入之模式之一

个人价值之思考 🤔

- 核心命题：**<mark data-color="rgba(0, 0, 0, 0)" style="background-color: rgba(0, 0, 0, 0); color: inherit">开放 + AI</mark>** 做出实质性的结果

开放最理想的结果是什么？

开放必须深刻地结合上面的三驾马车来打，<mark data-color="rgba(0, 0, 0, 0)" style="background-color: rgba(0, 0, 0, 0); color: inherit">多维表的开放、低代码体系的开放、</mark>*<mark data-color="rgba(0, 0, 0, 0)" style="background-color: rgba(0, 0, 0, 0); color: inherit">AI</mark>* <mark data-color="rgba(0, 0, 0, 0)" style="background-color: rgba(0, 0, 0, 0); color: inherit">的开放</mark>。

## **短期目标**

1 年级别：

- FY25 S1：融入团队，成为前端的主力军，积攒信用，实现 Baseline 价值
- FY25 S2：支撑业务，寻找个人的价值「**杠杆**」，实现独特价值，追求较高的绩效水位（3.75），近期大概率负责宜搭的「开放」主题的业务，思考是否构筑宜搭的第二增长曲线 ✅ → 插件体系、开放体系，重点耕耘 🌟 - 2024-10-21

# **🎛️ 架构与基线**

## 关键指标

- 战役的关键指标（PD & 业务驱动）
- 自驱「技术指标」，侧面证明价值（RD 驱动）

## 结构描述

Arno 重要参与的子系统 👇🏻

<edoc-reference-text refdoccontent="👨🏻‍💻 Yida 服务商体系.sys" refdoccuid="7858b8c7-e679-4611-b718-ed6f339bb18e"></edoc-reference-text>

<edoc-reference-text refdoccontent="🧩 Yida 开放 &amp; 插件.sys" refdoccuid="c7454608-92eb-497a-8b1c-dd05ae8b0a25"></edoc-reference-text>

---

> 对 Yida 全域的架构理解尽量根据周全、啤石级别的角色的图。

业务 / 产品 / 系统架构

- L0 - 架构图 → 全域架构
  - 业务架构（StakeHolders 视角）
    - 【经营视角】Yida 一号位视角
    - 【客户视角】Yida 用户视角
    - 【客户视角】服务商视角 → Ref → 服务商体系下的业务架构 L0
  - 产品架构 → <edoc-reference-text refdoccontent="💚 Yida L0 产品架构" refdoccuid="*************-4b35-b55b-a9ecd11bf02f"></edoc-reference-text>
  - 系统架构
    - 元数据架构（Metadata Architecture） → ATA
- L1 - 子域架构 → 前端架构

> 尝试探索价值链表示的方式。

### 

---

产研迭代流程

- [FY25 Yida 的产研流程研发机制](https://alidocs.dingtalk.com/i/nodes/QG53mjyd800agdlKHB3vYg6g86zbX04v)

# **🧬 Genes**

## 规划思考

<edoc-reference-text refdoccontent="💯 Yida OKR &amp; 规划思考" refdoccuid="207b8cf7-20b8-432a-aee3-0c4b958a884d"></edoc-reference-text>

## 核心策略

短期策略：

- 「**<u>稳扎稳打</u>**」，支撑好自身所负责的业务 & 战役，熟悉 Yida，进入深水区 → 成为 Core Dev
- 「**<u>信用评级</u>**」，提升，建立和 L1、L2，以及周边伙伴的互信
- 「**<u>全栈思维</u>**」，找机会 FullStack （产品、设计、研发）的思路，闭环解决问题，创就价值

中长期策略：

- 「**<u>洞见 Insights</u>**」：多尝试找到技术赋能业务的「价值点」并持续酝酿和铺入，思考第二曲线之所在
- 「**扶植副业**」，预留发展空间 → <edoc-reference-text refdoccontent="🦄 Elaboration Studio.sys" refdoccuid="2bd63932-67da-4c42-b725-e56a162c339c"></edoc-reference-text>
- 「**用好杠杆」**，技术的、业务的、人的杠杆

### 

## Idea that Sparks

<edoc-reference-text refdoccontent="🌟 Yida Sparkles" refdoccuid="521ea411-a148-47b9-a0c4-c26a3d807281"></edoc-reference-text> 👈🏻 想法很重要 Which Truly Matters.

## K.E.S.W.

- [🦄 低代码 & hPaaS 技术域](https://www.notion.so/arno93/LowCode-hpaPaaS-squo-832c8a2c82b74049985704c26889be56) 研究
- 🌟 AI → 低代码领域研究

## 

# **🌟 Elaboration**

## 里程碑

- 述职大纲 → [📑 述职报告](https://alidocs.dingtalk.com/i/nodes/OBldywvrKxo89a50kooy8Qk2ngpNbLz4?utm_scene=team_space)

## 关键演绎

[Yida 平台](<https://www.notion.so/arno93/Yida-sys-f71974c925934be7aa847e9367c0a33e>) Notion 管理

# 🔗 Ref