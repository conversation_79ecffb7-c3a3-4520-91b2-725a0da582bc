# 🦄 Yida 宜搭在 AI 时代的主要范式转移是什么？

## 前置思考：范式的转移模式是怎样

`FullCode` → `LowCode` → `AICode`

在 LowCode 的资产关键资产上，进行重构，大刀阔斧地定义 AI 时代下的应用创建范式。

> 技术民主化红利：通过AI将应用开发成本降低90%，激活2000万中小企业长尾市场。把行业Know-How转化为可复用的AI Blocks，构建新型生产要素市场。

- 超级个体：技术门槛降低以至于从业人员主导 SaaS 的核心价值 ⛓
- 软件工程师职责：多元工种开始聚合为：产品工程师、基础架构工程师

宜搭的关键优势是是很么 SWOT 分析几何？

势能是什么？

- AIGC / AGI 的能力带来的势能 → AGI 的能力发展
- 开放 & 生态合作伙伴的势能 → 服务于不同粒度的能力构建（组件、模板、模块、应用）
- SaaS 行业的增长势能（数据和服务依旧是基础）
- Alibaba 的势能：钉原生 → 云原生 → AI 原生（AIOS 的原生应用）
- Yida 多年的原始积累 → 行业和数据、解决方案、元数据引擎、低代码搭建基础设施

---

关键的演进形态是什么？

> Yida 的转型本质是在重构「软件生产函数」，将传统生产要素（工程师+工具+流程）重新组合为（领域知识×AI能力ⁿ×生态连接）。这场变革可能催生新的市场阶层——掌握「AI策展能力」的解决方案架构师将成为数字生态的新贵，而单纯写代码的低代码开发者可能面临价值重构。成功的关键在于能否在AI精度与人类创造力之间找到黄金平衡点。

- **AI能力**(n次方)：取代传统人力资本，成为边际成本趋近于零的"数字石油"
- **领域知识**(乘数因子)：从隐性经验转化为可交易的数字资产（如医疗SOP图谱、供应链优化模型）
- **生态连接**(β系数)：钉钉生态构成网络外部性，类似"数字基础设施的公共品"

> 典型案例：某连锁药店用自然语言描述会员运营需求，Yida调用AI药剂师模型+钉钉组织数据引擎，自动生成带药品滞销预测算法的CRM系统，开发周期从3个月压缩到72小时。
>
> ### ***边际革命与市场结构裂变***

**科斯定律的重构**：当AI生成代码的边际成本趋近于零时，企业边界不再由交易成本决定，而是取决于**领域知识封装速度**。这导致：

- **长尾市场觉醒**：2000万中小企业突破"数字鸿沟"，形成蚂蚁雄兵式创新
- **要素市场重组**：AI Blocks交易所可能催生新的数字华尔街，算法成为另类资产
- **熊彼特式毁灭**：头部SaaS厂商面临"创造性破坏"，类似iPhone颠覆诺基亚

这场变革本质是**数字世界生产方式的范式迁移**——当AI将代码生产从"手工业"升级为"智能制造业"，Yida正在成为新经济的操作系统，重新定义谁有资格参与数字财富创造。

---

- 切口小，见效快的模式几何？
  - AI Driven Headless CMS → Landing Page → Data X PageComposer
  - AI Driven Blocks Development
  - AI Driven Metadata / BusinessLogcis / Flows

> 每个 Composer 维度均可与生态合作，形成技术、数据、服务的网络效应。

---

典型的产品模式更新？

- UI Patterns → LUI / GUI / CLI → Next ERA?
- 横向领域 → 信息和效率：搜索、推理、多模态、公域知识与服务
- 纵向领域 → 行业和深度：数据、复杂 Agent、私域知识与服务

---

AI x 开放的关键是什么？

- 按照 UGC / PGC 的思路，由浅至深，服务普通用户、服务商、宜搭系统工程师，AI FIRST 去创造开放的维度、要素。

- 每一个 Composer 的维度都可以 x 开放

---

AIOS 的形态几何？

WIP

---

AI 驱动时代的用户画像和分层？

---

> 2025-02-24 整理 Stash 一番

## 范式转移层 Paradigm Shift

思考，下一代智能应用的搭建范式是什么？ 1 年，3 年，5 年？

Application Builder / SaaS

- **整体思想**：LowCodeFirst -&gt; **AI-First**
- **价值链条**：低代码工程师 x 拖拽配置 x 平台能力 -&gt; **行业数字化专家(领域知识) x AI能力^n x 生态**

详细解释：

- **AI 能力**(n次方)：取代传统人力资本，成为边际成本趋近于零的「数字石油」
- **领域知识**(乘数因子)：从隐性经验转化为可交易的数字资产（如医疗SOP图谱、供应链优化模型）
- **生态连接**(β系数)：钉钉生态构成网络外部性，类似「数字基础设施的公共品」

范式转移变革的核心：

- SaaS 应用的生产过程中所有的「**生产要素**」都被 AI 重构
- SaaS 应用自身的 UI 以及能力也会由 AI 重构

因此：智能应用的平台会成为 Yida 作为 PGC 的主要内容，会由宜搭的开发者提供；UGC 是未来智能应用的主要内容，会由生态（ISV、服务商等）提供基础行业能力、业务 / 行业 Know How 的专家作为内容提供者。

## 产品 / 业务架构层 Product Conceptual Design

新的产品模式是怎样的？

SaaS 消费侧：

> 各行各业的大众用户，使用 AI First 的数字产品，来解决自己的业务问题

- **智能应用（AI-First SaaS Apps）**：AI-First 的应用，重构传统的 SaaS 应用，形成新的应用模式
  - 基于传统的 GUI 模式，组合 AI 能力，形成新的应用模式，AI Everywhere in the app -&gt; Yida 必须加速这个过程，让 AI 无处不在
  - 基于传统的专业级工具和应用上组合 `Copilot` 能力，形成新的应用模式 -&gt; 通过让深度工具，植入 AI 能力，AI 智能应用也需要这类模式的存在
- **个人超级助理（Jarvis Assistant）**：One Assistant to rule them all
  - Everything is `quotable`: docs, people, tools, meetings, schedule, todos ... even the sub-assistant itself
  - Agent Mesh: 多Agent 协同工作，形成新的应用模式
- **行业 / 专用助理（Industry / Dedicated Assistant）**：行业 Know How 的专家 = 模型 / 知识库 / 工具 / 业务流 / 智能流 ...
  - Agent SDK: 行业助理的开放调用能力

SaaS 供给侧：

> AI 和 SaaS 能力提供者，行业 Know How 的专家

> 智能助理、智能助理的能力体系的生产过程 Agent / Agent Abilities Factory

- **智能应用 (Apps) 的生产过程**：低代码 LowCode + *AIAppComposer*
  - **数据实体 (Data Entity Composer)** = 数据模型、数据权限、数据服务 ...
  - **业务流程 (Workflow Composer)** = 业务规则、业务逻辑 ...
  - **页面视图 (Page Composer)** = 自定义页面视图、表单视图、数据视图、报表视图、流程视图 ...
- **能力(Abilities)的生产过程**：ProCode + *AIAbilityComposer*
  - **SaaS 应用能力的规格化** -&gt; **API / SPI Composer**
    - API: 服务端接口（连接器）、客户端 JSAPI、前端组件（JS-SDK）
    - SPI：服务端扩展接口、前端扩展接口、Native 客户端扩展接口
  - **AI 能力的规格化** -&gt; **Agent Composer**
    - Agent：单一 Agent 能力
    - Agent **Mesh / Swarm**: 多Agent 协同工作，形成新的应用模式
    - Agent Abilities
      - `Agent as a Service`: AI 能力即服务
      - `Tool` / `Function`: search, code evaluation, file reading, diagram generation, ...
      - `Workflow`: 流程编排
      - `AI Driven / Dedicated Workflow`: 智能流程编排
      - `Agent RPA`: 机器人流程自动化
  - **不同粒度的 UI 能力** -&gt; **UI Composer**
    - **Composable UI Block**: 可组合的 UI 组件（数据 x UI x 能力）
    - **Composable UI Section / Page**: 可组合的 UI 区块 / 页面（页面视图）
  - **硬件 x 软件 x 服务**：IoT 能力和 AI 能力结合，形成新的能力

## 技术架构层 Technology Conceptual Design

对应支撑的技术架构几何？

- **基础技术**：IaaS -&gt; 网络、数据库、中间件、AI 基础算力等等 -&gt; 阿里云大模型、云计算技术
- **基础平台技术**：PaaS -&gt; AIPaaS（LLM Studio（风火轮）、RAG / Doc2Bot、小模型）、私有化能力、数据能力（dPaaS）、钉钉融合底座（IM、通讯录、协同平台连接）、开放底座（开放平台、iPaaS）...
- **AI 平台技术**：AI 能力平台（Agent、Agent 能力体系）...
- **Yida 平台技术**：LowCodePaaS -&gt; 元数据能力、低代码能力、表单、流程、报表...
- **智能应用平台**：AI-First SaaS 平台 -&gt; 智能应用、智能助理

## 开放的原则思考

- **开源生长**：开放官方所有类型的开放要素，让生态伙伴基于这些要素进行生长，甚至反过来影响官方
- **野蛮传播**：如果 Chrome Extension 不用审核上架就可以使用的传播模式
- **完全开放**：API、SPI 渗透到应用、SaaS、Agent 的方方面面，不留余地

## Idea

- **智能应用 x 智能助理** 的融合形态？你中有我，我中有你

## Ref

<edoc-reference-text refdoccontent="💬 了解宜搭产品的功能与优势" refdoccuid="1cefa43a-4236-4119-92b2-1b472087e8bd" reftype="chat"></edoc-reference-text>
<edoc-reference-text refdoccontent="💬 Yida 宜搭在 AI 时代的转型分析" refdoccuid="6ad87d69-d847-4a24-8b69-0f8a78e07ff9" reftype="chat"></edoc-reference-text>
<edoc-reference-text refdoccontent="💬 宜搭在 AI 时代的 SWOT 分析" refdoccuid="0978d522-21b0-4589-be7a-905d951d953c" reftype="chat"></edoc-reference-text>
<edoc-reference-text refdoccontent="💬 六顶帽子思考法深度洞察分析" refdoccuid="e6006a4e-01b8-446b-ad12-a91540400fae" reftype="chat"></edoc-reference-text>
<edoc-reference-text refdoccontent="💬 经典思维模型助力深刻思考" refdoccuid="7183551e-0193-49b3-a7fc-1a547cc84843" reftype="chat"></edoc-reference-text>
<edoc-reference-text refdoccontent="💬 Yida 在 AI 时代的生产变革分析" refdoccuid="10e8bc80-3956-4fd6-85e7-17b33f30f191" reftype="chat"></edoc-reference-text>
