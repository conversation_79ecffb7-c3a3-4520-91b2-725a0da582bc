# <PERSON><PERSON>'s Digital Garden: Portfolio & Tech Musings

Welcome to my digital garden! This space is a blend of a personal portfolio and a blog where I share insights on software development, explore front-end technologies, and document my creative projects. Dive in to discover my work and thoughts on the ever-evolving tech landscape.

Inspired by and based on the work of: https://github.com/leerob/leerob.io/

## Local Development

### Getting Started

To get the project up and running on your local machine, follow these steps:

1.  **Clone the repository:** (You've likely already done this if you're reading this!)
2.  **Install dependencies:** Open your terminal in the project root and run:
    ```bash
    pnpm install
    ```
3.  **Run the development server:** After installation, start the development server with:
    ```bash
    pnpm dev
    ```
    This will typically start the server on `http://localhost:3000`.

### Related TechStack

- next.js
- tailwindcss
- typescript
- mdx
- contentlayer
- React
- Vercel Edge Config

Have fun hacking 😎.

## Future plans

- [ ] add Photography module

## Features

- MDX Support (for rich content)
- RSS Feed
- Bilingual Posts (English and Chinese)
- Responsive Design
- Syntax Highlighting for code blocks

## Content Focus

This blog explores a variety of topics, including:

- Front-end development (React, Next.js, browser technologies)
- Software architecture and design patterns (SDK design, IOC)
- Developer tools and productivity (Alfred, Arc Browser, Notion, Mac mastery)
- Artificial Intelligence in tech (AI-driven front-end, AIGC)
- Personal insights and project journeys

## Deployment

This site is deployed using Vercel.
Visit the live site here: [**Update this with your live site URL!** - e.g., https://arno.xyz]

## License

This project is licensed under the MIT License. See the [LICENSE](LICENSE) file for more details.
