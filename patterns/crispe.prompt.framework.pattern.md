# CRISPE Prompt Framework

Another great framework is <PERSON>'s [CRISPE](https://learningprompt.wiki/docs/chatGPT/tutorial-extras/chatGPT-prompt-framework#crispe-prompt-framework). More complex but quite comprehensive for prompt templates:

CR： Capacity and Role - The role for ChatGPT to take on.
I： Insight - Background info and context (I think Context is clearer).
S： Statement - What you want ChatGPT to do.
P： Personality - Desired style or manner for responses.
E： Experiment - Ask for multiple answers.
Here are examples for each element:

## Prompt Template

### Capacity and Role

* [ ] Capacity and Role: `{{capacity}}`

### Insight

```insight info```

### Statement

```statement info```

### Personality

```personality info```

### Experiment

```experiment info```

