# <PERSON><PERSON>'s Portfolio

This is a project of personal blog and portfolio website for <PERSON><PERSON>, showcasing his work, skills, experiences, and articles.

You can learn the `README.md` file for more details about the project.

- MDX Support (for rich content)
- RSS Feed
- Bilingual Posts (English and Chinese)
- Responsive Design
- Syntax Highlighting for code blocks

## Project Structure

* `content`: content layer mdx files to serve as blog posts and portfolio items.
* `src`: next.js application source code.

## Project Tech Stack

* next.js 13
* see `package.json` for other dependencies
* tailwindcss
* typescript
- next.js
- tailwindcss
- typescript
- mdx
- contentlayer
- React
- Vercel Edge Config

use `pnpm` for package operations
