I want to have a route of module to log my quotes.

* the route is /quotes
* these quotes are usually read a datasource from remote, you can mock it for now
* it is like a cool modern twitter / x card, use the same beautiful design
* this is a card grid to show those posts, post including text, images, youtube video, etc.
* the card should be clickable, and redirect to the post page, if it has a link to the related detailed page.
* card should have some animation style, you can install framer-motion for this scenario.
* card should look cool in modern design, write style in tailwindcss.
* pay attention to the performance, and the SEO of course on @metadata @sitemap @robots.txt ...