# Commit Msg Rule

format examples:

🦄 feat: <short description of the change>
🐛 bugfix: <short description of> the bug
🎨 style: <short description of the style change>
🚀 perf: <short description of the performance improvement>
📝 docs: <short description of the documentation change>
🔧 chore: <short description of the chore>
♻️ refactor: <short description of the refactoring>
🔒 security: <short description of the security fix>
🔖 release: <version number>
[customized-emoji] keyword(module): <short description of the change>

---

- keep the commit short and brief in less than 70 words each line
- only with oneline to describe the change no extra line
- if the modifications is too long or complex, the one-line should still be brief but consider add module name or keyword to describe the change, add detail change list in the commit body