{"private": true, "license": "MIT", "scripts": {"dev": "next dev", "build": "next build", "start": "next start"}, "dependencies": {"@vercel/blob": "^0.14.1", "@vercel/edge-config": "^0.2.1", "clsx": "^2.0.0", "contentlayer": "^0.3.4", "date-fns": "^2.30.0", "dotenv": "^16.3.1", "framer-motion": "^10.16.4", "gray-matter": "^4.0.3", "next": "13.5.5", "next-contentlayer": "^0.3.4", "react": "^18.2.0", "react-dom": "^18.2.0", "react-tweet": "^3.1.1", "react-wrap-balancer": "^1.1.0", "rehype-autolink-headings": "^6.1.1", "rehype-pretty-code": "^0.10.1", "rehype-slug": "^6.0.0", "remark-gfm": "^3.0.1", "rss": "^1.2.2", "server-only": "^0.0.1", "shiki": "^0.14.5", "swr": "^2.2.4", "uvcanvas": "^0.2.1"}, "prettier": {"arrowParens": "always", "singleQuote": true, "tabWidth": 2, "trailingComma": "none", "semi": false}, "devDependencies": {"@mdx-js/loader": "^2.3.0", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/typography": "^0.5.10", "@types/node": "20.4.8", "@types/react": "18.2.18", "@types/react-dom": "18.2.7", "autoprefixer": "^10.4.16", "eslint": "8.46.0", "eslint-config-next": "13.4.13", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "typescript": "5.2.2"}}