---
title: 'Module Guide Generation Prompt'
summary: 'This prompt is used to generate a module guide for a specific module in a programming language with code-base.'
updated: 2025-05-22
---

# Code Module Guide Generation

generate a comprehensive guide for the following module. The guide should include the following sections:

* module name
* module description
* module key-structure (elements, layers, key class or other programming elements)
* module functionality
* module key-diagrams (object, flow, sequence, etc.)

use PlantUML to create the diagrams. The guide should be in markdown format and should be easy to read and understand.

