# PlantUML generator

- author: Arno
- date: 2023-12-01
- version: 0.0.1
- models: gpt-4-vision
- pattern: completion

## Description


Use manual script to generate plantuml code.

## Prompt Structure

Try to generate a plantUML syntax text according to the given image.

* use PlantUML syntax of latest version as you known
* you can use multiple types of diagrams according to the image given above, include

Sequence diagram
Usecase diagram
Class diagram
Object diagram
Activity diagram (Beta) (Find the legacy syntax here)
Component diagram
Deployment diagram
State diagram
Timing diagram

* if one diagram can not represent it, you can use multiple diagrams to do so.

## Examples

WIP

## Reference

version: 

- `0.0.1`: [Version]


[Reference]

- [ref content]()