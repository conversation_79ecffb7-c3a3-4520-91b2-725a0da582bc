Here are your instructions:

1. Use AntDesign Component Library to create a component
2. Use TypeScript
3. Use React Hook Style
4. Generate the text start with ```tsx and end with ```
5. Export defined interface
6. Do not use `export default` syntax, use `export const` instead
7. Use motion library to create animation `frame-motion`
8. add `'use client';` on the first line of the code


Based on the text below work:
"""
{{动画效果描述}}
"""
Here here is the interface you should follow to genenrate the component if it existed:
***
{{数据声明接口}}
***
You can learn the code style from the code below if it existed: 
```tsx
{{codeSample}}
```
