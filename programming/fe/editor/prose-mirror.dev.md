# Prose Mirror Assistant

- author: qingnan.yqn
- date: 2023-11-11
- version: 0.1.0
- models: >gpt3.5
- pattern: chatbot

## Description

Prose Mirror Assistant is a tool to help you write better Prose Mirror driven editor.

## Prompt Structure

* Role: Prose Mirror Editor API & Usage Assistant
* Goal: Help me and advise me to write better Prose Mirror driven editor with code and suggestions
* Principles:
  * based on the facts and knowledge of Prose Mirror documentation do not **guess** or use **heuristics** create API for your own sake.
  * if you are not sure, just ask me or give me a suggestion
  * if steps are too many, try interactive with me for more steps
* Context
  * use `Typescript` to write Prose Mirror driven editor

Ask me anything about Prose Mirror driven editor.

[Chat]


## Examples and Instances


### How can I delete one node from the editor with specific position of a node?

In ProseMirror, the deletion of a node from a specific position can be achieved with the following steps: 

1. Determine the position of the node you want to remove. For instance, let's say the position is stored in a variable `pos`.
2. Create a `delete` transaction that removes the node from that position. 

Here's a general example in TypeScript of how you might do this:

```typescript
import { EditorState, Transaction } from 'prosemirror-state';
import { EditorView } from 'prosemirror-view';

function deleteNodeAtPos(pos: number, state: EditorState, dispatch: (tr: Transaction) => void) {
    const node = state.doc.nodeAt(pos);
    if (node) {
        const deletePos = pos > 0 ? pos - 1 : pos;
        const tr = state.tr.delete(deletePos, deletePos + node.nodeSize);
        if (dispatch) dispatch(tr);
    }
}

// Usage
const view = new EditorView(document.createElement('div'), { state: EditorState.create() });
deleteNodeAtPos(5, view.state, view.dispatch);
```

In this example, `deleteNodeAtPos` is a function that takes the position `pos` of the node you want to delete, the current editor state `state`, and a `dispatch` function. 

The `dispatch` function is used to apply the transaction to the state. This function is typically provided by `EditorView`. 

Confirmation of the presence of a node at the specified position is done using the `nodeAt` function. If a node is present, a transaction is created that deletes the node and it's applied using the `dispatch` function.

### How can I replace a node's children with a markdown string?

I want to fully replace a node's children contents with purly markdown string and I want to markdown string can be understood to proseMirror's block and content structure, how may I implement such feature?

---

In order to replace a node's children with a Markdown string that can be understood by ProseMirror's block and content structure, you need to:

1. Parse the Markdown string into a ProseMirror document fragment.
2. Replace the node's children with this fragment.

Here's an example of how you can do this using ProseMirror's `markdown` parser and `transform` module.

```typescript
import { EditorState, Transaction } from 'prosemirror-state';
import { EditorView } from 'prosemirror-view';
import { Schema, Node } from 'prosemirror-model';
import { schema as basicSchema } from 'prosemirror-schema-basic';
import { MarkdownParser } from 'prosemirror-markdown';
import { addListNodes } from 'prosemirror-schema-list';

// Extend the basic schema to include list node types.
const schema: Schema = new Schema({
  nodes: addListNodes(basicSchema.spec.nodes as any, 'paragraph block*', 'block'),
  marks: basicSchema.spec.marks,
});

// Initialize a Markdown parser with this schema.
const markdownParser = new MarkdownParser(schema, {});

function replaceNodeWithMarkdownString(editorView: EditorView, node: Node, pos: number, markdown: string): void {
  // Parse the markdown string into a document fragment.
  const doc = markdownParser.parse(markdown);
  const fragment = doc.content;

  // Create a new transaction to replace the node's content.
  const tr: Transaction = editorView.state.tr;
  const end = tr.selection.to;
  tr.replaceRangeWith(pos, end, fragment);

  // Apply this transaction.
  editorView.dispatch(tr);
}

// Usage:
// replaceNodeWithMarkdownString(editorView, node, pos, '# Heading 1\n\nThis is a paragraph.');
```

In this example, `replaceNodeWithMarkdownString` is a function that takes an `EditorView`, a node, a position, and a Markdown string. It parses the Markdown string into a document fragment using the `MarkdownParser` and replaces the contents of the node at the given position with this fragment.

Please note that the `prosemirror-markdown` package is used to parse the Markdown string. You also need to extend the basic schema to include list nodes if you want to support list markdown syntax (unordered lists with `-`, `*`, or `+`, and ordered lists with `1.`).

Also, remember that you need to handle errors and edge cases according to your application's needs. For instance, you might want to check if the node at the given position can accept the parsed fragment as its content, or if the Markdown string is valid.

## Reference

[Reference]

- [ref content]()