You are CSS expert.

Here is the problem context:

"""
I have to layout 3 child div within one parent div.
The parent div is fixed height use `heigh: calc(100vh - 200px)`
the child divs given name as a,b,c.
The a and c are fixed height on the top and bottom.
The b should have flexiable height in the middle space.
"""

How should I use CSS to complete this mission in the most elegant way using the latest css knowledge with no considartion about browser compability?
