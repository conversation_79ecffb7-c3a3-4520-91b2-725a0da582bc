---
title: 'The Tool Literacy of an Excellent Front-End Developer'
publishedAt: '2022-06-12'
summary: 'For efficiency, to save time, we need good tools ~'
tags: ['Dev', 'Tools', 'Frontend', 'en']
image: http://image.surfacew.com/posts/20230517210339.jpg
---

| Version | Description                         | Date       |
| ---- | ---------------------------- | ---------- |
| v2   | Some content updated to keep up with the times | 2023-10-26 |
| v1   | First version                         | 2022-06    |



## 💎 Foreword

> A workman must first sharpen his tools if he is to do his work well.

👆 Everyone probably understands the道理 behind this saying ~

But recently, during my summer internship, I paid special attention to the tool usage habits of the intern students around me. I found that what I considered a highly efficient work model in college, they were somewhat clumsy in both their awareness and use of tools. Especially some students who had hardly ever used a Mac, they were practically novices in the realm of efficiency. So, in the spirit of being a responsible and good senior, I will share my experience with using tools with everyone. It's also a way to throw a sprat to catch a whale, and to chat with everyone about what NB and fun tools are out there.

> It should be noted: I am mainly using the Mac Apple ecosystem as a baseline here, but I believe the principles of tools and efficiency improvement are universal. There are specific tools available on Windows as alternatives. If I have the chance later, I will also write about how to tame my gaming PC at home.

Of course, I can't explain the tools below thoroughly in this kind of summary article, so I'll just "scratch the surface" and provide links to related extended reading. If you're interested, you can follow the external links to research further or Google high-quality resources from the community yourself ~

So, are you ready? Here we go ~

## 🛠 Tools in Front-End Work

Before we start talking about mainstream front-end tools, let me first emphasize the importance of being proficient, extremely proficient, lightning-fast proficient with keyboard shortcuts as a Coder!

### Become a Keyboard Shortcut Enthusiast

When using software tools, I subconsciously require myself to remember at least the Top 20 "keyboard shortcuts" for operations. While I don't demand becoming a "pure gold keyboard warrior" like a Vim editor user, at least for VSCode's mainstream shortcuts, one should develop "muscle memory." Let me ask everyone a question, if you can answer it, it means your mastery of VSCode shortcuts is pretty good ~ Here it is:

---

Question: What is the keyboard shortcut for Rename Symbol in VSCode? (P.S. If you don't even know what Rename Symbol does, you deserve a spanking 😄)

Answer: ——————————

---

If you can't answer, then please work harder. Believe me, keyboard shortcuts can save you at least 1s per operation. Think about how worthwhile that is ~
Of course, here I recommend a tool for querying registered "keyboard shortcuts" for applications on Mac — CheatSheet. Long-press the Command key to activate the shortcut menu for the currently used App.

### 👑 Github Copilot

> The king-level tool of 2023. I won't elaborate here, it's my top recommendation ~

- [Github Copilot](https://github.com/features/copilot)
  - Supports `Copilot Chat`, allowing you to chat with your selected code, files, and projects, like a built-in mini ChatGPT.
  - Supports various code suggestions and completions.

I've become completely inseparable from Copilot. A day without it feels unbearable. Btw, if you're still hesitating because of its cost, let me tell you, try it for a month. The efficiency gains from the code it writes far exceed its cost, so don't hesitate, get on board now ~ If you really don't want to spend money, you can look for free alternatives in the community. In modern programming, it's almost embarrassing to call yourself a programmer without an AI assistant.

![Github Copilot](https://6gflxwplhijgv9h7.public.blob.vercel-storage.com/20231026224328-JfSx7MoEWIJSUNZhbnTVOoKpDgImHR.jpg)

### Become a VSCode Professional

Tools also have trends. Since TypeScript started to become widespread, VSCode has indeed become the mainstream front-end development tool. However, I find that many students are still at an introductory level in their use of VSCode, not truly leveraging the power of this tool ~ So I'd like to chat with everyone about this. I don't intend to write a Bible-level VSCode guide, but rather use a few small cases to show you that VSCode has many interesting features that can greatly improve efficiency, especially VSCode Extensions (plugins).

1. Did you know that VSCode has a cloud sync feature for configurations and can sync with other devices with one click?
2. Did you know that VSCode has a plugin that can automatically sort TypeScript imports and remove unused dependencies?
3. Did you know that VSCode can use keyboard shortcuts to automatically fold code levels?

If you know all of these, that's pretty good 👍. If you've never even heard of them, then I have a few suggestions for you:

- Go through the VSCode keyboard shortcut list, pick out the ones you think can improve your coding efficiency in the future, and practice them repeatedly until they become muscle memory.
- Browse through the Top 200 VSCode plugins by installation count and popularity, read their descriptions, and install the ones that interest you. 👈 Embark on a treasure hunt, read fewer recommendation articles, and tinker more yourself to find good tools!
- Finally, give VSCode a beautiful theme and font. According to my aesthetic, this is the effect I want ~ btw, the theme is `OneDarkPro` and the font is: [FiraCode](https://www.zhihu.com/question/32058777/answer/761703173).

---

Extended Reading:

- [VSCode Plugin Recommendations](https://zhuanlan.zhihu.com/p/40417719): There are many such articles on Zhihu, feel free to browse.
- [Guide to Writing VSCode Plugins](https://code.visualstudio.com/api): Know not only what it is but also why it is. I've written a VSCode Plugin before and have some understanding of VSCode's architecture; it feels very NX.

---

Here, I also recommend a few plugins that I personally find particularly useful:

- **Pretty Typescript Error**: Makes TS error messages clear and easy to understand at a glance, no more eye strain.
- **Code Spell Checker**: Checks for spelling errors in code, no more worrying about typos. Strongly recommend that colleagues around you also install it [doge].
- **Document This**: Generates JSDoc comments with one click, no more struggling with writing comments.
- **Github Copilot**: Needless to say, highly recommended for everyone to use, no more struggling with writing code.
- **JSON to TS**: Converts JSON to TS types with one click, no more struggling with writing types.
- **One Dark Pro**: A good-looking theme, I use it frequently.
- **Plant UML**: A plugin that allows you to draw UML diagrams directly in VSCode. You can write code directly in VSCode to generate UML diagrams, very useful.
- **Todo Highlight**: A plugin that allows you to directly mark TODOs in your code.
- **Typescript Class Organizer**: The ultimate solution for those with OCD.
- **WSL**: Windows developers will understand immediately.

### Use the Terminal Well

As an engineer, you are not required to be a Shell master, but common commands in the Terminal and daily beautification and optimization are necessary. Here I recommend the iTerm + OhMyZsh combination to create a stable and easy-to-use Terminal.

- Download and install [iTerm](https://iterm2.com/index.html) (You can also use the one in VSCode, but I still recommend a standalone terminal App because VSCode sometimes freezes and takes iTerm down with it, so it's better to have specialized tools 🙈). With this, split-screen operations like the one below 👇 become routine.
  - However, in 2023, a startup product called [Warp](https://docs.warp.dev/features/blocks) is more recommended. This Terminal is future-oriented, with many excellent features, such as direct regional selection and editing in the terminal, making the terminal like a rich text editor. It also supports AI-driven features, integrates the functionality of oh-my-zsh, and supports `Alfred`-style invocation. It's very user-friendly and highly recommended.

![Warp](https://848020679-files.gitbook.io/~/files/v0/b/gitbook-x-prod.appspot.com/o/spaces%2F-MbqIgTw17KQvq_DQuRr%2Fuploads%2F5xTRTXvr4jV3U1jWqNLt%2FWarp_Essentials.gif?alt=media&token=424f40a6-d525-44df-9890-6fb74fb7a2aa)

- Download and install [OhMyZsh](https://ohmyz.sh/), update to the latest Git directory, download all mainstream plugins, and it will instantly become colorful after installation. Then install the corresponding theme, and you'll be overjoyed.
- "Train" OhMyZsh according to personal interest. It is strongly recommended to enable these plugins in `~/.zshrc`: you'll know how good they are once you use them ~ 😄 Any one of their features is incredibly useful. I won't go on about it here. If any other students have useful plugins, feel free to share them in the comments.

---

```shell
plugins=(git osx wd autojump zsh-autosuggestions copyfile history last-working-dir)
```

For example: The Git plugin can simplify complex git commands like `git checkout -b 'xxx'` to: `gcb 'xxx'`.
For example: The OSX plugin can help us quickly open Finder and perform other operations.
...

---

Extended Reading:

- [Shell Programming Introduction](https://juejin.cn/post/6844903553119748109): Write scripts by hand, improve efficiency ✍🏻
- [OhMyZsh Plugin Collection](https://github.com/ohmyzsh/ohmyzsh/tree/master/plugins): Look at those fancy shell plugins, come on, let's show them all off 🌞
- [Vim](https://vim.rtorr.com/) Shortcut CheatSheet: In the era of manually configuring servers, Vim was a divine tool. It still seems worthy of passing on the torch 🧎‍♂️ Master, please accept my obeisance.
- [Warp Documentation](https://docs.warp.dev/getting-started/readme) Have a try ~

## Use Chrome DevTools Well

As a front-end developer, I don't need to elaborate on its importance. I strongly recommend that everyone read the official documentation thoroughly; you will gain a lot of new knowledge.

[https://developer.chrome.com/docs/devtools/](https://developer.chrome.com/docs/devtools/)

👆 This could be a whole book, but I still recommend everyone to make good use of this tool. This is our most important debugger for front-end development. I often ask candidates during interviews how they use the debugger to solve problems. In fact, by observing how people debug code, you can tell their programming proficiency. Really, everyone can consciously observe how they debug and troubleshoot problems. Is it efficient? Is there room for improvement?

- For example: How to identify rendering bottlenecks in a project?
- For example: How to investigate memory leaks?
- For example: How to globally search for duplicate code?

## Use Chrome Extensions Well

Browser extensions, I won't say much. I will list the Chrome extensions I use daily here, and I welcome experts from all walks of life to add their own browser extensions and cool tricks. Let's focus on those for developers:

- JSONFormatter: For formatting JSON data directly requested daily.
- XSwitch: A browser network request proxy tool handwritten by my former TL. Let me give him a shout-out 😛
- ReactDeveloperTools 👈 No need to explain this one, I strongly recommend everyone to enable the HighlightRerender feature to see how poorly written their code is and to criticize themselves more 🙈

There are many "godly articles" about Chrome Extensions. I won't mention tools like Tampermonkey, AdBlock, video downloaders, etc., here. Those who know, know. Those who don't, can Google it themselves. I'll recommend a few more articles here for everyone to read as needed:

- Chrome Front-end Plugin Recommendations: This question and [answer](https://www.zhihu.com/question/264685470) on Zhihu are quite pertinent.
- Chrome General Plugin Recommendations: Zhihu continues to [recommend](https://www.zhihu.com/question/19594682), check out the "collective wisdom" under the highly-voted answers 😁

## 🔍 Search! Search!! Search!!!

Phew, finally finished talking about development tools. Next, let's talk about search. In my understanding, I've always divided the efficiency of personal information management in the digital age into three basic levels:

- Entry-level: Rarely organizes their disk and desktop. A typical characteristic is that all sorts of bizarrely named files are piled together on the desktop.
- Novice-level: Starts to consciously organize. Files are hierarchical, attention is paid to file naming, and mechanisms like tags are established.
- Proficient-level: Starts to consciously build database indexes, performs file indexing at the OS level, and has data awareness.
- Master-level: Starts to focus on data, systematically integrates personal data and collective data into daily life, and even begins to use unstructured data to assist in tasks. For example, can you directly access and compare the year-over-year EPS trends in the financial reports of various companies to judge their profitability, or even feed this to ChatGPT? This is master-level thinking.

Ask yourself, which level are you at?

### Spotlight

I won't talk about the first and second levels. Here, I want to focus on sharing the indexing and search tools to reach the third level. You should know that Spotlight on Mac has always been a global search tool. Using Spotlight well allows seamless unlocking of system-level search for major Apps, files, calendars, etc.


### Alfred

But the system's built-in tools are often not the strongest, right? So, based on Spotlight's system-level metadata (Mac builds its own file index library and provides APIs for upper-level applications to call), a very powerful tool called **Alfred** was born. I have always been a senior fan + user of Alfred, using Alfred's functions (search) up to 70 times a day.

Alfred is a "truly meaningful efficiency tool," its main functions include:

- Document retrieval
- Quick URL concatenation
- Clipboard history quick access & search
- Bookmark search
- Custom workflows (this will be discussed in detail in the next chapter)
- ... (invincible functionality)

I strongly recommend that students who don't know what Alfred is read the article below 👇. **This article is an introduction to Alfred that I wrote on the intranet during my first year at Alibaba. If you find it helpful, please give me a thumbs up** 👍

[Alfred Efficiency SuperTool Full Guide](/posts/alfred)

### Raycast

In 2023, `Raycast`, as a rising star in the field of quick search and efficiency tools, directly challenged the status of the veteran tool Alfred. I am mainly a lifetime pack buyer of Alfred, so I haven't studied it инфек深入. You can find comparison articles between Raycast and Alfred online to see which one suits you better.

> To be fair, Raycast's UI and interaction are indeed better looking than Alfred's, but Alfred's Workflow community ecosystem is indeed stronger than Raycast's, so everyone can choose for themselves.

### 🚌 The Charm of Automation

**"Automation" must be a thinking mode that programmers should deeply implant in their "minds."** Whenever we encounter repetitive processes, shouldn't we try to ask ourselves: how frequent is this time-consuming process, and is it worth automating with tools?

Nowadays, there are even companies that have gone public by focusing on automation. So here, I want to focus on discussing with everyone how individuals can maximize automation around them. I will focus on three tools: Alfred Workflow, Apple Shortcuts, and IFTTT.

### AlfredWorkflow

Focuses on automation workflows on Mac. It creates an action flow through a visual programming approach like the one shown below 👇. For example, if I want to search Chrome bookmarks 🔖 using `Cmd + Alt + B`. Community members have already created a workflow for us. We can directly download existing Workflows from Alfred's community Packal and other forums to implement these small daily life automations.

For example, the ones above:

- ChromeHistory: Search Chrome history (in Alfred search)
- GithubRepos: Browse and search your own Github Repos
- Colors: Quickly convert front-end colors (front-end developers will definitely know why this is commonly used) 🙈
- ... and so on

We can also define our own workflows to automate some processes. Using my own case as an example, I define many keyboard shortcuts to bind my daily operations. For example:

- Cmd + Alt + D: Open DingTalk
- Alfred input weather: Query weather
- Alfred input calendar: Open Baidu Calendar (for no other reason than to check public holiday calendars 😄)
- codereview: Enter the group's CR workbench
- ...

It's seamless and very fun, allowing for extensive customization of your own workflows. I previously wrote an article that touched on the Workflow part, interested readers can [read it](https://www.yuque.com/surfacew/fe/gkapz7#Workflows).

### AppleShortcuts

Focuses on automation workflows on mobile phones (iPhone).

It provides nearly 0-code process orchestration, allowing us to access App and some operating system APIs, thereby achieving functional orchestration similar to 👆 Alfred. Isn't it also very powerful? For example, if we want to implement a function that reads content from the clipboard and opens a webpage, we only need to add two simple programming actions below (truly 0 code) to achieve custom process implementation.

Schematic diagram of API provided by Apple Shortcuts:

It can be seen that major companies like Apple are constantly thinking about making coding truly accessible, allowing ordinary novice users to define their own workflows at a low cost. There are many ways to play with Shortcuts, so I won't go into detail here, leaving you with plenty of possibilities to explore.

### IFTTT

🔗: [https://ifttt.com/home](https://ifttt.com/home)

A third-party neutral automation process provider. This tool supports multiple platforms and devices. I use it relatively little, but it can solve some of my cross-platform process problems. Everyone can explore this on their own~

Smart people will definitely use an "automation" mindset to solve problems, so I believe everyone understands the importance of using automation tools well.

## Recommended Mac Software

> In the world of software, many applications are cross-platform, so here I only recommend commonly used software for Mac. Windows users can Google them on their own.

- `Microsoft Todo`: Considering that I have a Windows computer, I chose Microsoft Todo as my GTD tool. The advantages of this tool are its cross-platform nature, simple design, and fulfillment of core needs. Microsoft's synchronization service is also free and stable, so I have been using it.
- `iHosts`: A tool for managing Hosts, allowing for quick switching of Hosts. This is convenient for switching Hosts during development, for example, when needing to switch to a test environment, you can switch directly in this tool without manually modifying the Hosts file each time.
- `Notion`: I have written an article specifically about this tool, so I won't elaborate here. If you are interested, you can check it out: [Notion Tools for Future](/posts/notion-intro), a universal note-taking & information management tool.
- `Infuse`: A video player for NAS, which can play videos directly from the NAS without downloading them locally, very convenient.
- `Moon`: A multi-window management tool that can achieve Windows-like window management effects on a Mac.
- `Contexts`: An enhanced version of Cmd + Tab, which can achieve the effect of Windows' Alt + Tab on a Mac, allowing quick switching to a specific window of an app.
- `SourceTree`: A Git management tool that can manage Git repositories on a Mac and directly view Git commit history within the tool.
- `Bartender`: A Mac Menu Bar management tool that can hide icons in the Menu Bar, making the Menu Bar cleaner and more efficient.
- `Flow`: Pomodoro 🍅 technique, a global Pomodoro technique management tool that can help us better manage time, prevent prolonged sitting, and maintain physical health.


## 💻 Tools That Break Dimensional Walls

Finally, let's talk about non-software "tools". I still believe that as coders, we should spend more on our equipment, just like how Teacher Kevin is more skilled than ordinary hairstylists after using a Dyson hairdryer.

- Your **main machine** must have excellent performance. If finances allow, I still strongly recommend Mac (high-spec) and the Apple ecosystem for front-end development ~
- Get yourself **a 4K screen** (preferably at the office), it will improve your mood. If you are wealthy, get an Apple [PRO DISPLAY XDR](https://www.apple.com/pro-display-xdr/), and I'll be floored.
- Use an **iPad & Apple Pencil** to try the art of digital note-taking, doodling, finding inspiration, and organizing thoughts.
- Automatic standing desk & ergonomic chair: Be kind to your body and spine 🙂 It's an undisputed fact that sitting for long periods makes your butt bigger and fatter 😿
- **HHKB** keyboard ⌨️, I've been using it for a while, and after getting used to the layout, I feel like I'm typing faster... Maybe it's the power of money making even coding feel smoother 🎶 (just kidding)
- ...

## 🎓 Conclusion

Of course, the tools mentioned above are just the tip of the iceberg of tools condensed from collective wisdom in the vast world.

The efficiency gains from these tools often seem small at first, but everyone must know that these gains accumulate and amplify over time. Let's do a simple calculation: if you optimize your efficiency 100 times a day with tools, even if each optimization saves only 5 seconds, the time saved in a year (Alfred can directly calculate the time).

Isn't it shockingly as high as 50 hours, a full **2 days**?! Due to space limitations, if you find this article helpful, please like, save, and share it (why does this sound like a Bilibili three-combo request?) haha. If I have time later, I will share with you how I manage information and knowledge, hoping to bring you some real help.
