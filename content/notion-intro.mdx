---
title: 'Notion: The Future Model of Information Management'
publishedAt: '2022-07-09'
summary: "Notion is the future tool for info management ~"
tags: ['Tools', 'en']
---

## What's great about Notion 🐮🍺

I've always felt that "Notion" defines the next era of personal & team information management software. The main reasons are as follows 👇：

- **Hierarchical Structure**：Notion's Pages and Foldable Blocks enable "hierarchical layering" at different granularities, achieving "stratification" of information；
- **Hyperlink Structure**：Through `@` in Notion, documents can be interconnected like web hyperlinks, building complex topological connection structures；
- **Searchable**：Supports full-text search within the workspace, with an implementation similar to <PERSON>'s search 🔍；
- **Collaborative**：Notion has always been building a collaborative app suitable for teams, ideal for startups to build their own small information systems；
- **Incredibly Rich Content Blocks**：Both structured and unstructured data information can be stored。
   - It provides the concepts of databases and tables to store structured data；
   - It offers various content expression syntaxes supported by Markdown；
   - It provides card embedding capabilities for Twitter / Youtube / Figma, etc., with potential for infinite future collaborations；
   - ...

:::success
📝 It can be said that Not<PERSON>, based on web technology, has built a higher-level web, a richer and more structured one, redefining the form of information collaboration ~ It allows for the definition of personal and team "information systems" at an extremely low cost. If this isn't the future, what is？
::：

- Rapid construction tool for personal information systems (Jarvis) 🛠
- Quickly adapt to changes in personal information system ideas and system upgrades 👍

## Miscellaneous Notes

I recently wrote an analysis report on Feishu, comparing the product feature differences between Feishu and Notion. The specific comparison is here：

[Arno's Analysis Report on Feishu Docs](https://www.yuque.com/go/doc/53323428?view=doc_embed)

2021-10-30 Recently added SyncBlock：

This feature is truly a dream come true. Edit one Block, and all referenced Blocks update, much like the concept of "symbols" or "components" in front-end development. It can open up many new usage scenarios, it's fantastic 👍。

---


2021-03-18 Supplementing a few more points I've felt deeply about recently：

Notion's most fundamental product-driven philosophy：

```typescript
- augment our collective intellect
[Doug Engelbart] 

- amplify imagination 
 [Alan Kay] 

- expand our thoughts far beyond text on paper 
[Ted Nelson] 
```

- Notion's diverse content & open structure => Connects more interactive cards and even dynamic cards.
- Notion's Database Table => Visualization and representation upgrade for data.
   - It's both a DataGrid and a Doc
   - Supports Union relationship queries
- Bidirectional linking structure => Connections allow knowledge to form a network.
- Hierarchical folding structure => Makes layering more natural.
- Global search => Every byte counts ~
- Collaboration: Empowers teams to use this new information processing system to improve system efficiency.
   - Deep version management

---


- Full Web / H5 core architecture design.
- Super small team & rapid iteration.
- Diverse team, diverse ideas, strong entrepreneurial spirit and self-drive.

## Let's talk about Notion's problems

> After talking so much about Notion's good points, let's discuss its existing problems。

- The only regret is that **it is centralized**. Once it's blocked or the service provider goes down, the loss of data assets and the cost of reorganization are enormous. This defines its product tone as a SaaS service, not purely a tool software.
- Notion is only suitable for individuals and small teams. Once the complexity rises to information management for organizations of hundreds or even thousands of people, its capabilities are insufficient. Take permission control, for example； Notion's permission system is still in a rudimentary management stage.
- Notion does not support privatization, which leads to many issues with managing information after employees leave. It feels like Notion's enterprise version might have solutions for this, but when it comes to distinguishing between individual and collective knowledge connections, enterprise-level services still have a long way to go.

# References

- [Notion 的支撑者](https://mp.weixin.qq.com/s/MbWAx_pVa42Y1q8qieRmGA)
- [Web Brutalism, Seamfulness, and Notion](https://www.viget.com/articles/web-brutalism-seamfulness-and-notion/)
