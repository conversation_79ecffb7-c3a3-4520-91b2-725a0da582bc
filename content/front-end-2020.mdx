---
title: '2020 AS A FRONT-END DEVELOPER'
publishedAt: '2020-01-01'
summary: "<PERSON><PERSON>'s summary and thoughts on the recent direction of <PERSON><PERSON> and the work he has been doing."
tags: ['Frontend', 'en']
---

Taking advantage of the "Double Egg" festival, I'd like to review some of the things I've done as a front-end developer in 2020. I've seen many discussions about various technical directions, such as Serverless, WSAM, Flutter, and so on. I thought about it and decided to talk about the things my team and I have done in the front-end field this year and the thinking behind them, all within the context of our business.

Before I begin, let me introduce my team: the **"Proprietary DingTalk" front-end team** under the DingTalk Business Unit of Alibaba Cloud Intelligence. This young team, just over a year old, is leveraging the existing standard capabilities of public DingTalk to serve large clients, especially large organizations like government and state-owned enterprises, meeting their various and often "fancy" business needs.

It is precisely because of this focus on serving large clients and Alibaba's "[Cloud DingTalk Integration](https://baijiahao.baidu.com/s?id=1682706368165790318&wfr=spider&for=pc)" strategy that our entire team has centered its technical layout around these keywords this year: "**Proprietary (Cloud) Deployment**," "**Modularization**," and "**Customization**." Building upon Alibaba Group's existing infrastructure, the team has grown rapidly, driven by the mission of expanding the boundaries of the front-end 2B2G (Business-to-Business-to-Government) domain for a year. So, here I'd like to share our team's explorations and thoughts on these three keywords, hoping to spark further discussion.

## Proprietary Deployment

Mature B2B businesses inevitably involve proprietary deployments. Imagine if your entire product & service suite (both front-end and back-end) had to be deployed and run in a private environment, disconnected from the public internet. In this scenario, the front-end application would:

- Be unable to use CDNs for accelerated resource fetching.
- Have limited internet access.
- Lose access to many mature public internet-based services and solutions.

Initially, it felt quite isolating. Fortunately, to address the challenges of proprietary deployment, the entire team has put a lot of effort into engineering this year:

- Static resource analysis during build time, local aggregation, and outputting build packages.
- Docker image creation for front-end resources, enabling one-click deployment with the operations platform.
- Localization of front-end page scripts for client-side integration (CEF container offline capabilities).
- ...

This solved the basic "survival needs 🍚" of proprietary environment delivery, making project delivery more flexible. However, there's still a lot of room for exploration in this area, especially regarding the deployment model of Alibaba's Mini Program ecosystem under a hybrid cloud architecture, which is a crucial topic for the future.

## Modularization

"**Modularization**" has always been an important theme in program design. This year, the "modularization" challenge our team faced, within the context of Cloud DingTalk Integration, was to transform the "DingTalk" app into a foundational framework. The core components, organization, and features built upon it needed to be customizable and replaceable. This placed higher abstraction standards and requirements on existing business modules:

- Modules need to be "**highly cohesive**," with a converged set of external capabilities.
- Modules need to be "**structured**," supporting plug-and-play and assembly.
- Modules need to be "**standardized**," allowing for extension, customization, and replacement.

Against this backdrop, thanks to the co-creation and intensive input from our server-side and native client colleagues during this period, and influenced by ideas like **DDD (Domain-Driven Design)**, we are attempting to **explore the "DNA" 🧬 of these existing modules**. We aim to extract their immutable aspects and core designs to become the "**module core**," further solidifying them into what we call "**business assets**".

This is essentially a high-level business abstraction, and the process is one of **"continuous refinement"**. It's a mission that each core business module (like chat IM, address book, etc.) will persistently explore over the next 3-5 years. With this, perhaps we can set a "small goal" for ourselves to become the front-end team with the most experience in designing and implementing collaborative application modules in China 😀 wouldn't that be interesting?

## Customization

> You can never predict the peculiar demands your clients will make.
> It's also challenging to firmly reject the requests from your major clients.

With the Cloud DingTalk integration, DingTalk is now "tailored to each individual." Faced with a large number of customized business requirements, our team cannot infinitely increase manpower to meet these demands. Therefore, what we need to build is a "stage," allowing our partners to become the "performers." We hope to have a set of models that enable partners to deeply customize and extend the capabilities of the DingTalk platform, exploring new possibilities!

To achieve this, based on the previous "Proprietary Deployment" and "Modularization," we also need to find ways to "empower" our partners to extend and customize the existing "platform capabilities." We must open up everything that can be opened. Thanks to classic programming ideas like **AOP**, **SPI**, and **IoC**, we have new tools in the front-end to design for extension and customization.

This area is currently in a rapid initial stage, with the team working hard to experiment and find a viable solution that allows for third-party customization of modules. On this road, I've deeply realized that front-end development can also benefit from these classic program design ideas. For instance, frameworks like [Inversify](https://www.yuque.com/surfacew/daily-learn/gcl771), which focus on IoC and DI, are now relevant to the front end. It has also made me understand that as a front-end developer, one should never set limits for oneself; this way, you can go further ~

## Finally

Lastly, I'd like to share the most important realization I had this year:

- The business tone determines the technical tone.
- The business scale determines the technical scale.
- The business diversity determines the technical diversity.
- ...

A thriving business will bring about **boiling** technology! This year, together with the team, I have:

- Researched the group's cross-end materials [Rax](https://rax.js.org/) and implemented them in our business.
- Established a technical stack of full TS, React Hooks, and Redux.
- Experimented with various playful uses of [Rx.js](https://github.com/ReactiveX/rxjs) in the messaging (IM) module.
- Tried out new technologies like [Flutter](https://flutterchina.club/) and WebAssembly, looking for ways to integrate them into our business.
- Contemplated how to make [Micro Frontends (MS)](https://www.yuque.com/surfacew/daily-learn/vgolwq) a viable solution for keeping mid-to-back office applications "forever young" and vibrant.

I also experienced the team's expansion from a few people to over a dozen. This process taught me that every team, at different stages, has its own rhythm, and there are certain patterns to follow in what needs to be done. Especially when it comes to technology, things cannot be rushed or delayed; blindly trying to speed things up will backfire. In hindsight, it's still about taking steady steps 👣 and solidly building technical talent is the way to go.

Well, haha, this can be considered as submitting a relatively satisfactory report card for 2020 😉 I believe that **2021 will undoubtedly be a year of intensified organizational digitalization ~ **The road is long and has no end, and I will search up and down for it!

---

Finally, I would like to give a little advertisement for this young front-end team — the Proprietary DingTalk front-end team welcomes like-minded individuals to define the future of the 2B2G front-end R&D system together ~ Feel free to private message me or send your resume to my email 📮 _qingnan.yqn@alibaba-inc.com_, and I will shamelessly add you on WeChat 😀
