---
title: '🛰️ Systematic x Systemic theory'
publishedAt: '2025-05-26'
summary: '🧠 Systematic x Systemic theory and its application based on AI'
tags: ['Theory', 'en']
---

# Systematic x Systemic theory

## Elementary Structure of a System

> system should be purpose driven, values driven, and has its meaning and vision for stakeholders. its about "why" of this system exist?

### System Purpose & Vision

- Problem (Contradiction) & Purpose (Goal) & Values
  - Vision
  - Values
  - Meanings
  - Goals (short-term, medium-term, long-term)
  - Stakeholders View
  - ... (KPI / OKR / ...)

> it's about "what" of this system.

### System Functionality


- Functionality (I/O)
  - Environment (Open / Close level and scope)
  - Product & Service
  - Inputs / Outputs / Throughput
  - ...

> it's about the insights of "how" this system is structured and organized.
> states, structure, and genes(knowledge and experience behind scene) are the core elements of a system.

### System Structure

- States
  - Current State
  - Evolved State
  - ...

Sate important Patterns:

* the quantification purpose and function of "core metrics." (Quantifiable, accurately quantifiable), must be considered & designed.
* Extract features deemed understandable and meaningful, and then quantify them.
* Combined with "purposefulness," extract and refine the quantification & dimensional definition of attribute changes for the next transition state, leading to considerations of the system's "abrupt change" and "stable" states.
* Consider the attributes of "similar" systems (or systems with "similarity").
* Focus on the selection and evaluation methods for attribute quantification and digitization.
* Consider the evolution brought about by the system's related "ordered fluctuations" and "optimized evolution."
* Consider the relationship between "quantitative change" and "qualitative change."

---

- Architecture (Elements & Relations) Structure
  - Elements -> research on how structure influences functionality
  - Relations (Connections)
    - sub elements v.s. super elements
    - Isomorphism v.s. Heteromorphism
    - Generalization v.s. Specialization
    - Extends, Reduces, Modifies, Replaces
    - Contains, Includes, Excludes ...
    - Competition, Cooperation, Collaboration
    - Symbiosis (Mutualism, Commensalism, Amensalism), Parasitism
  - Flows (Information, Energy, Matter, Business...)
  - Feedback Loops
  - Boundaries
  - ...

Architecture important Patterns:

- Structure Patterns
  - Symmetry and Asymmetry
  - Hierarchy / Hierarchical Nature -> Layeredness -> continuity and distinct stages in functional and structural development.
    - Continuity
    - Phased nature
  - Control & Coordination & Self-Organization -> Cellular Automata
  - Replicability / Substitutability
  - Centralization vs. Decentralization
  - Atomic structures (unconscious structures) construct conscious structures.
  - Adaptability / Resilience: numerous corrective and feedback loops.
  - Meta-resilience: is the ability of a system to maintain its capacity for resilience even when facing novel, unexpected, or large-scale shocks.
  - Fixed Points: exploring the structure of fixed points.

---

### System Knowledge & Experience

- Genes
  - Truth & Universal Laws
  - Data -> Information -> Knowledge -> Experience -> Wisdom -> Truth & Universal Laws
  - KESW (Knowledge, Experience, Skills, Wisdom)
  - Principles, Patterns, Theories, Standards, Strategies, Rules, Laws, ...
  - ...

### System Dynamics

> thinking in a dynamic way, it's about "how" of this system evolve and change.

- Dynamics (Change)
  - Changes / Tracks and Trends -> Find inner / outer reasons
    - mutations and stability
  - Evolution (Evolutions)
    - Evolutionary Paths
    - Evolutionary Mechanisms
    - Evolutionary Patterns
  - Elaborations -> things we design / done / achieved in levels to mutate the system
    - Elaborations (Artifacts, Products, Services, Processes, Systems)
    - Elaborations as a System
  - ...

Patterns of Dynamics:

* time: past, now, future
* entropy: order, disorder, chaos
* stability: stable, unstable, metastable
* evolution: evolution, devolution, revolution
* change: incremental, radical, disruptive
* feedback: positive, negative, neutral
* leverage: high leverage, low leverage, no leverage
* buffer: buffer, no buffer, over-buffered
* time-delays: short, medium, long
* self-organization: self-organizing, self-regulating, self-adaptive
* multiplicity: single, multiple, many-to-many
* multi-diversity: convergence, divergence, multi-stability -> mono, poly, and multi-stability

## Features of a System

### Systematic View

- Methodical
- Structured
- Linear
- Analytical Process (SOP), Workflows
- Models & Frameworks -> mathematical, logical, and algorithmic

### Systemic View

- Holism and Emergence (NoneLinear & Chaotic): The Whole Beyond the Parts
  - Anti-Fragility: Resilience and Adaptability
- Interconnectedness and Interdependence
- Feedback Loops: The Engines of System Dynamics
- Dynamics and Change: The Evolution of Systems
- Boundaries and Environments and Interfaces: Defining the System
- Hierarchy and Organization: The Structure of Systems
- Self-Organization and autopoiesis
- Homeostasis/Equilibrium: Homeostasis
- Equifinality & Multifinality

### Compare Systematic & Systemic

| Characteristic     | Systemic Thinking                                                    | Systematic Thinking                                                     |
| :----------------- | :------------------------------------------------------------------- | :---------------------------------------------------------------------- |
| **Focus**          | Relationships, interactions, context, the whole                      | Parts, components, sequence, procedure                                  |
| **Approach**       | Holistic, contextual, iterative, synthesis-oriented                  | Linear, step-by-step, analytical, reductionist                          |
| **View of Whole**  | Emergent properties; whole is different from sum of parts            | Whole understood by analyzing parts; sum of parts                       |
| **Analysis Style** | Contextual, pattern-seeking, feedback-focused                        | Linear, cause-effect analysis                                           |
| **Understanding**  | Through synthesis and understanding relationships within context     | Through analysis of components and defined procedures                   |
| **Key Goal**       | Understand _why_ a system behaves as it does; see the bigger picture | Understand _how_ a system works; follow a method for consistent results |
| **Perspective**    | Crucial; boundaries are perspective-dependent                        | Often considered less important; assumes objectivity                    |

## System Analysis Methodologies & Techniques

By combining the systematic and systemic views, we can develop a systematic analytic process in a more comprehensive and integrated way.

### PSMs and SSMs

In the early stages of system analysis, especially when facing complex, ambiguous, or ill-defined "wicked problems," problem structuring plays a crucial role. It doesn't directly seek solutions but rather focuses on clearly defining "what the problem actually is," laying a solid foundation for subsequent in-depth analysis.

Problem Structuring Methods (PSMs) are a series of "soft operations research" methods designed to help groups with different backgrounds reach consensus on problem focus and make commitments to subsequent actions. These methods are particularly suitable for handling complex situations with multiple interactive issues, involving numerous stakeholders, full of uncertainty, and without a single clear formulation. System theory itself, with its emphasis on interconnections, feedback, adaptability, and resilience, provides a natural framework for constructing complex problems and developing holistic solutions.

Effective problem structuring is essentially participatory and fully considers the perspectives of multiple stakeholders. This highly aligns with the core principle of "multiple perspectives" in systems thinking. For example, a problem structuring method proposed for complex social decision-making (such as sustainable development of large infrastructure) explicitly draws from systems theory and cognitive psychology, and aims to accommodate different stakeholder perspectives. One of its core tools is the use of "trilemmas" to characterize and critique a set of competing forces.

PSMs such as Soft Systems Methodology (SSM), Strategic Choice Approach, and Strategic Options Development and Analysis (SODA) provide structured approaches for handling "soft," human-centered system problems where objectives are unclear or conflicting. SSM is particularly suitable for analyzing unstructured complex problem situations involving human-technology interactions.

### Systematic Analytic Process

- **Clearly defining the problem or goal**: This involves establishing specific and unambiguous objectives at the outset.
- **Gathering and organizing relevant information**: A thorough collection and structuring of data pertinent to the problem is a prerequisite for analysis.
- **Breaking down the problem**: Complex issues are decomposed into smaller, more manageable parts to allow for focused examination.
- **Analyzing data objectively and thoroughly**: Each component or piece of information is subjected to careful, objective scrutiny.
- **Generating and evaluating potential solutions**: Based on the analysis, various solutions are proposed and assessed against predefined criteria.
- **Implementing the chosen solution and monitoring its effectiveness**: The selected solution is put into action, and its performance is tracked to ensure desired outcomes and allow for adjustments.
- **Documenting and following standard work**: Adherence to established procedures and the use of tools like process check sheets are characteristic of maintaining and improving systematic processes.

### Systemic Analytic Process

#### System Function Analysis: Understanding System Behavior and Functional Flows

For visualizing and documenting functional design, diagramming tools are often used, such as activity diagrams in SysML, SV-3 or SV-10 diagrams in the UAF framework, and Functional Flow Block Diagrams (FFBDs). FFBDs are particularly common, using rectangles to represent functions and arrows to show flows, sequences, or any type of input-output relationships between functions.

#### System Dynamic Analysis: Exploring System Behavior Patterns

- System Dynamics Modeling Fundamentals
- Behavior Patterns: Stability, Oscillation, Growth, Collapse

#### System Thinking Visualization Tools

- Causal Loop Diagrams (CLDs)
- Stock and Flow Diagrams
- The Iceberg Model
- Behavior-Over-Time Graphs (BOTGs)
- Connection Circles and Rich Pictures

#### Building Systematic Thinking Paths and Enhancing Analytical Capabilities

- Defining System Boundaries and Identifying Key Elements: Before analyzing any system, the first and crucial step is to clearly define the system boundaries to be studied and identify the key elements that are essential for understanding and solving the problem.

### Building a Personalized Systems Thinking Methodology

Building a path map from theory to practice means organically integrating the content from the previous sections—the foundations of systems thinking (Part 1), systematic methods of system analysis (Part 2), core methods and tools (Part 3), and strategies for building systematic thinking paths (Part 4)—into a coherent, operational analytical framework.

#### Problem Perception & Initial Definition

- **Core Activities**: Identify problem symptoms, sense complexity. Use the "Iceberg Model" for initial exploration, thinking about patterns behind events.
- **System Principles Application**: Initial perception of wholeness, interconnectedness.
- **Tool Support**: Behavior Over Time Graphs (BOTGs) to record initially observed dynamics.

#### Problem Structuring & Boundary Setting

- **Core Activities**: Interact with stakeholders, collect multiple perspectives. Use rich pictures, connection circles, and other tools for divergent exploration. Clarify analysis goals and scope, define initial system boundaries.
- **System Principles Application**: Multiple perspectives, boundaries, wholeness.
- **Tool Support**: Rich pictures, connection circles, stakeholder analysis, goal hierarchy analysis. Soft Systems Methodology (SSM) concepts can be introduced at this stage.

#### System Structure & Function Analysis

- **Core Activities**: Within defined boundaries, identify key system components and their static connection relationships (structural analysis). Analyze the functions that these elements achieve individually and collaboratively (functional analysis).
- **System Principles Application**: Interconnectedness, hierarchy, wholeness.
- **Tool Support**: Component diagrams, flowcharts, Functional Flow Block Diagrams (FFBDs).

#### System Dynamic Mechanism Exploration

- **Core Activities**: Deeply analyze feedback loops, time delays, and nonlinear effects that drive system behavior. Use Causal Loop Diagrams (CLDs) to depict causal relationships and feedback structures. Identify potential system archetypes.
- **System Principles Application**: Feedback, dynamics, emergence, nonlinearity.
- **Tool Support**: Causal Loop Diagrams (CLDs), system archetype analysis.

#### (Optional) Quantitative Modeling & Simulation

- **Core Activities**: For problems requiring more precise prediction or policy testing, transform qualitative CLDs into quantitative stock and flow diagrams and conduct computer simulations.
- **System Principles Application**: Feedback, dynamics, cumulative effects.
- **Tool Support**: Stock and flow diagrams, system dynamics simulation software.

#### Leverage Point Identification & Intervention Strategy Design

- **Core Activities**: Based on understanding of system structure and dynamics (especially feedback loops and archetypes), find "leverage points" that can produce significant, lasting positive changes at relatively small cost. Design and evaluate alternative intervention strategies, considering their potential short and long-term impacts and possible unintended consequences.
- **System Principles Application**: Feedback, equifinality, wholeness.
- **Tool Support**: Leverage point analysis (referencing Meadows et al.), scenario analysis.

#### Action, Monitoring & Learning

- **Core Activities**: Implement selected intervention strategies. Continuously monitor changes in system behavior, evaluate intervention effectiveness. Learn from practice, reflect on analysis process and intervention results, adjust mental models and subsequent actions.
- **System Principles Application**: Dynamics, feedback, adaptability.
- **Tool Support**: Behavior Over Time Graphs (BOTGs) to track changes, regular review and reflection meetings.

## AI integration space & exploration

> Chances and Opportunities behind the AI Revolution

Beyond augmenting systemic and systematic thinking in isolation, AI holds the potential to facilitate a more fluid and integrated interplay between these two crucial cognitive modes. AI can act as a bridge, translating insights from one mode into actionable inputs for the other.

- The inherent nature of many AI systems, particularly in machine learning, involves iterative refinement through feedback loops (e.g., backpropagation in neural networks, reward signals in reinforcement learning).
- Human-AI collaboration is emerging as a key paradigm for tackling complex problems.

Work in a cooperation way with AI.

### Systematic x AI

> AI Fortifying Systematic Processes: From "System 0 Thinking" to Automated Structured Analysis and Logical Progression

- **Information Gathering and Organization**: AI tools, including Large Language Models (LLMs) like ChatGPT, can assist in formulating research questions, developing comprehensive search strategies for literature reviews, and automating the screening and extraction of relevant information from vast document repositories.
- **Data Extraction and Processing**: Technologies like Intelligent Document Processing (IDP), powered by machine learning, can automatically extract and validate data from both structured and semi-structured documents such as invoices or research papers. AI can also analyze large volumes of unstructured data, like emails, to perform tasks such as document validation and information routing.
- **Problem Decomposition and Solution Generation**: AI problem-solving methodologies often involve exploring potential solutions through sophisticated reasoning techniques and computational modeling. Fundamental to this are AI search algorithms (both uninformed, like Breadth-First Search, and informed, like A\* search) that systematically explore complex problem spaces to identify optimal or near-optimal solutions.

### Systemic x AI

> AI Amplifying Systemic Insight: Modeling Complexity, Unveiling Interdependencies, and Identifying Feedback Loops

- Tools and platforms are emerging that leverage AI to make the creation and interpretation of systems thinking models, such as system dynamics models or causal loop diagrams, more accessible and intuitive.
- AI can play a crucial role in identifying feedback loops and leverage points within these complex models. By analyzing system dynamics, AI algorithms can highlight critical feedback mechanisms that drive system behavior and pinpoint leverage points.
- Network analysis, a field significantly enhanced by AI and machine learning, directly supports the systemic focus on relationships and interconnections.
- Causal reasoning. Moving beyond mere correlation, AI techniques are being developed to infer cause-and-effect relationships within systems.

| Thinking Mode  | Specific Cognitive Task                     | AI Technique/Tool/Concept                                                                | Mechanism of Augmentation                                                                                          | Key Benefits                                                                                                  |
| :------------- | :------------------------------------------ | :--------------------------------------------------------------------------------------- | :----------------------------------------------------------------------------------------------------------------- | :------------------------------------------------------------------------------------------------------------ |
| **Systemic**   | Identifying Interconnections & Wholes       | Network Analysis with ML, Knowledge Graphs, Large-scale Data Mining                      | Processing vast datasets to reveal hidden patterns, relationships, and system-wide structures                      | Enhanced holistic understanding, discovery of non-obvious links, mapping complex systems                      |
|                | Modeling Complex Systems                    | System Dynamics Co-pilots (e.g., SYMBIOSIS), Causal Discovery Algorithms                 | Translating diagrams to natural language & vice-versa, inferring causal structures from data                       | Increased accessibility of modeling, deeper causal understanding, simulation of system behavior               |
|                | Analyzing Feedback Loops & Leverage Points  | AI-driven simulation, Reinforcement Learning, Anomaly Detection in time-series data      | Identifying critical feedback mechanisms, predicting impacts of interventions, pinpointing sensitive system points | Better prediction of system behavior, identification of effective intervention strategies                     |
| **Systematic** | Structured Data Analysis & Information Org. | NLP for literature review, Intelligent Document Processing (IDP), "System 0 Thinking"    | Automated information extraction, data validation, offloading repetitive data processing tasks to AI               | Increased efficiency, reduced human error, faster information synthesis, focus human cognition on strategy    |
|                | Logical Deduction & Problem Decomposition   | Automated Theorem Provers, AI Search Algorithms (BFS, DFS, A\*), Constraint Solvers      | Exploring solution spaces methodically, breaking down problems into sub-problems, verifying logical consistency    | More rigorous analysis, generation of optimized solutions, automated reasoning for well-defined problems      |
|                | Process Automation & Standardization        | Robotic Process Automation (RPA) with AI, AI-driven workflow management                  | Automating rule-based tasks, ensuring adherence to standard procedures, optimizing workflows                       | Improved consistency, reduced operational costs, enhanced process efficiency                                  |
| **Integrated** | Bridging Holistic Insight & Action          | Human-AI Collaborative Platforms, AI for translating systemic models to actionable plans | Facilitating dialogue between systemic understanding and systematic implementation, iterative refinement cycles    | More effective translation of insights into practice, adaptive problem-solving, synergistic human-AI outcomes |
|                | Bias Detection & Mitigation in Processes    | G-AUDIT framework, AI for fairness in algorithms                                         | Identifying potential biases in datasets or systematic procedures that could lead to skewed outcomes               | More equitable and reliable outcomes from both systemic analyses and systematic implementations               |

## Thinking Techniques Applied to S&S research

> Be able to "mathematize" and "abstract" research, forming models that construct systems. Applicable for systematic analysis of abstract events and entities to understand essence, make predictions, and solve problems.

> From a formal perspective, we can describe structures and relationships using axioms, theorems (laws), etc. This includes first-order, second-order, and even third-order sequences.

Models

- The ICEberg Model
- Feedback Loops
- Stock and Flow Thinking
- Time Delays
- Shifting the Burden Archetype
- Boundaries and Perspectives
- Ladder of Inference
- Second-order Thinking
- Bottleneck Model
- First Principles Thinking
- Antifragility Thinking
- ...

-> see [mental-models of Arno](https://arno.surfacew.com/posts/models) S&S thinking related models section

Frameworks

- Senge's Five Disciplines: Developed by Peter Senge, this framework outlines five essential disciplines for building "learning organizations" – organizations capable of continuous adaptation, innovation, and achieving desired futures.
- SWOT Analysis (Strengths, Weaknesses, Opportunities, Threats)
- Failure Mode and Effects Analysis (FMEA)
- PESTEL Analysis (Political, Economic, Sociocultural, Technological, Environmental, Legal factors)
- Porter's Five Forces (analyzing industry competition)
- McKinsey 7S Framework (analyzing internal organizational alignment)
- Business Model Canvas (visualizing business models)
- BCG Matrix (portfolio analysis)

Tools & Patterns

- Causal Loop Diagrams (CLDs)
- Stock and Flow Diagrams
- Behavior-Over-Time Graphs (BOTGs)
- Connection Circles
- Concept Mapping
- Process Mapping (Flowcharts, etc.)
- UML standard modeling language

---

- Root Cause Analysis (RCA)
  - 5 Whys
  - Fishbone (Ishikawa) Diagrams
- Decision Trees
- Algorithmic Reasoning
- Checklists
- Decomposition / Work Breakdown Structure (WBS)
- MECE Principle (Mutually Exclusive, Collectively Exhaustive)

## References

- [Systemic and Systematic Thinking in the AI Era: Augmentation, Theory Development, and Intelligent Research Systems](https://docs.google.com/document/d/1qzcJDNjgKa9eezUbyzRi9jihZlxXE7A_VasxQyCy2l4/edit?tab=t.0)

Docs Upgrade:

- 2025-05-26: more details on my experience added to the theory itself
