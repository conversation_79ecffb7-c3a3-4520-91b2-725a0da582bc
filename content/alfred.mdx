---
title: 'Alfred Productivity Tool'
publishedAt: '2019-07-09'
summary: "A good workman sharpens his tools first."
tags: ['Tools', 'en']
---

> As a senior user, I have already called it 100,000+ times. So I brought this three-year-old article to <PERSON><PERSON>, hoping to benefit more people ~

> A good workman sharpens his tools first. The efficiency Alfred unleashes on a Mac is unprecedented!


:::info
July 09, 2019:
As a senior user, I have already called it 100,000+ times. So I brought this three-year-old article to <PERSON><PERSON>, hoping to benefit more people ~
:::

## Foreword

As the most powerful productivity tool on Mac, <PERSON> optimizes quick launch and search functions based on Spotlight (MacOS X's built-in search and quick launch engine). It also introduces powerful extension functions like Workflows, making it a "tool platform" software with unlimited automation potential. You can use it to realize almost any automation idea.

Although I am not a hardcore Alfred user, as an ordinary "player", I already use <PERSON> with high frequency in my daily work.

To illustrate the potential value of <PERSON>, let's do a simple calculation. Assume that I can maintain the current average of 45.2 awakenings per day (in fact, the recent usage frequency is much higher than 45.2 times). If each Alfred operation can save me `5s` (often some complex operations will take longer than 5s, such as finding a deeply hidden file), then over 5 years, it can save **4.7** days. In other words, you can spend 4.7 fewer days working on the computer. In other words, **<PERSON> extends your life by 4.7 days**！

```shell
5 * 365 * 45.2 * 5 / 3600 / 24 = 4.773726852 days
```

It can be seen that Alfred is a productivity tool for workers who rely heavily on computers every day. Given that previous scattered articles on the Internet about Alfred were not systematic or up-to-date, I plan to make a "family bucket" here to help readers better use Alfred to improve their daily work efficiency！

So next, I will lead you to experience the charm of Alfred ~

## Software Installation

Alfred only supports Mac operating systems. It is recommended to download the application from the [official website](https://www.alfredapp.com/) or the AppStore. Its latest version is currently: `3.0.3` 。

Alfred's basic functions are free, but some powerful extension functions and Workflows require purchasing an upgrade package. I personally recommend that those who can afford it buy a genuine license code, because this software is definitely worth the price (a personal license is 17 euros, equivalent to about 126 yuan, and the family version is cheaper)。

## Basic Function Configuration and Use

### Basic Configuration

- It is recommended that you configure Alfred's activation key to a shortcut you are familiar with. I personally like to use `⌥ (Option) + Space` to activate it, and I also choose to disable Apple's built-in Spotlight search. Please Baidu how to disable Spotlight yourself.
- In the Default Results tab on the right panel of Features, we can let Alfred selectively choose the content types and addresses we want to search from the metadata index service provided by Apple Spotlight.
   - When configuring, _the ideal is to only put the content you expect to retrieve into the Search Scope_，which can greatly improve the speed at which Alfred displays search results.
   - For the types of files to retrieve, naturally, the fewer the better. For me, I rarely use retrieval of file types such as pictures and compressed packages, so I removed them. Note: **It is not recommended to search all file types here** because the search results will not only be slow, but the quality of the search results will be very poor, and a lot of system-generated file information will be searched out.
   - Secondly, on the `Advanced…` button on the right, you can configure custom file types. For example, I configured MindManager's `.mmap` and markdown files' `.md` here. You can add corresponding file types according to your own needs to let Alfred retrieve these files. Just drag the file types that Alfred cannot retrieve into the pop-up box to add them.
- Alfred can also choose default styles or customize the appearance. Friends who like to tinker with DIY styles can modify the style of the Alfred interactive panel in the `Appearance` panel.
- The `Advanced` configuration includes advanced configuration functions such as "cache cleaning" and "metadata reconstruction". Generally, no configuration is required. Please do not proceed blindly if you do not know how to use them.
- Alfred also supports using the "mobile version of Alfred" to control the desktop. For details, please refer to [this article](http://sspai.com/28137)，I will not go into details here。

### Basic Functions

#### Search Function

- With the above configuration, I can search for various specified types of files very quickly. On the corresponding search results:
   - We can use the `↑↓` keys to switch options and press Enter to open files or directories.
   - You can also use `⌘ + number key` to quickly open the corresponding file or directory.
   - Clicking the `fn` key on the selected file or directory can trigger its **additional operations**, refer to the configuration.
   - Using `⌘ + Enter` on the corresponding _file_ can **enter the folder where the current file is located**.
   - Alfred also supports "**fuzzy search**"，which can effectively match the corresponding search content.
- Alfred also supports searching for applications.
   - Note: You need to check whether `/Applications` is in the configured directory of Alfred's searchable scope.

#### Web Search

- Alfred has many built-in quick searches that can quickly search Internet information.
   - As marked by 1 in Figure 1 below, enter `amazon` in Alfred and press Enter, then enter the corresponding product you want to search for, and Amazon will automatically open in the browser and display the search results.
   - Such a configuration can also add custom search items through the button marked by 2 in the figure. The figure adds a custom Taobao search shortcut. This makes it so that I no longer have to load the Taobao homepage and be forcibly distracted when shopping.
- Alfred can remember the URLs you have typed in Alfred and record them in the local database. The next time you enter a URL, you can quickly find it. For example, I once entered the URL of 12306, and now I only need to:
- Alfred can also open the browser's default search engine to search after entering the content by clicking the `⌃(Ctrl) + ↩︎(Enter)` key combination.

#### Calculator

- Alfred also supports calculator calculations. You can simply enter `22 * 33` to calculate the result. After pressing Enter, it will be automatically saved to the clipboard for quick copying and use.
- Alfred also supports the calculation of complex expressions, but it needs to start with `=`, for example: `=log2(34) + sqrt(64/2*2^3)` expression.
   - I used to love using this feature to calculate accounts, it's very powerful.

#### Dictionary and Contacts Search

- Alfred has a built-in dictionary search and uses Apple's built-in dictionary software. It can be activated by the keywords `define` and `spell`. It is worth mentioning that `spell` can help you with fuzzy spelling. Sometimes when you forget how to spell a word, you can use it. It feels surprisingly useful.
- Alfred also has a built-in contacts search, linked with Apple's built-in contacts. Entering the name of a friend can view the corresponding business card, and you can also copy the corresponding information.

#### Clipboard and Snippets Access

This is a very useful feature of Alfred. It is recommended that you choose your own shortcut keys in the configuration and enable caching for copying pictures and files. Alfred also supports custom Snippets (character fragments) that can insert your defined text after the current cursor through keywords or shortcut keys.

#### System Functions

Alfred also supports quick commands such as locking the screen, shutting down, emptying the recycle bin, and sleeping. My commonly used recommendations are as follows：

- `lock` 1s to lock the screen, let's go eat ~
- `empty` empties the recycle bin, instantly refreshing for OCD patients.
- The `eject` command can eject disks, memory cards, or virtual disk images, such as disks mounted after `.dmg`.

For temporary `shell` commands, Alfred can enter the `>` character in the activated input box to start `Terminal` to execute the command string after `>`.

#### Other Extensions

- Alfred natively supports `1Password` as a great tool for password reminders。

## Workflows

### Using Workflows

Workflows are automation scripts and programs developed by yourself or developers on the internet to improve daily efficiency. Workflows can be found and downloaded from the resource sites listed later. After downloading, double-clicking them will automatically install them into the Workflows list.

Workflows are very powerful. Here, I will list a few of my favorite Workflows that significantly boost daily efficiency as a demonstration. Readers are encouraged to identify their own pain points and find or design their own Workflows.

- `Chrome History` and `Chrome Bookmarks` can be used to search Chrome\'s bookmarks and history (supports fuzzy search).
   - Note: I have customized the hotkey for Chrome Bookmarks to `Ctrl + Option + Command + B` (a global high-priority hotkey that will override internal application hotkeys).
   - The image below shows the Bookmarks application; History is similar.
- `Evernote`: Supports global search for Evernote. This feature is excellent and powerful, helping to quickly locate specific Evernote entries.
- `Mail.app Search`: Supports global search for Apple\'s native Mail application.
- `Airdrop`: Quickly opens Mac\'s AirDrop feature.
- `Dash`: Global quick search for code documentation. Mom no longer has to worry about my slow API lookup speed.
- `Sublime Text Projects`: Automatically searches for Sublime Projects within the indexed scope. A developer speed-up tool, I personally like this one very much as it loads project files directly, very fast and efficient.
- `Relaunched`: Restarts unresponsive applications.
- `Show Desktop`: One key to a windowless desktop, instantly relaxing.
- `TerminalFinder`: Switch between Terminal and Finder.

### Designing Workflows

For Workflow design, refer to the official developer documentation and [this article on how to develop them](http://myg0u.com/python/2015/05/23/tutorial-alfred-workflow.html). I won\'t go into detail here.

### Workflow Resources

- [Developer-specific Workflows series](https://github.com/zenorocha/alfred-workflows) This collection of workflows maintained on Github is fantastic, addressing many development pain points. Here are a few I find very useful:
   - The SourceTree workflow can directly search for projects in SourceTree and open them quickly.
   - Quick indexing for npm / gem.
   - Caffeinate to prevent the computer from sleeping.
   - Quick emoji lookup.
   - Quick search for personal/global GitHub projects.
- [Alfred Forum](http://www.alfredforum.com/): Mostly plugins developed by individual developers to solve specific problems. You can easily find them on Google by searching `alfred + problem keyword`.
- [Workflows Chinese Site](http://www.alfredworkflow.com/): A large collection of Workflows curated by Chinese users; there\'s always something to suit your needs.
- [Workflows Official Site](https://www.alfredapp.com/workflows/): Officially recommended "must-have" Workflows.
- [Packal](http://www.packal.org/): Officially recommended Workflow sharing site.

## Conclusion

Due to length constraints, further details and in-depth reading for some content can be found in the reference articles below. It is recommended that readers explore and play with this productivity-boosting tool for Mac to save time and effort, and let efficiency soar ~

## References

- [Alfred 3.0 New Version Explained](http://sspai.com/34468)
- [How to Use Alfred to Improve Operational Efficiency](https://zhuanlan.zhihu.com/p/19985861)
- [Learning Alfred from Scratch (Part 1): Basic Functions and Settings](http://sspai.com/32979?preview)
- [Learning Alfred from Scratch (Part 2): Significantly Improve Operational Efficiency with Alfred - workflow](https://zhuanlan.zhihu.com/p/19986749)
- [OS X Efficiency Launcher Alfred Explained with Usage Tips](http://sspai.com/27900/)
