---
title: '🤯 Why I Create Elaboration Studio'
publishedAt: '2024-10-29'
summary: "The creation of Elaboration Studio represents a significant step towards redefining how individuals approach complex problem-solving and idea development. By integrating advanced AI capabilities with user-friendly features, the platform not only streamlines the process of organizing thoughts and resources but also fosters a culture of collaboration and innovation."
tags: ['Product', 'AI', 'en']
---

> It’s a tool for thinking, writing and collective Intelligence.

[✨ e-studio.ai](https://e-studio.ai)

## 🙋🏻‍♂️ Problems Define

Elaboration Studio is designed to assist individuals in **elaborating ideas** and **solving problems** using AI technology. It primarily focuses on addressing the following challenges:

- How can one effectively organize complex information relevant to the problem at hand?
- How can the full potential of AI technology be harnessed using existing cutting-edge tools and theories to enhance performance?
- How can a thinking system be developed and continuously evolved to adapt to new information and challenges, ensuring ongoing improvement and innovation in problem-solving methods?

To address these questions, I aim to seamlessly integrate the `Workspace`, `Elaboration Docs`, `Elaborators` (AI agents), and `Chat` features into a unified platform. I will introduce each functionality individually and explain the rationale behind their design.

## 📦  Context all in one workspace

> Context Engineering Required.

Properly preparing `context`, especially user-related information, is essential for enhancing the overall user experience. Organizing this information allows AI to understand and respond effectively to user queries while maintaining relevance and accuracy. This ensures that the AI delivers tailored responses that meet individual needs.

In Elaboration Studio, we utilize a `Workspace` to organize and manage all relevant information, enabling users to access and utilize context effectively within a single, streamlined environment.

The workspace integrates various tools and functionalities that enhance user interaction with AI, providing streamlined access to documents, files, agents, and chat features—all in one place. This integration fosters a cohesive environment where users can efficiently manage their resources, collaborate seamlessly, and leverage AI capabilities to address their specific needs and inquiries.

> `Docs + Agents + Chats + Resources` in one workspace.

In our workspace, we aim to incorporate several key elements:

- **Elaboration Docs**: These documents are structured like Notion blocks, utilizing modular components that allow for flexible organization and easy integration of various content types.
- **Elaborators**: These specialized AI agents assist users by providing tailored responses and insights based on the context and information available within the workspace.
- **HistoryChats**: This feature enables users to engage in conversations with AI about historical events, offering context and insights selected by the user, and even storing memories over time.
- **Elaboration Resources**: By organizing related files, such as PDFs and media files, as resources, we ensure easy access and retrieval.

To effectively organize these elements within the workspace, it is crucial to facilitate user navigation and access to relevant information. This seamless integration of ideas promotes efficient collaboration on the platform.

In **Elaboration Studio**, we plan to represent these different types of information resources using graphs or specialized diagrams.

Next, let’s discuss the key element of information organization: **Elaboration Docs**. These serve as foundational building blocks within the workspace, enabling users to create, edit, and collaborate on structured documents that integrate seamlessly with AI-driven insights and contextual information.

## 💠 Elaboration Docs

> We are not notes taking apps. We learn from notion, but in a more simple and elegant way. Focusing on writing simplicity. Focusing on highly customized AI integration.

Elaboration docs has a clean, simple and elegant editing content experience which makes it is easy to collaborate with other apps such as `Arc` browser.

In Elaboration doc, we learn from `Notion’s Blocks` driven architecture and content writing experience, which allows users to create and organize content in a flexible and intuitive manner, enabling seamless integration of AI-driven features for enhanced writing and collaboration.

- **Article writing with AI as a Copilot**, instant completion with AI technology has revolutionized the way we write articles by providing real-time suggestions (based on the article and context you write) and enhancing the overall efficiency of the writing process. Or you can trigger AI completion via shortcuts manually, which provide a great writing experience with AI.

- **Chat doc with AI**: User can easily use those types of content to interact with AI via Prompt in Doc or Chat directly.

- **Rich context-based QA** leverages a variety of sources such as documents, tools, and user interactions to provide comprehensive and accurate answers to user queries.
- **Rich Types of Blocks**, we preset some of `AI driven Blocks`  to collaborate your content with preset AI blocks
  - **Fin. Query**: you can ask question to fetch Financial related domain data and ask AI to analysis with you.
  - **Prompter**: you can insert prompt block and directly prompt inside your doc and convert the content or quote related content in this article as context to AI.
  - **Elaborator:** offer a way to interact with pre-built AI agent directly in your doc.
  - **Planner**: you can insert this block as a central planner, which accept current doc as context and your question or goal to pick elaborators for you and generate Elaborator Blocks. Insert those blocks into the docs and you can craft your results with the agent given above.
  - **CoT** → Complex problem solving invocations in a block to have a long polled session to solve complex challenges collaboratively, leveraging the capabilities of AI to generate insights, facilitate discussions, and refine solutions in real-time.

With these features, `Elaboration Docs` provide users with an intuitive and efficient writing experience, allowing for seamless integration of AI-driven suggestions and context-based Q&A to enhance the overall quality and clarity of their documents. It is the **AI Ways** of writing docs and store the important information with AI context.

Other than the docs, we can use extra types of resources to enhance our creative processes, such as multimedia files, templates, and curated content libraries that support diverse project needs and foster collaborative innovation.

## 🗃️ Elaboration Resources

> All work with RAG!

After carefully crafting your documents, it's essential to integrate existing content into the Elaboration Resources for easy access and reference. This ensures that all relevant information is available to enhance the elaboration process. In Elaboration Studio, we plan to support the following file types in the future:

- **Docs**: Markdown text, PDFs, and Microsoft Office documents are the most commonly used formats for creating and sharing structured information. These formats allow users to collaborate easily and access diverse types of content within Elaboration Studio.

- **Media**: Images, audio, and video formats facilitate multi-modal content creation, enhancing the overall user experience by providing various ways to engage with information and ideas.

- **Database Connectors**: Connectors to remote databases and integrations with platforms like Notion and `Airtable` will be supported. Utilizing technologies similar to NL2SQL, users will be able to seamlessly query and manipulate data from various sources using natural language processing. This feature will enable a more intuitive and efficient data management experience, allowing users to retrieve relevant insights and analytics from connected databases, empowering informed decision-making based on real-time data analysis.

With Elaboration Resources, users can easily access a variety of document types and media, ensuring that all necessary information is at their fingertips for effective collaboration and idea development.

Next, we will discuss `Elaborators` as Agents, where users can create customized.

## 🪄 Elaborators

> Agent = Prompting Engineering + ReAct + CoT + Mesh + …

User can simply create an Agent (Elaborator) tailored to their specific needs, allowing for customized assistance in various tasks and enhancing the overall efficiency of their requirements.

eStudio want to design and provide Agent Creator in the following ways:

- **Workspace elements as context**: as the chapter mentioned above, the Workspace serves as a foundational element, providing users with the necessary context to effectively engage with the AI Elaborators, ensuring that the information is organized and accessible for enhanced collaboration and problem-solving.
  - Elaboration Docs: finely crafted information by user.
  - Resources: file, data, and etc.
  - …
- **Agents as Tools**: by using the `ReAct` pattern of LLM, users can leverage Elaborators as powerful tools that enhance their problem-solving capabilities by providing real-time insights and suggestions tailored to their specific needs.
- **Agent picker & Elaborators picker** (question → selected agent): by allowing users to easily choose the most suitable AI agent for their specific tasks, enhancing the efficiency and relevance of the assistance provided.
- **Agent Memory**: we can dynamically adjust the capabilities and knowledge of each Elaborator based on user interactions and specific project needs, both in private or public scenarios.

Finally, for how to interact with those crafted Elaborators, we can use:

- Chat (LUI) driven in a chat-box window
  - **directly chat** in `/chat` API, to initiate a conversation with the Elaborators, allowing users to receive tailored assistance and insights in real-time.
  - **switch elaborator** chat with same context / messages list, which means switching the elaborator chat will allow users to maintain the same context and messages list, ensuring a seamless transition between different AI agents while preserving the continuity of their discussions and ideas.
- Chat in side doc naturally interact with Elaboration doc.
- Inline Block with AI prompting

In the upcoming future, when AI LLM costly lowly and become more powerful, we can use the idea from `AutoGPT` as `agent mesh` / `agent swarm` (by OpenAI) to create a network of specialized Elaborators that can collaboratively tackle complex tasks, leveraging their unique strengths and insights to enhance problem-solving and innovation within Elaboration Studio.

## ✨ Finally

The creation of Elaboration Studio represents a significant step towards redefining how individuals  approach complex problem-solving and idea development. By integrating advanced AI capabilities with user-friendly features, the platform not only streamlines the process of organizing thoughts and resources but also fosters a culture of collaboration and innovation. Users can seamlessly transition between brainstorming, drafting, and refining their ideas, all within a single, cohesive workspace. This holistic approach not only enhances productivity but also empowers users to unlock their full creative potential, making Elaboration Studio an invaluable tool for anyone looking to navigate the intricacies of modern challenges effectively 🆒 \~

You can try this tool for free at start in `e-studio.ai` , happy hacking exploring the endless possibilities of idea elaboration and problem-solving with Elaboration Studio, where your creativity meets cutting-edge technology \~

> BTW, this article is generated via Elaboration Studio and use its AI completion feature to help me write this article. 🤖
