---
title: "🧠 ARNO's Mental Models Checklist"
publishedAt: '2025-05-09'
summary: "Arno's Mental Models for thinking and decision making."
tags: ['Thinking', 'Theory', 'en']
---

## Basics / 基础

模型是用数学公式和图表展现出来的形式化结构，它能够帮助我们理解世界。(Models are formalized structures, often represented by mathematical formulas and diagrams, that help us understand the world.) 掌握和使用模型可以提高我们理解、解释、设计、沟通、行动、预测和探索等能力。(Mastering and using models can enhance our abilities to understand, explain, design, communicate, act, predict, and explore.)

- 「**简化**」(Simplification)，剥离不必要的细节，抽象掉若干现实世界中的要素，或者从头开始创造。(Stripping away unnecessary details, abstracting certain elements of the real world, or creating from scratch.)
- 「**形式化**」(Formalization)，通常需要使用形式定义（数学公式、算法、结构表示）。(Usually requiring formal definitions (mathematical formulas, algorithms, structural representations).)
- 「**非具象化**」(Non-representational)，对于世界而言，抽象的模型都是错误的。(In relation to the world, abstract models are inherently imperfect, or "all models are wrong".)

对「多元模型」的使用，是非常重要的，毕竟一个模型视角有一定的局限性，多个模型好比「微积分」一样，逐渐运算准确。(The use of 「Multiple Models」 is very important. After all, a single model has its limitations; multiple models are like 'calculus', gradually leading to more accurate understanding.)

- 最底层的是思考方式，或者说是哲学，是思维的范式。(At the lowest level is the way of thinking, or philosophy, which is the paradigm of thought.) [哲学(Philosophy).squo](https://www.notion.so/Philosophy-squo-2c7ee9c7e02745579d7bbd88ceb9de14?pvs=21)
- 往上走是抽象的思维主义，是一种基础的意识形态，是一种思想的具体表达，比如：犬儒主义 (Cynicism)、利己主义 (Egoism)、价值主义 (Axiology)、虚无主义 (Nihilism)、人文主义 (Humanism)、共产主义 (Communism)、资本主义 (Capitalism)、唯物主义 (Materialism)、唯美主义 (Aestheticism)、现实主义 (Realism)、理想主义 (Idealism)… (Moving up are abstract schools of thought, a fundamental ideology, a concrete expression of ideas, such as: Cynicism, Egoism, Axiology, Nihilism, Humanism, Communism, Capitalism, Materialism, Aestheticism, Realism, Idealism…)
- 再往后是流派所表达的观念或者观点（Statements），最后形成「意识形态」。(Further up are the concepts or viewpoints (Statements) expressed by various schools, ultimately forming an 「Ideology」.)

## Models for Thinking / 思维模型

### 基础思维与心智模型 (Foundational Thinking & Mental Models)

- 第一性原理 (First Principle)
- 逆向思维 (Inversion)
- 奥卡姆剃刀原则 (Occam's Razor)
- 汉诺剃刀 (Hanlon's Razor)
- 概率思维 (Probabilistic Thinking)
- 能力圈 (Circle of Competence)
- 认知局限性 (Cognitive Limitations / Circle of Competence)
- 模型局限性 (Model Limitation)
- 实验性思维 (Experimental Thinking / Thought Experiments)
- 1357 原则 (1357 Principle)
- 天时、地利、人和 (Time, Place, People)
- 自私的基因 (The Selfish Gene)
- 进化与选择 (Evolution and Selection)
- 竞争思维 (Competition / Competitive Thinking)
- 军事思维 (Military Thinking)

#### 决策框架模型 (Decision-Making Frameworks)

- OODA 循环 (OODA Loop - Observe, Orient, Decide, Act)
- 决策树 (Decision Trees)
- 成本效益分析 (Cost-Benefit Analysis)
- 期望值模型 (Expected Value Model)
- 艾森豪威尔矩阵 (Eisenhower Matrix / Priority Matrix)
- 预期后悔最小化 (Regret Minimization Framework)
- 可逆性与不可逆性决策 (Reversible vs Irreversible Decisions)
- 10-10-10 法则 (10-10-10 Rule)

#### 风险管理模型 (Risk Management Models)

- 黑天鹅理论 (Black Swan Theory)
- 反脆弱性 (Antifragility)
- 风险收益模型 (Risk-Return Models)
- 肥尾分布 (Fat Tail Distribution)
- 风险平价 (Risk Parity)
- 压力测试 (Stress Testing)
- 情景分析 (Scenario Analysis)
- 蒙特卡洛模拟 (Monte Carlo Simulation)

### 数学与统计基础模型 (Mathematics & Statistics Models)

#### 贝叶斯定理 (Bayes' Theorem)

- 贝叶斯定理 (Bayes's Theorem)

#### 关键统计分布与定理 (Key Statistical Distributions & Theorems)

- 正态分布 (Normal Distribution)
- 泊松分布 (Poisson Distribution)
- 二项分布 (Binomial Distribution)
- 几何分布 (Geometric Distribution)
- 指数分布 (Exponential Distribution)
- 均匀分布 (Uniform Distribution)
- 大数定律 (Law of Large Numbers)
- 中心极限定理 (Central Limit Theorem)

#### 回归分析 (Regression Analysis)

- 回归分析 (Regression Analysis)

#### 其他数学框架 (Other Mathematical Frameworks)

- 关键思维模式 (Key Thinking Patterns): Linear vs Non-Linear, Static vs Dynamic, Deterministic vs Probabilistic, Discrete vs Continuous, etc.
- Fundamental Frameworks: Logical Models, Set Theory, Graph Theory, Game Theory ...

### 自然科学模型 (Natural Sciences Models)

#### 物理学模型 (Physics Models)

- 牛顿力学 (Newtonian Mechanics)
- 热力学 (Thermodynamics)
- 相对论 (Relativity)
- 量子力学 (Quantum Mechanics)

#### 生物学模型 (Biology Models)

- 自然进化论 (Natural Evolution / Theory of Natural Evolution)
- 分子生物学 (Molecular Biology)
- 遗传学 (Genetics)
- 生态学 (Ecology)
- 生物信息学 (Bioinformatics)

### 社会科学模型 (Social Sciences Models)

#### 经济学模型 (Economics Models)

- 供需关系 (Supply and Demand)
- 博弈论 (Game Theory)
- 比较优势 (Comparative Advantage)
- 市场失灵 (Market Failure)
- 前景理论 (Prospect Theory)
- 市场模型 (Market Model)
- 福利模型 (Welfare Model)
- 权衡模型 (Trade-off Model / TradeOff)
- 林迪效应 (Lindy Effect)
- 生产模型 (Production Model)
- 周期模型 (Cycle Model)

#### 心理学模型 (Psychology Models)

- 马斯洛需求层次理论 (Maslow's Hierarchy of Needs)
- 认知失调 (Cognitive Dissonance)
- 学习理论 (Learning Theories)
- 依恋理论 (Attachment Theory)
- 认知偏差 (Cognitive Bias)
  - 确认偏差 (Confirmation Bias)
  - 可用性偏差 (Availability Bias / Availability Heuristic)
  - 锚定偏差 (Anchoring Bias / Anchoring Heuristic)
- 福格行为模型 (Fogg Behavior Model)

#### 行为经济学模型 (Behavioral Economics Models)

- 助推理论 (Nudge Theory)
- 损失厌恶 (Loss Aversion)
- 禀赋效应 (Endowment Effect)
- 框架效应 (Framing Effect)
- 心理账户 (Mental Accounting)
- 现状偏误 (Status Quo Bias)
- 沉没成本谬误 (Sunk Cost Fallacy)
- 双曲贴现 (Hyperbolic Discounting)
- 峰终定律 (Peak-End Rule)

#### 沟通与说服模型 (Communication & Persuasion Models)

- 修辞三要素 (Rhetoric Trinity: Ethos, Pathos, Logos)
- 西尔迪尼说服六原则 (Cialdini's Six Principles of Persuasion)
  - 互惠原理 (Reciprocity)
  - 承诺一致性 (Commitment & Consistency)
  - 社会认同 (Social Proof)
  - 喜好原理 (Liking)
  - 权威原理 (Authority)
  - 稀缺原理 (Scarcity)
- 非暴力沟通 (Nonviolent Communication / NVC)
- 倾听的层次 (Levels of Listening)
- 反馈模型 (Feedback Models / SBI Model)

### 商业与战略模型 (Business & Strategy Models)

#### 战略分析模型 (Strategic Analysis Models)

- 波特五力模型 (Porter's Five Forces)
- 波特价值链模型 (Porter's Value Chain)
- SWOT 分析模型 (SWOT Analysis)
- 颠覆性创新 (Disruptive Innovation)
- 蓝海战略 (Blue Ocean Strategy)
- 穆林七领域模型 (Mullin's Seven Domains Model)
- 商业创新分析模型 (Business Innovation Analysis Model)
- 借风造势 & 顺势而为 (Leveraging Trends & Riding the Momentum / Seizing Opportunities & Going with the Flow)

#### 平台与网络效应模型 (Platform & Network Effects Models)

- 梅特卡夫定律 (Metcalfe's Law)
- 平台经济学 (Platform Economics)
- 网络效应 (Network Effects)
- 双边市场 (Two-Sided Markets)
- 赢者通吃效应 (Winner-Take-All Effect)
- 锁定效应 (Lock-in Effects)
- 飞轮效应 (Flywheel Effect)

#### 创新与精益模型 (Innovation & Lean Models)

- 待完成任务理论 (Jobs-to-be-Done Theory / JTBD)
- 设计思维 (Design Thinking)
- 精益创业 (Lean Startup Methodology)
- 最小可行产品 (Minimum Viable Product / MVP)
- 构建-测量-学习循环 (Build-Measure-Learn Loop)
- 客户开发模型 (Customer Development Model)
- 敏捷方法论 (Agile Methodology)
- 精益画布 (Lean Canvas)

#### 协商与冲突解决模型 (Negotiation & Conflict Resolution Models)

- 双赢思维 (Win-Win Thinking)
- 最佳替代方案 (BATNA - Best Alternative to a Negotiated Agreement)
- 基于利益的协商 (Interest-Based Negotiation)
- 哈佛协商项目模型 (Harvard Negotiation Project Model)
- 冲突解决阶梯 (Conflict Resolution Ladder)
- 调解与仲裁模型 (Mediation & Arbitration Models)

#### 管理理论 (Management Theories)

- 科学管理理论 (Scientific Management Theory)
- 行政管理理论 (Administrative Management Theory)
- 官僚制理论 (Bureaucracy Theory / Weber's Bureaucracy Theory)

### 其他重要模型 (Other Important Models)

#### 思维方法 (Thinking Methods)

- ZOOM 思维 (ZOOM Thinking)
- 金字塔结构思维 (Pyramid Structure Thinking / Minto Pyramid Principle)
- 帕累托原则 (Pareto Principle / 80/20 Rule)
- 多层因素分析 (PNF / Multi-level Factor Analysis)
- 复盘式思维 (Review Thinking / After Action Review)
- 批判性思维模式 (Critical Thinking)
- N 顺位思考模式 (N-th Order Thinking / Second-order thinking and beyond)
- 归纳演绎 (Induction and Deduction)
- 六顶帽子方法 (Six Thinking Hats / De Bono's Six Hats)
- 5W2H 问题引导法 (5W2H Questioning Method)
- 分层抽象模型 (Layered Abstraction Model / LayeredStructure)
- 换位思考 & 多视角分析 (Perspective Taking & Multi-perspective Analysis / ChangePosition)
- 层次推理思维 (Layered Inference / Layered Infer)
- 杠铃策略 (Barbell Strategy / Leverage and Buffer)
- 权重矩阵 (Weighted Matrix / WeightMatrix)
- 价值思维 (Value-based Thinking / Values)
- 仿生和拟态思维 (Bionic and Mimicry Thinking)
- DFSQBZS - 道法术器兵志势 (Dao, Fa, Shu, Qi, Bing, Zhi, Shi - A framework for strategic thinking, roughly: Way, Method, Technique, Tool, Force, Will, Momentum)

#### 学习与知识管理模型 (Learning & Knowledge Management Models)

- 费曼学习法 (Feynman Technique)
- 间隔重复 (Spaced Repetition)
- 主动回忆 (Active Recall)
- 知识图谱 (Knowledge Graphs)
- 学习金字塔 (Learning Pyramid)
- 布鲁姆分类法 (Bloom's Taxonomy)
- 四阶段能力模型 (Four Stages of Competence)
- 刻意练习 (Deliberate Practice)
- 学习迁移 (Transfer of Learning)
- 认知负荷理论 (Cognitive Load Theory)

#### 时间与生产力模型 (Time & Productivity Models)

- 时间盒方法 (Time-boxing)
- GTD 工作法 (Getting Things Done)
- 番茄工作法 (Pomodoro Technique)
- 深度工作 (Deep Work)
- 心流状态 (Flow State)
- 注意力残留 (Attention Residue)
- 帕金森定律 (Parkinson's Law)
- 二八法则在时间管理中的应用 (80/20 Rule in Time Management)

#### 系统与网络模型 (System & Network Models)

- 抓手和杠杆 (Handles and Levers)
- 系统分析模型 (System Analysis Model)
- 循环图与反馈环模型 (Feedback Loop Model / Causal Loop Diagrams)
- 网络模型 (Network Models)
- 混沌分析 (Chaos Analysis / Chaos Theory)
- 系统特征分析 (System Properties Analysis)
- 不动点 (Fixed Point)
- 冰山模型 (Iceberg Model)
- 扩散（传播）模型 (Diffusion Model / Spread Model)
- 量变与质变 (Quantitative Change and Qualitative Change / From Quantity to Quality)
- 极性分析 (Polarity Analysis / Polarity Management)
- STC Operator (Situation, Target, Current Reality Operator / STC Operator)

#### 设计与产品开发模型 (Design & Product Development Models)

- 一般系统和产品设计过程模型 (General System and Product Design Process Model / PDP - Product Development Process)
- 问题分析与设计决策树 (Problem Analysis and Design Decision Tree / Decision Tree)
- 升维视角 (Higher Dimensional Perspective / GodView / Systems Thinking from a Higher Level)
- Elon Musk 的做事五步思考法则（综合思考模型） (Elon Musk's Five-Step Thinking Process for Doing Things (Comprehensive Thinking Model))
- 突破束缚 (Breaking Constraints / Thinking Outside the Box)
- 备份与冗余 (Backup and Redundancy)

#### 复杂性科学模型 (Complexity Science Models)

- 复杂适应系统 (Complex Adaptive Systems / CAS)
- 涌现现象 (Emergence)
- 相变理论 (Phase Transitions)
- 临界点 (Tipping Points)
- 小世界网络 (Small World Networks)
- 无标度网络 (Scale-Free Networks)
- 自组织临界性 (Self-Organized Criticality)
- 蝴蝶效应 (Butterfly Effect)
- 吸引子理论 (Attractor Theory)

#### 信息理论模型 (Information Theory Models)

- 信号与噪声 (Signal vs Noise)
- 信息熵 (Information Entropy)
- 信息压缩 (Information Compression)
- 香农信息理论 (Shannon Information Theory)
- 信息不对称 (Information Asymmetry)
- 过滤泡沫 (Filter Bubble)
- 信息茧房 (Information Cocoon)
- 认知过载 (Information Overload)

#### 软件工程模型 (Software Engineering Models)

- 软件工程领域的应用研发执行思维模型 (Application R&D Execution Thinking Model in Software Engineering / SE - Software Engineering)

### 哲学与伦理模型 (Philosophy & Ethics Models)

#### 古典哲学学派 (Classical Philosophy Schools)

- 斯多葛主义 (Stoicism)
- 伊壁鸠鲁主义 (Epicureanism)
- 犬儒主义 (Cynicism)
- 怀疑主义 (Skepticism)
- 柏拉图主义 (Platonism)
- 亚里士多德主义 (Aristotelianism)

#### 伦理学框架 (Ethics Frameworks)

- 功利主义 (Utilitarianism)
- 义务伦理学 (Deontological Ethics / Kantian Ethics)
- 美德伦理学 (Virtue Ethics)
- 关怀伦理学 (Care Ethics)
- 情境伦理学 (Situational Ethics)
- 结果主义 (Consequentialism)

#### 东方哲学智慧 (Eastern Philosophy Wisdom)

- 中庸之道 (Doctrine of the Mean)
- 阴阳理论 (Yin-Yang Theory)
- 五行理论 (Five Elements Theory)
- 无为而治 (Wu Wei - Non-action)
- 正念 (Mindfulness)
- 因果法则 (Law of Karma)
- 中观哲学 (Madhyamaka Philosophy)

### 跨学科整合模型 (Interdisciplinary Integration Models)

#### 元认知模型 (Metacognitive Models)

- 元认知策略 (Metacognitive Strategies)
- 反思性实践 (Reflective Practice)
- 学习如何学习 (Learning How to Learn)
- 认知偏差校正 (Cognitive Bias Correction)
- 思维关于思维 (Thinking About Thinking)

#### 整合性框架 (Integrative Frameworks)

- 跨学科思维 (Transdisciplinary Thinking)
- 系统性思维 (Systemic Thinking)
- 全局优化 (Global Optimization)
- 多尺度分析 (Multi-scale Analysis)
- 综合评估模型 (Comprehensive Assessment Models)
