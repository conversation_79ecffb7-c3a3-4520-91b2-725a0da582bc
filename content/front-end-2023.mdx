---
title: '2023 Thoughts on Current Web Application Development Trends'
publishedAt: '2023-01-01'
summary: "<PERSON><PERSON>'s thoughts on technology trends such as low-code, AIGC, full-stack development, VR/AR, and the metaverse."
tags: ['Frontend', 'en']
---

> It's that time of year for planning again. Let's take this opportunity to look at the present and think about the future.


Only by grasping the general trend can we master the future. Here are some of my thoughts on Web application development trends when I was doing technology planning. I'm throwing out these ideas to spark discussion and invite everyone to explore them together.

## Face Low-Code, Use Low-Code Well

> Do code-less, but in a right way ~

> Low-code is not a silver bullet, but low-code products that are deeply cultivated in specific fields can often generate considerable power.


Low-code has indeed become a mainstream trend. However, I believe that **good low-code products are those that focus on specific domains and are deeply cultivated**. It is unrealistic to expect low-code to subvert tradition or to want low-code products to be omnipotent. In my opinion, typical scenarios can be divided into the following three categories：

- Low-code products represented by Microsoft PowerPlatform set the user threshold to the difficulty of Excel. **This allows ordinary users without professional programming skills to use these platforms to solve long-tail needs in various fields**, such as dragging and dropping to generate data models, configuring CRUD forms, and creating conventional workflows. In China, DingTalk's Yida is an example of this trend.
- Low-code products from established CRM vendors like Salesforce and domestic players like Yonyou **focus on "secondary development & customization." They build upon existing high-rise buildings (CRM products, ERP products, etc.)** for custom development. These products often rely on the accumulation of original technical assets and then allow the ecosystem to play with the entire customization system, reducing the marginal cost of customization.
- From the perspective of application production division of labor, these are **tool-based products that enable various roles such as product managers, designers, server-side developers, front-end developers, client-side developers, and testers to collaboratively produce pages, processes, and logic on a low-code pipeline**. This area is highly competitive in China but also faces many challenges. Such products are often prone to failure. Coupled with the closed nature, black-box characteristics, and uncontrollability of the technology, choices need to be made carefully, otherwise, they become pitfalls 👿.

Gartner 2022 Low-Code Magic Quadrant

Low-code can indeed lower the threshold for application development, allowing more long-tail needs ([Some thoughts on reading "The Long Tail"](https://www.yuque.com/surfacew/fe/agn5ke)) to be met. Secondly, low-code applications have an advantage in rapid prototyping and validating product ideas. Here, I would like to make some predictions for the future：

- "**Boundary Capabilities and Boundary Issues**": **There needs to be a better boundary between professional application programming and low-code programming**. Good products are those that focus on specific domains and can provide solutions that offer explosive efficiency improvements compared to traditional coding. For example, [Yida](https://www.aliwork.com/) enables even factory workers to build applications to solve problems they discover, which is a breakthrough. **For small teams, the smaller and more specialized the problem that low-code is expected to solve, the more universal the solution, and the more it can be integrated and reused to amplify value**. This would be a good entry point. Don't be impatient when doing low-code; even with a thousand-person investment, grand ambitions may not necessarily succeed in a real sense.
- "**Evolution and Sustainability Issues**": When the application scale becomes complex to a certain extent, low-code will encounter capability bottlenecks because low-code is the result of abstracting and encapsulating code and functions, and its expressiveness will always be weaker than pure code programming. Many products try to achieve both low-code and completeness. Once the business develops and the application needs to solve complex problems, low-code needs to use very "clumsy" methods (such as opening CodeEditing) to achieve completeness. The cost of this completeness is very high, often turning the product into a mishmash. Non-professional users find it complex and difficult to use, while professional users find it a dispensable gimmick, **because the product can hardly cater to the "habits" of both user groups**. Therefore, I personally believe it is valuable to start quickly with low-code products for rapid prototyping, and when the complexity increases and it becomes unmanageable, to be able to degrade to professional coding, **allowing the low-code application to "graduate smoothly."** Currently, most low-code platforms do not have this capability. They can only "weld" users to the platform, forcing them to either swallow their needs, make users submit requests that are then met in a clumsy way for complex customizations, or customers have to start from scratch, significantly increasing costs. I would like to discuss this further in the context of evolutionary or adaptive application architecture.
- "**Openness Issues**": For applications produced by low-code, **the underlying data assets and technical assets need to be well integrated and interoperable with the standard technology ecosystem**. Don't let the low-code application system become an "island," lest it become an orphan later, with no one to care for it. Good application openness standards can organically combine core systems, mission-critical business systems, and long-tail applications to achieve digitalization.

Some recommended further reading：

- [Understanding Low-Code from its Implementation Principles](https://zhuanlan.zhihu.com/p/451340998)：This article is highly recommended. It discusses the pros and cons of low-code system coding and is, in my opinion, a relatively objective and pertinent article.

## Optimistic about the Potential Outbreak Behind AI

> ChatGPT has been extremely popular recently, and Copilot is also impressive.


AIGC is truly a hot topic among hot topics recently. The use of AI in future Web application development includes, but is not limited to, the following scenarios:

- **Programming Consultant**: A relatively fast search engine. For some factual questions, such as: `Is the unit of max-age in the HTTP CacheControl cache header s or ms?` **ChatGPT** is indeed more efficient than Google. It can also carry context for continuous questioning and quickly solve programming problems. Of course, for some novice and unfamiliar domain questions, **ChatGPT** can also act as a consultant, providing some coding suggestions or code explanations. Once this is commercialized, programmers of different levels can benefit. In my opinion, how to reduce the cost of each query to a level acceptable to the general public is where the ML engineering system needs to strive to break through next.
- **Robot Q&A**: Through transfer learning of large language models such as GPT-3, adding its own "**material**" for training, it is a good practical idea to use it for answering common questions in the programming field and to de-service.
- **Natural Language => Code (or DSL)**: **Copilot** / **ChatGPT**, etc., based on `OpenAPI`, can already assist in generating some code from natural language. Although it is fragmentary, it can be used as a programming reference in actual R&D, or as a reference for beginners to write code. Of course, using natural language to generate specific DSLs (such as the mode of generating workflows on MSPowerPlatform) is also a new way.
- **Machine Code Review**: Many products for ML-based code review have already been launched. Currently, it seems that some basic problems can be found, but large-scale code input and engineering-level context connection are current technical bottlenecks that need to be improved.
- **Code => Code**: Coding assistance represented by `Copilot` is indeed becoming stronger and stronger. The recommended code often combines a large amount of engineering and project context for calculation. Although the price of 600 yuan per year is a bit expensive, when you think that you still make money by selling code, and it can improve productivity, you will feel that it is quite cheap, right?
- **Image => Code**: This type was also played quite well in the previous few years, such as Taobao's `ImageCook`, which can realize simple PSD / Sketch => Code, or conventional design images of middle and back offices => React component Code, or some product images or simple visual effect cards => card DSL. There are many such attempts, and there is also a certain room for implementation to improve the efficiency of simple UI generation, especially in weakly interactive domains.
- **Digital Content Generation**: Recently, image generation represented by `StableDiffusion`, speech synthesis, image optimization, etc., have also flourished. It is quite interesting to generate specific content through natural language, and it can even be used to generate logos for technical projects, which is quite interesting ~

From the perspective of research reports, Generative AI indeed has a lot to offer. **Using AI as a weapon for daily production and accelerating the R&D speed of Web applications indeed has a lot of room**.

Image cited from: [Base10 Blog: If You’re Not First, You’re Last: How AI Becomes Mission Critical](https://base10.vc/post/generative-ai-mission-critical/), delete if infringing

Due to the extremely high threshold for building AI infrastructure, but the application threshold is indeed decreasing, continue to pay attention and look forward to future development.

## **Full-Stack Thinking, Integrated Design, Rapid Innovation**

As a front-end developer, I have recently seen many people badmouthing the front-end profession, and some articles claim that "front-end is dead." However, I personally always believe that front-end is just a specific division of labor in the technical field due to specific times and specific needs. **As the saying goes, what is long divided must unite, and what is long united must divide**. When Web technology gradually matures, especially when terminal technology, cross-end technology, front-end technology, etc. begin to mature, **the boundary between end (Native) to end (Web) to end (Server) becomes increasingly blurred. Coupled with the support of cloud native and many virtualization technologies, which shield our perception of the complexity of application deployment and operation, middleware calls, microservice architecture implementation, etc., many full-stack developments have an out-of-the-box Tech-less (code focuses on business implementation, shielding underlying complex implementation) experience**.

> Purely defining one's work as coding for the UI presentation layer is naturally not long-lasting and will weaken and be eliminated with technological development.

If an engineer can define, discover, and solve problems from a full-stack perspective, and use mature infrastructure to solve business or technical problems in a Tech-less way, they often have greater productivity improvements. This full-stack R&D process often has the following advantages:

- The entire software design often has a high degree of **conceptual consistency**, cleverly decomposing business problems into a unified technology stack for maintenance, reducing communication and collaboration costs and conceptual understanding costs for multiple roles, and effectively reducing the cognitive load of maintainers.
- The overall software implementation often uses a unified programming language and framework, while existing technology or platform capabilities can better shield the complexity of IaaS / PaaS. Developers only need to **make technical selections according to specific scenarios, efficiently solve current problems, and have relatively high overall flexibility**. Consistency in the technology stack can be fully guaranteed.
- In an era where microservices and DDD-driven server-side architectures are mainstream, **the basic capabilities of BaaS are already quite mature**. Cross-platform and cross-technology stack calls are also shielded from the complexity of service call compatibility through RPC protocols such as REST (HTTP based), gRPC, and HSF. This allows services to specialize in specific business areas. For example, Node is very suitable for the application design and implementation of the view layer and interactive presentation layer, but not suitable for the functional implementation of heavy computing (CPU-intensive), enterprise-level services, etc. The trade-off of this architecture requires key design by the enterprise CTO / architect.
- Strong individual combat capability. Full-stack engineers need to be **Swiss army knives, often able to create R&D applications in a short period of time and produce prototype systems to be verified by the market and users**. Subsequently, service refactoring and decomposition can be continuously carried out into the aforementioned more complex microservice constructions, creating an "**evolutionary**" architectural design and becoming an expert system.
- Thinking about problems from a full-link, or rather, full-end perspective, **increases the depth and breadth of a person solving a technical problem, and the dimensions of the solution will also increase**. For example, in performance optimization, it will no longer be simply limited to the client or server, which can inspire better solution design and break the shackles of existing models.

There are paths to achieving full-stack, but "**lowering the threshold for full-stack**" has always been an important breakthrough for engineers to improve their full-stack capabilities. Recently, the software industry has made many attempts, as early as the idea of Java unifying terminals (Applet), and then the popularity of template rendering and front-end and back-end separation models in server-side frameworks, so that the UI view architecture of SPA (SinglePageApplication) has become mainstream.

Now that `Node.js` is popular, `Next.js` [Next.js Research and Learning](https://www.yuque.com/surfacew/daily-learn/zpubn3) has emerged, combining the idea of React Client / Server rendering isomorphism, trying to further lower the threshold for front-end and back-end views, API services, etc. Here I briefly draw a map of my understanding of full-stack technology:

Full-stack technology ecosystem from the perspective of JavaScript programming language

It is worth mentioning that:

- The recent popularity of high-performance languages such as **Rust** has laid a lot of foundation for the high performance of full-stack infrastructure. Currently, many high-performance R&D tools prefer Rust for R&D. Rust is gradually becoming popular and becoming a trend.
- The emergence of **Wsam** (WebAssembly) technology has recently matured. Figma's use of Wsam to implement its design canvas is amazing for the drawing performance inside the browser.
- The almost fanatical construction and popularization of the **JavaScript** ecosystem (as the basic language for Web application construction) has made the containerization of **JavaScript** spread across multiple end domains, and has also laid an important foundation for upper-layer application R&D frameworks to "set foot" across ends.
- **Next.js** can be regarded as the leader in B/S cross-platform. **One Project to rule them all**. Recently, it launched a ServerComponent First application architecture, which has become a UI rendering paradigm that can be selected in the future.

Overall, it can be seen that solving problems with full-stack thinking in terms of technology and business is the general trend.

Some recommended further reading:

- [My View on Next.js: A More Modern Poseidon](https://mp.weixin.qq.com/s?__biz=MzAxMTU0NTc4Nw==&mid=2661157942&idx=1&sn=09266ac8328b2c0373a7631132353bbd&scene=21#wechat_redirect): Uncle Wolf's understanding of Next.js as a "modern Poseidon" is quite interesting.
- [In-depth Analysis of Next.js Implementation](https://www.yuque.com/surfacew/daily-learn/fdupg3b6d59ueuig): My recent analysis of the new features of Next.13 app, currently based on the code reading of the canary version, welcome to discuss together.

## Idealist: Adaptive, Evolvable Application Architecture

> This part is relatively abstract, leaning towards an exploration of an ideal Web architecture.


As time moves forward, business will change, technology will change, and people will change. How can application systems withstand the test of time, reduce long-term maintenance costs, and achieve flexible adaptation? Imagine an application that can not only adapt to the flexible changes of business, but also adapt to the continuous evolution of technology, and can also be good for new and old maintainers. **This seems to be the ultimate exploration of software engineering in the face of complexity and uncertainty**. Is it possible to have a Web application architecture that can approach this high degree of adaptability and can evolve to adapt to changes?

In the book ["Evolutionary Architecture"](https://www.yuque.com/surfacew/daily-learn/bbii5l), the author tries to provide a software architecture and R&D model to adapt to these "**changes**", and its ideas include:

- **Introduce architectural fitness (degree) functions**, and evaluate the fitness of the system architecture based on formal modeling and data-driven forms.
- **Implement highly controllable incremental changes**, including automated testing, deployment pipelines, and changes in fitness functions, to evaluate the impact of new changes on the existing system and ensure the stability of the existing system.
- **Gradually decouple the existing application architecture and make "gradual" evolution towards the current advanced software architecture**, such as from monolithic architecture => service-oriented architecture => extracting some components to be implemented with Serverless architecture, and continuously evolving on the basis of the first two points.
- ... 

In my personal opinion:

- **Focus on core implementation**, focus on unchanging business or technical characteristics to do a good job in CORE layer capability building (such as: refinement of domain models, abstraction of core business processes, etc.), and grasp the unchanging in the midst of myriad changes to build a high-quality software core.
- **Focus on the construction of platform engineering**. This is a point mentioned in Gartner's 2023 technology trends. For large factories, whether it is a middle platform or a technology platform, it is a silhouette of excellent engineering. Platform engineering is closely related to the "life" of an application. When building platform capabilities, considering the requirements of application adaptation and evolvability as an indicator for design can also

This is indeed a big proposition, which can rise to the overall level of software engineering. However, as engineers, why don't we always think about and explore the structural design of current applications in the process of Web application R&D, concerning its adaptability and "evolvable" characteristics? I won't go into further detail here, otherwise, it would cover all aspects of a book called "Contemporary Software Engineering 2023".

## Connecting to Future-Oriented Technical Fields with Web Technology

Web technology is the main form carrier of current applications. Facing the future, we also need to reserve some capabilities to connect to new technical fields, such as:

In 2022, the trend of decentralization is coming, and concepts such as blockchain technology & NTF are on fire 🔥. After this wave is over, let's take a look at what technology is left after the bubble bursts? Using decentralized technologies - blockchain technology, decentralized identity, decentralized storage technology - to give centralized applications decentralized capabilities, what can these capabilities bring to existing businesses? Or what kind of changes will they produce? Or using Web technology to build completely decentralized applications are all propositions worth exploring and experimenting with.

Furthermore, the living space of Web technology in the context of the metaverse. I don't know if Apple will release new XR-related devices this year. If it can trigger the next wave of wearable devices, the expressiveness of Web AR / VR that may intersect with Web technology behind it, or the birth of Web-specific engines in metaverse applications to consume and create Web technology-driven content will become technological hotspots worth exploring. Of course, the field of graphics technology itself is a field with a very high threshold. Entering it also requires a higher subject foundation, and even more cross-border patience and courage to explore.

## Finally

With the trend, and then looking at the present in combination with the business at hand, we can naturally discover many propositions to do, just as Alan Turing said: **As far as the eye can see, it is only the near future, yet even so, we can still see that there is much work worth doing waiting for us there**.

> As far as the eye can see, it is only the near future, yet even so, we can still see that there is much work worth doing waiting for us there. —— Alan Turing, Computing Machinery and Intelligence, 1950


Let's encourage each other ~
