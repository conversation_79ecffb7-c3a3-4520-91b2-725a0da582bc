# Research Outline

## Products

- [DeepSeek-R1-new] seems to be strong enough to match with Claude 4 level?

## AI

* [3] MCP research, and pick the best 50 MCP typical tools ranking from 1 to 50
* [3] Research the most valuable AI products in general (12) from level of large, medium and small companies in capital market
* [3] Research the valuable AI products in typical vertical industries

## Web Dev Tech

> Use DeepWiki to learn tech in booster way ~

* [3] next.js with DeepWiki
* [3] react with DeepWiki
* [3] sqlite with DeepWiki

Architecture Topics

WIP

## Investment


## Life learning


## Dive Deeper

* [Dao](./wisdom/DAO.md)

## Mental Models

* [mental-models] scan this answer and summarize -> [思维模型有哪些？ - 亿图图示的回答](https://www.zhihu.com/question/420038169/answer/3051734989) -> I can offer my own answers to it 👈🏻 WIP :cool

## Trace

* ✅ cat raise and how to live with pregnancy wife - 5.03
* ✅ AI Native editing technology research
* ✅ MCP and its related technologies: dive deeper to find opportunities and chances we have to cooperate with MCP both in Yida and in other products of ours
* ✅ Google Deep Research & Production Use and comments
* ✅ learn Python in 7 days research - 4.28
* ✅ ShangHai PuDong Core Area House Price Research
* ✅ China v.s. US Economy
* ✅ Alibaba v.s. PDD
* ✅ Trump's Era Stock Market Research in USA
* ✅ USA's bond market research and understanding the history and future - 4.26
* ✅ Deep understanding of the relationship between bond market and stock market - 4.26
* ✅ NVIDA company & stock price research - 4.26
* ✅ Most investable AI technology companies in 10 large and 10 start-up list and explain the core reasons - 4.27
* ✅ In CHINA how to play it safe to have a 5% interest rate of 3M RMB - 5.01
* [3a] `Flowise` and its related technologies to learn - 5.28
* [x] Personal Brand, SEO, and E-E-A-T, how to build a self-growth branding and impactions
* [x] deep compare `cursor` | `windsurf` | `augment` and `Github Copilot` to find the best one for our use case - 5.28
- [x] windsurf research and its related technologies to learn - 5.28 (it sucks ...)
