# MacOSX.desc

`DotFiles` reference see Github anyway

MacOSX 的启动：[组合键](https://support.apple.com/zh-cn/HT201255)

[日历(Calendar).app](MacOSX%20desc%2041bbe02543d44890badc8c82d82423c4/%E6%97%A5%E5%8E%86(Calendar)%20app%203144d5ced5354f76aa8f281a136dc9e3.md)

# Mac

Hide files / directories

```bash
chflags hidden /path/to/folder/
chflags nohidden /path/to/folder/
```

or

```bash
defaults write com.apple.finder AppleShowAllFiles YES
defaults write com.apple.finder AppleShowAllFiles NO
```

---

Mac Screen Capture shortcut:

```
cmd + shift + 3 / 4 + space
```

---

Make all apps installable:

```bash
sudo spctl --master-disable
#or
sudo xattr -r -d com.apple.quarantine /Applications/StarUML.app
```

# Often used commands

```bash
# remove all node_modules
find . -name 'node_modules' -type d -prune -exec rm -rf '{}' +

# remove all node_modules in trash
npm install --global trash-cli
find . -name node_modules -type d -prune -exec trash {} +

# remove all .stylus file
find . -name '*.styl' -type f -prune -exec rm -f '{}' +
```

安装 Roseta

```bash
softwareupdate --install-rosetta
# or
/usr/sbin/softwareupdate --install-rosetta --agree-to-license
```

安装 `xrun / xCode related`

```bash
xcode-select --install
```

## AppleScript

> 做 MacOS 上的自动化。可以将其通过 Alfred 封装为一个按钮或者一个命令就搞定很多 Mac 上自动化的诉求。
> 

## Syntax

```
(* This is a basic demo of AppleScript*)
tell application "Safari"
	(* 嵌套调用类似于 Safari.frontDocument *)
	tell front document
		do JavaScript "window.alert('Hello, World')"
	end tell
end tell
```

- [AppleScript 入门](https://sspai.com/post/46912) | [AppleScript 入门简书](https://www.jianshu.com/p/a78b31badb5a)
- [AppleScript full-guide of Apple Developer Doc](https://developer.apple.com/library/archive/documentation/AppleScript/Conceptual/AppleScriptLangGuide/introduction/ASLR_intro.html#//apple_ref/doc/uid/**********-CH208-SW1)

## System Fonts

编程等宽字符集：

```
- Fira Code
- Menlo
- Monaco
- Source Code Pro
- Operator Mono
- Consolas
```

Secure Input

[macOS: find app using Secure Input](https://rakhesh.com/mac/macos-find-app-using-secure-input/)

# 装机软件必备

最高优先级系列：

[玩转苹果](https://www.ifunmac.com/?s=istat+menus&x=0&y=0)

必修：

- [Alfred.entity](../Alfred%20entity%209c5f414fab2747d98e4b13d7413dcd5a.md)
- [Notion.entity](../Notion%20entity%20e793fb0c3a9a48bfa691d718504f2ca2.md)
- [翻墙系列(VPN ShadowSockets / VMess).desc](%E7%BF%BB%E5%A2%99%E7%B3%BB%E5%88%97(VPN%20ShadowSockets%20VMess)%20desc%20f6a5da2af3304e0b8426e006785d9830.md) ⇒ V2rayU
- 搜狗拼音（输入法必备系列）

日常：

- Chrome [Chrome 浏览器(Chromium & Webkit).software](../%E7%A7%91%E6%8A%80%E9%9B%B7%E8%BE%BE(Science&TechnologyRadar)%20sys%20c09812c3edfa4da2864d391d86762607/%E7%A7%91%E6%8A%80%E9%9B%B7%E8%BE%BE(Science%20&%20Technology%20Radar)%20db%2087da405c2a1741f2852cc82a25818e16/Chrome%20%E6%B5%8F%E8%A7%88%E5%99%A8(Chromium%20&%20Webkit)%20software%2045a04447243c4ea394d12bd134fe42a6.md)
- Arc 浏览器（更好用，更自如的浏览器） → @Arc
- IM 全家桶（微信、DingTalk）
- Infuse（AppStore Purchased）

其它：

- 清理系列
    - DaisyDisk：Mac 磁盘空间管理工具（AppStore Purchased）
        - 快速索引和展示磁盘的使用情况，用来做空间清理非常高效
    - Mac Disk cleanup tools：`PrettyClean`
        
        [https://pretty-clean.github.io/](https://pretty-clean.github.io/)
        
- 日常监控
    - `iStat` Menus
- **Contexts** - 替换 Mac 自带的应用切换
    
    [Contexts - Radically simpler & faster window switcher for Mac](https://contexts.co/)
    
    [Contexts-License-CON180506-1590-83746.contexts-license](MacOSX%20desc%2041bbe02543d44890badc8c82d82423c4/Contexts-License-CON180506-1590-83746.contexts-license)
    
    ```jsx
    https://mail.163.com/js6/main.jsp?sid=NAxhOHXhXCUYDrPdaEhhMhUwHMhCDIhm&df=mail163_letter#module=read.ReadModule%7C%7B%22area%22%3A%22normal%22%2C%22isThread%22%3Afalse%2C%22viewType%22%3A%22%22%2C%22id%22%3A%22409%3AxtbBmQXcVlr77G-7IwABst_d1a%3A4213%3Ah%3Aq%3A8%22%2C%22fid%22%3A1%7D
    ```
    
- Flow：番茄时钟 🍅（AppStore）
- Rectangle：窗口 Size 管理工具，支持拖拽 size 变化

- iHosts 修改 Host 工具
    
    参考相关的 IP Tables。
    
    ```json
    # Github
    ************ github.com
    ************** gist.github.com
    *************** assets-cdn.github.com
    ************** raw.githubusercontent.com
    **************  gist.githubusercontent.com
    ************** cloud.githubusercontent.com
    ************** camo.githubusercontent.com
    # copilot config
    ************ github.com
    ************ api.github.com
    ```
    

### 研发工具系列参考

- [有趣的研发工具、SDK、Lib、Framework(CoolTools).index](../../../../KESW%203daa3cfbc556440bb0c4b84bef104fd6/%E7%9F%A5%E8%AF%86%E4%BD%93%E7%B3%BB(KnowledgeSystem)%20index%200022a2d318db42d49975d6997fb3ff59/%E6%9C%89%E8%B6%A3%E7%9A%84%E7%A0%94%E5%8F%91%E5%B7%A5%E5%85%B7%E3%80%81SDK%E3%80%81Lib%E3%80%81Framework(CoolTools)%20index%20801315536c644301a6825f8efdb56f69.md)
- `Warp`: YYDS 的终端工具已经完美平替（iTerms）了
- Canary
- SourceTree
- `home-brew` ⇒ 需要科学上网
    
    ```bash
    /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
    # Mac 国内镜像加速
    /bin/zsh -c "$(curl -fsSL https://gitee.com/cunkai/HomebrewCN/raw/master/Homebrew.sh)"
    # https://zhuanlan.zhihu.com/p/111014448
    ```
    
- Oh My Zsh ⇒ https://github.com/ohmyzsh/ohmyzsh
    - replace `.zshrc`
    - 依次安装插件丰富能力即可
- 安装 CI 辅助工具：`commitizen`
    - `npm i -g commitizen`
- 安装命令行帮助工具 `tldr`

# Apple 服务状态查询

[Apple - Support - System Status](https://www.apple.com.cn/cn/support/systemstatus/)

# Beta 版本使用

[Betahub.cn](https://www.betahub.cn/)

# Arno’s Software Configs

### MySQL

```tsx
We've installed your MySQL database without a root password. To secure it run:
    mysql_secure_installation

MySQL is configured to only allow connections from localhost by default

To connect run:
    mysql -u root

mysql@8.0 is keg-only, which means it was not symlinked into /opt/homebrew,
because this is an alternate version of another formula.

If you need to have mysql@8.0 first in your PATH, run:
  echo 'export PATH="/opt/homebrew/opt/mysql@8.0/bin:$PATH"' >> ~/.zshrc

For compilers to find mysql@8.0 you may need to set:
  export LDFLAGS="-L/opt/homebrew/opt/mysql@8.0/lib"
  export CPPFLAGS="-I/opt/homebrew/opt/mysql@8.0/include"

To start mysql@8.0 now and restart at login:
  brew services start mysql@8.0
Or, if you don't want/need a background service you can just run:
  /opt/homebrew/opt/mysql@8.0/bin/mysqld_safe --datadir\=/opt/homebrew/var/mysql
  
  
#

 - brew services stop mysql
 - brew install mysql@8.4
 - brew services start mysql@8.4
 - brew services stop mysql@8.4
 - brew services start mysql
```

## Node.20

```tsx
==> node@20
node@20 is keg-only, which means it was not symlinked into /opt/homebrew,
because this is an alternate version of another formula.

If you need to have node@20 first in your PATH, run:
  echo 'export PATH="/opt/homebrew/opt/node@20/bin:$PATH"' >> ~/.zshrc

For compilers to find node@20 you may need to set:
  export LDFLAGS="-L/opt/homebrew/opt/node@20/lib"
  export CPPFLAGS="-I/opt/homebrew/opt/node@20/include"
==> mysql
```

# Ref

[https://www.swyx.io/new-mac-setup-2021/](https://www.swyx.io/new-mac-setup-2021/) Mac for the new