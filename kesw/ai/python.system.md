---
type: agent
uuid: edb3e5eb-0c51-46a7-abff-801e29774879
name: <PERSON><PERSON>'s Python Programming Language Learning
desc: Guide Arno to learn python well in a good way
---

# <PERSON><PERSON>'s Python Programming Language Learning

## Goal

> Python is the programming language to AI, to Data.

SHORT term

- pass the python exam in 7 days - 25.04
- read essential python from open-source project - 25.05

MID term

- try dedicated AI intense work done with Python to enhance real-python experience

LONG term

none.

## States

| State                     | Value                       | Description |
| ------------------------- | --------------------------- | ----------- |
| <PERSON><PERSON>'s python familiarity | Rare - 看得懂基础语法的程度 |             |

## Abilities

- read and write python code in a fluent way, especially to understand those important open-source project in glance
- powered by AI to generate python code to start-up projects

## Architecture

- outline for Python programming language
- famous open-source python libs / framework / tools / platform apps
- arno’s projects on Python

---

- 语法速通：jupter notebook + 经典入门书籍快速过一遍语法，play with python，重点关注不熟悉的部分
- 向来源和明星项目学习：兴趣驱动，选择 2 ～ 3 和 python 经典工程来问题驱动式学习，比如：qwen3，微调工程，rag 工程学系，见招拆招，成长迅速。Combo with cursor 等 qa 工具，快速进步
- 实际动手解决问题：Cursor powered 部署 python 服务用于文件分析和输出 AnyFile to markdown 到 vercel 上

## Genes

Learning Strategy:

- learn and know Python’s most famous libs
- learn and know Python’s most AI powerful basic libs
- learn and know Python’s most famous AI open-source projects

---

Make full use of _[DeepWiki](https://deepwiki.com)_ to do code-research when you are interested in a specific open-source project.

In this python's case: <https://deepwiki.com/microsoft/markitdown/1.1-architecture>

### Related Resources

- [🦄 Meta Thoughts on Knowledge Research](https://e-studio.ai/e/fcf0bc11-4fb3-43fc-91c3-f0e5564b718f)

## Elaborations

Research plan by Arno - 2025-04-28

- learn and solid your foundation in basic programming language rules in one day - grammar, syntax, programming language features in Geeks for Geeks and take simple quiz list - 05.04
- make full use of Jupyter Notebook in VSCode to learn python in details x Gemini llm to do quick QA and list of examples to learn quickly
- learn in practice for open-source projects (three AI projects with AIWiki to rule them all)
  - L0 ✅ `markitdown`: simple python powered lib
  - L1 `dify`: real web-driven AI project
  - L2 `open-manus`: real AI project for complex agentic system
  - L3find one more real-world AI model related pre-train or fine-tune project
- python QA or exam questions

### trace

- write to learn -> <https://github.com/SurfaceW/arno-learn-python/tree/main>

### story

- [✅][python-exam] 🧑🏻‍💻 cope with DingTalk Python exam in actions [python-learn.sys](../kesw/ai/python.system.md)
- 61 / 100 gotcha, no.1 in team, the only person who passed the exam, for 100 questions in 30 minutes, incredible ~ 😎

## References

### Research

- DeepResearch BP to learn python - 2025-04-28 → <https://docs.google.com/document/d/1fjw-l3FlOGzPj-r3ANHYhuYbkp-r6BTbL4vuIGAVx_4/edit?tab=t.0>
- Python ML x LLM Roadmap - 2025.05 -&gt; <https://gemini.google.com/u/1/app/b00c61300649c796>
- Python LLM Learning Projects - 2025.05 -&gt; <https://gemini.google.com/u/1/app/a906eeca0c6f369d>

### Books

> Top 10 books of python guide?

### Tools

- Python `Jupyter` Notebook in VSCode with basic abc and modules and ML examples to learn

### Papers

None for this sit
