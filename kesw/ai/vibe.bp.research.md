# Vibe Coding BP for full-stack developer

## Deep Research View

do deep-research first:

'''
I want to find the BP for VibeCoding in Cursor or Github Copilot, what is the best practice for full-stack developer?
for example, how to make full use of Cursor or Github Copilot to do the full-stack development?
for example, how to write the BP guide for necessary prompt for Cursor or Github Copilot?
for example, any BP for tech-stack or directory structure for or etc.?
do the research and give me the best practice for full-stack developer.
'''

---

Recap of Key Best Practices

To fully harness the capabilities of these AI tools while mitigating potential risks, full-stack developers should prioritize:

- Mastering Strategic Prompt Engineering: Crafting clear, contextual, and iterative prompts is fundamental. This includes breaking down complex tasks and providing examples.
- Leveraging Tool-Specific Context Mechanisms: Utilize features like Cursor's .cursorrules and GitHub Copilot's custom instructions to provide persistent, project-wide context, ensuring AI outputs align with specific project standards and architectures.
- Maintaining Human Oversight and Critical Evaluation: AI-generated code must always be reviewed for quality, security, and correctness. Developers retain ultimate responsibility for the codebase.
- Proactive Quality Management: Actively refactor AI-generated code, combat duplication, and ensure adherence to security best practices. Do not let the speed of AI generation compromise long-term maintainability.
- Continuous Learning and Skill Adaptation: Avoid over-reliance by focusing on understanding AI suggestions and using AI to tackle mundane tasks, thereby freeing cognitive resources for more complex problem-solving and skill development.

- offer the template BP for those all dir above
- `quote` code to learn from <- good bp for code few shots
- make full use of `prd` as it is the main folder for all the code, description of the project
- technology adoption for good models to understand and abstract -> use next.js for example (the guide can be publicly exposed to gain insights and values) -> organize the repo in good shape
- write prompt by `markdown` can use the tab and completion of code-gen and even make AI finish the related content writing, that's the best practice for writing prompt
- try test-driven approach for the project, so you can use the test cases to generate the code and even the test cases with AI

* prompting engineer as basic needs, precise and contextual required
* patterns and ai techqinues use such as RestfulAPI、hook first … learn and use knowledge and experience from real world software engineering
* context engineering for ai code assistant
* Vibe coding code quality strategy
* break complex tasks into simple one in a flow, iterate and refine
* Clear architecture, modular and clear guide of application design and implementation
* Naming conventions
* API. Design Guidelines, Database schema guidelines, FE conventions
* External knowledge via MCP sources
* Pay attention to production level implementation
* mono repo with workspace
* file name no index but with concrete
* small projects in mono repos, small is beautiful, small is powerful in large scale view
* use best boilerplate to start quickly with maturity and elegance, print the BP in code boilerplate

Vibe coding manifesto

- pick the right techstack

basic abilities of vibe coding

- normal QA about project and programming details
- tab engineering , multiple places editing
- write tests for refactoring and stability
- help review code modifications and give suggestions
- implement new features both in all domains
- modifications of code logic
- documentation like Devin

Proactive and Iterative Refactoring: Developers must maintain a critical eye and actively refactor AI-generated code. The first solution provided by the AI should be seen as a draft, not the final product. Prompt the AI specifically for refactoring: "This function works, but can you refactor it to be more modular and reduce redundancy with the similarFunction in utils.js?"
Emphasize Reusability in Prompts: When requesting new functionality, explicitly ask the AI to create reusable components, functions, or services. For example: "Generate a reusable React hook for fetching and caching user data from /api/users/:id."

## Directory Structure

`.rules` basic folder for all rules and instructions for AI as project folder

---

`/commit` commit message rules for all the commit messages
- `/commit-msg.md` - rules for commit msg generation

---

`/tech` technology rule folder for all the tech-stack
- `/project.md` - general rules for all the tech-stack -> this is for project specific
- `/general/tech/*.md` - tech-stack guide for the project
  - `/oo-bp.md`: object-oriented programming best practice
  - `/react-bp.md`: react best practice
  - `/next.js.md`: next.js best practice

---

`/prd` the project folder for all PRD, modules and iterations related specifications

* `/main.md` - main folder for all the code, description of the project
* `/modules/*` - all the modules for the project (optional)
* `/iterations/*` - all the production iterations for the code, the trace is important (optional)
* `/commits/*` - so your basic instructions should be persist here and quote by the AI tool for iteration and generation (optional) -> it's git commit level track
* `/release/*` - similar to release logs and feature tags

---

use `__archived__` folder to archive all old prompts and instructions for the project, in dir to avoid search and indexing.

* add archived to search exclude and file exclude for the AI tool

## Techniques

* offer the template BP for those all dir above
* `quote` code to learn from <- good bp for code few shots
* make full use of `prd` as it is the main folder for all the code, description of the project
* technology adoption for good models to understand and abstract -> use next.js for example (the guide can be publicly exposed to gain insights and values) -> organize the repo in good shape
* write prompt by `markdown` can use the tab and completion of code-gen and even make AI finish the related content writing, that's the best practice for writing prompt
