# Arno's Agentic Research

## Patterns

* [single-prompt] [SAS] single prompt to generate content
* [ReAct-agent] `ReAct` pattern with one single agent, ai use CoT x generated tool / action / task-flow (call other agents) x reflection to generate content
* [reflection] this pattern involves repeating cycles of output generation, self-reflection, critique, and refinement, ultimately leading to more accurate and polished results.
* [RPA-loop] perception x reasoning x action loop
* [agentic-flow] [MAS] agentic with reflection / planning / action flow to generate content in automation and controlled by a meta-agent
* [agentic-mesh] [MAS] agentic flow with other dedicated agentic services / process to generate content
  * centralized control v.s. de-centralized and self-organized
  * hierarchical v.s. flatten
  * serial v.s. parallel
  * supervisor v.s. none-supervisor
  * communication protocol
    * end-to-end
    * broadcast
    * shared-memory-channels
  * state-based v.s. memory-based
    * short-term memory v.s. long-term memory
    * graph-based v.s. tree-based
    * vector store v.s. graph db v.s. relational db
  * tool invocation protocol -> MCP (model context protocol)
  * human interfere in agentic loop
    * human as supervisor
    * human as part of the loop
    * human as meta-agent

## Engineering Guide

* design for modularity and composability and observability and monitoring and error-handling and resilience and scalability and security and governance.

## Research

* [Implementing Agentic AI Systems: An Engineering Guideline](./agentic.research.google.md)
