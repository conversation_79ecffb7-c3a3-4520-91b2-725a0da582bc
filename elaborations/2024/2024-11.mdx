# 📝 2024.11 CORE

## 目标要点

- `e-studio` 继续系统化运营，收获 3 个可以持续使用的种子用户，继续做商业化相关的能力建设
- `e-studio` 聚焦 Chat 以及日常的工作流对产品本身的打磨 <edoc-reference-text refdoccontent="🦄 Elaboration Studio.sys" refdoccuid="2bd63932-67da-4c42-b725-e56a162c339c"></edoc-reference-text>
- `KESW` 演绎王川的观点做深度思考，进入「**投资准则**」等 Topics <edoc-reference-text refdoccontent="🙋🏻‍♂️ 硅谷王川投资 N 问" refdoccuid="485e0d35-8e0a-43df-a8d2-45c62cf8f1ca"></edoc-reference-text>
- **<u>Yida ALL IN PLUGINS</u>**，研发 MVP，紧贴业务落地插件实现价值 <edoc-reference-text refdoccontent="🧩 Yida 插件系统.sys" refdoccuid="c7454608-92eb-497a-8b1c-dd05ae8b0a25"></edoc-reference-text>
- 个人品牌力打造的研究，给出个人品牌建立的持续运营之道 <edoc-reference-text refdoccontent="🏙️ Arno.Portfolio" refdoccuid="2387b44a-738e-4134-8cb3-d51c0ecef4be"></edoc-reference-text>

---

2024-11-01 \~ 2024-11-03

- 北海道之旅 🇯🇵 → 疲劳奔波却又小有感觉的旅程，生日快乐 Arno！生日快乐 Vicky！愿 ❤️ 永存！

2024-11-04 \~ 2024-11-10

- 插件前端能力链路上预发环境，开始从前端插件的角度添砖加瓦 → 战役 KO 成为 S2 的关键聚焦要素
- 插件中心前端链路上预发环境（`AI Powered` + `OpenAPI Specs`）模式生成 → Boost your Efficiency
- `eStudio` 打磨 Chat 相关能力，Chat 依旧是核心 LUI 交互，和文档做深度整合的模式依旧是当下场景驱动的 CORE → 优化各类 Chat 的基本体验（复制、渲染优化等）
- 研究个人品牌的一些要素，被玉伯老师关注 \~ 💐

2024-11-11 \~ 2024-11-17

- 🤯 Yida Plugin 标的着 1130 的目标推进，落地平台能力 MVP，关注组件丰富度和周全 Call 的 AI 插件、闪记插件。
- EStudio 继续根据场景驱动的模式开始推递功能 Feature 的研发
  - **<u>Chat Session</u>** 化 → 挑灯夜战，终于可以 Chat with Session 了 👍🏻
  - **<u>EDOC Generator</u>** 模式的研发 → 真正助理 0 → 1 的 `Doc` 生成过程，后续证明还是直接 Chat 的模式更为自由


- 思考王川的「**四项投资准则**」，结合近期的特征进行研究 → 开始落地研究原则，投资之前的先验操作
- 上海 120 救护车晚秋的故事 <edoc-reference-text refdoccontent="💗 晚秋(Vicky).lover" refdoccuid="4c772fdb-320a-4b26-bc45-bc19fcefc1aa"></edoc-reference-text> 的奇特 session，最后被换得 2 天杭州的假期，也算是颇为舒适了 \~

2024-11-18 \~ 2024-11-24

- 🤯 Yida Plugin 打磨平台质量，支持自定义 Setter，支持新的插件类型（FORM or AI 插件 + Setters 模式）
- 🦄 `EStudio` 从日常的使用过程中打磨细节，汲取灵感，Portfolio 底部文章做更新宣导 → 引流 [e-studio.ai](http://e-studio.ai) 😁
- 🪙 总结 Arno 的投资 Principles and CORE SOP. → Combine with AI plz.

2024-11-25 \~ 2024-12—02

- 🤯 Yida Plugin 适配主要的 1130 的场景，准备内部 `20+` 精品组件的 *KickOFF*，设计 & 支持流程自动化节点 VMP
- 🦄 `EStudio` Chat 能力持续增强，持续完善「**<u>投资标准</u>**」以及相关模型的建立 <edoc-reference-text refdoccontent="✨ Arno 标准投资 SOP(Investment SOP).sop" refdoccuid="8016d850-a501-44ae-9cd8-1eff2b4a8090" reftype="doc"></edoc-reference-text> 以持续思考投资路径 & 判别式
- 保健品（营养补充剂）研究 → <edoc-reference-text refdoccontent="💊 营养补充剂研究.research" refdoccuid="0e3b11c4-9030-4a2b-ae63-ac1a1dbd12f0"></edoc-reference-text> 并最终选择了「姜黄素」、「Omega3 鱼油」、「槲皮素」作为常规补充剂交替使用

## Monthly Summary

✨ GOOD

- 蓝宝石 🔮 之美 ❤️ + 北海道的生日之旅 浪漫 \~ - 11.02


- `eStudio` → FRAME 可以直接将 Workspace 贴过来啦！<edoc-reference-text refdoccontent="🧬 FRAME" refdoccuid="c347b5e3-cc61-491a-89d3-35a5770f87bd" reftype="workspace"></edoc-reference-text> ← 做了非常多的小优化 ✨
- 极限为 AIGC plugin-center 点赞，一个下午，基本上生成 10+ 组件和分镜，框架 & 基础组件内容完成 ✅
- 执着于 `Zustand` V5 和 `AntD` / `LobeUI` 的升级也是一种对新鲜事物执着之勇气 - 2024-11-06
- ✨ 一起 Work at Home with `Vicky` - 2024-11-09
- 🚩用好 Todos 中的 `Flags` 聚焦关键的事项
- 挑灯夜战 - 也要将 Chat Session 的能力建设完毕！🎉 - 2024-11-11 可悲的矛盾存在。
- 第一时间的想法 💡 可以先放一放随后经历批判和思考后在执行，你会发现有很美妙的收获。11.12
- **<u>Arno</u>** 速度 & 质量推动 `yida` 插件体系的落地 👍🏻 - 11.25
  - AIGC + OpenAPI Spec + 前端产研效率 → 1 person = **<u>平台能力 + 前端架构 + 插件产物</u>**
- 利用 `Apple 手记` 来 trace ETIWTT 的点滴的确颇为有趣 🤔 11.26
- <edoc-reference-text refdoccontent="💊 营养补充剂研究.research" refdoccuid="0e3b11c4-9030-4a2b-ae63-ac1a1dbd12f0"></edoc-reference-text> AI 立大功 \~ 11.26
- 用好札记，对 daily life 最美 PRINTS - 12-02

😈 BAD

- 不要 🙅 凌晨 3 点过还在写代码自嗨，这并不骄傲，这是对身体的亵渎，Respect your body and soul plz. 🫡

🤡 UGLY

- 多用用新的产品特性，比如 `Coze` 然后获得启发，深度思考 🤔 `eStudio` 的方向和价值，你固然回更加笃定未来的产品形态 & 模式
- 无论节奏再快，也请慢下来欣赏生活 ❤️
- 尽可能多地使用 Github Copilot 才是明智之举，符合 AI 时代的「🌊」\~ - 2024-11-11
- 🚑 120 的 session 都给赶上了 🤣 - 2024-11-17
- Calendar 模式 + Reminder 模式的结合让「日常」似乎更加丰满了一些 🤯
- 所以「大澡堂子泡澡」也不失为一种 ETIWTT 的艺术，激发对 ETIWTT 在日常的更深度的思考
- 阅读，「微信阅读」开始进入，成为一种日常