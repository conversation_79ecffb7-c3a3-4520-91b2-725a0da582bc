# 🦄 2024.10 CORE

AI Summary

在2024年10月，关键事项可以总结为以下三个要点：

1. **e-studio的系统化运营与技术提升**：`e-studio` 开始系统化运营，成功收获了 3 个可持续使用的种子用户。同时，实施了 Notion Cache Hit 搬山计划，将精华内容剥离并整合到 e-studio 体系中，为未来的 AI 深度加持打下基础。e-studio的 Copilot Completion 机制得到了优化，提升了响应速度和内容生成的准确性。

2. **宜搭插件与开放体系的推进**：在宜搭插件生态的研究中，完成了MVP能力验证，并为后续的开放与插件开发奠定了基础。通过多个模块的试点，验证了MF的有效性，并在第一个MVP场景中实现了插件的加载与扩展，推动了插件中心的研发需求。

3. **个人成长与反思**：在个人生活方面，游泳成为了一种良好的习惯，AI驱动的写作模式逐渐适应并取得了积极成果。同时，反思饮食和作息习惯，意识到需要改善外卖和夜宵的依赖，确保更健康的生活方式。

## 目标要点

- e-studio 开始系统化运营，收获 3 个可以持续使用的种子用户
- e-studio Notion Cache Hit 搬山计划，只留精粹和剥离 core 到 estudio 体系之中，为后续的 AI 深度加持奠定基础
- KESW 演绎王川的观点做深度思考，并反思近期 A 股反弹，找到投资的有效机会点
- Yida 规划好 1030 的关键节奏，并开始推演 S2 Yida 生态 & 伙伴效能的第一环
- Yida `MF` 技术向团队推广第一步：「*CMD+K*」、「`DataManage`」、「生态 `Plugins`」
- 阅读：《系统化思维导论》和 Arno 已有的关键系统做 `connect`

---

2024-10-01 \~ 2024-10-07 十一国庆节

- 🧬 化繁为简，将每个时间段（CHUNK) 的事项做 「**<u>3</u>**」 件预测，1 \~ 2 件 Surprise，在此处做聚焦。
- 🧬 Frame Execution Engine 重构 <edoc-reference-text refdoccontent="👑 3D - 执行引擎（ExecEngine）" refdoccuid="b575264f-e9e3-45d2-8dd3-8781cda89420"></edoc-reference-text>，转移到 eStudio 之上，回归简单、精要主义的设计。「**<u>化繁为简，彻底去冗余</u>**」。开启 shot and transfer to e-studio 之计划。e-studio 成为缓存，notion 成为引用和内存级别之存在。
- 🦄 EStudio 支持了更好地 Completion 机制，使用了 `cache` 的上下文机制，具备更高效的响应速度和更准确的内容生成能力。
- 🧳 长沙之旅 with Vicky \~ 臭豆腐、口味虾 🦞、岳麓山、橘子洲、茶颜悦色，长沙要素叠满的几天 \~
- 💗 和 Vicky 的时光中，更进一步，聊起了订婚、育子
- 🧬 持续将 <edoc-reference-text refdoccontent="🗺️ 旅行(Travel)" refdoccuid="d812bb4c-6e25-4048-a1d4-607aee0483c3"></edoc-reference-text> 以及 <edoc-reference-text refdoccontent="👾 游戏(Gaming)" refdoccuid="9592e345-a8e4-41ec-abfe-4058788c2d6f"></edoc-reference-text> 等加入到 e-studio 体系之中。 - 2024-10-06
- 🐱 Hello, 狂野 Loca \~ （宇宙无敌烦人眼）😁

2024-10-08 \~ 2024-10-13

- 🧱 MF 完成 MVP 能力验证，组内规模分享，并以 `yida-next` / `data-manage` / `vc-deep-yida` / `cmdk` 模块作为重要试点，插件的 Fed 模块或将成为巨大的「**<u>机会点</u>**」✨
- 🪐 `Yida` 插件生态研究 **Research Report** & 组织向一号位汇报的前端技术方案材料，六脏俱全，大概率成为重点县项目，为 S2 的「开放 & 插件」结果奠定基础节奏。
- 🌟 `EStudio` 融入了更多有趣的想法：以 `Workspace` 产品体验为核心 upgrade 等策略上的优化。比如：workspace graph / outline, quotable workspace, pined workspace, desc, doc-transfer. → idea that sparkles \~

2024-10-14 \~ 2024-10-21

- 🌟「**宜搭插件和开放体系**」：组件、扩展的方案设计和 MVP 给出，理清关键的技术链路 & 研发重点，顺带验证 MF 的有效性，让 MF 正式登上预发环境 → 得到周全的 Phase 1 的认可 👍🏻 → 1230 将聚焦在内部赋能上，将插件的开发者体验做到「优秀」
- 🦄「**eStudio**」：持续打磨写做优化的体验，增加 workspace + elaborator 的模式，优化了 Writing Copilot 的缓存模式，同时优化了本地缓存的能力防止数据丢失。
- 🗂️「**Portfolio**」：<edoc-reference-text refdoccontent="软件 SDK 设计指南" refdoccuid="6066ca70-f04c-4bae-a977-15aabb440db2"></edoc-reference-text> 和「[浏览器模块化包加载机制](https://arno.surfacew.com/posts/en/browser-module-tech)」和「[Sandbox](https://arno.surfacew.com/posts/en/browser-sandbox)」文章，AI Driven / AI First 编写

2024-10-21 \~ 2024-10-28

- 🌟「**宜搭插件和开放体系**」：借助第一个 MVP 场景去打磨，完成 MF 加载插件，渲染扩展实现等最小 MVP 的运行时环境链路实现，将 Landing 页面组件 & 物料外迁为插件模式，将自定义 Validators 的需求用插件实现，将设计器中的 API 扩展外迁为插件实现。
- 🦄「**eStudio**」：完成运营文章的编写，持续打磨产品体验。
- 💗 FRAME 上建立 <edoc-reference-text refdoccontent="💗 晚秋(Vicky).lover" refdoccuid="4c772fdb-320a-4b26-bc45-bc19fcefc1aa"></edoc-reference-text> 的 Tracker ← LOVE really matters! 回忆真的很美！2024 发生了好多，值得感动的事情！
- 🔭 思考 <edoc-reference-text refdoccontent="🪐 范式转移的思考(Paradigm Shift）.dive" refdoccuid="d70c51ff-96e7-4370-9856-7a02479b094f"></edoc-reference-text> 问题，深入研究 AI 技术的范式转移模式

2024-10-28 \~ 2024-11-03

- 🌟「**宜搭插件和开放体系**」：全典型场景上预发，完成 R1 的技术评审，研发插件中心、附件预览的关键研发需求 → 插件，正式启动！立项！！开干！！！Fully BOOSTED!!!!
- 🦄「**eStudio**」：发布运营文章，探索基于 `Vercel` & `Next.js` 的微服务拆解模式，持续打磨 Workspace 的产品功能弱化粗糙感 → 重点打磨 Writing Scenario（Refine 模块增强）。研究了从「视频录制」、「场景驱动的子文章编写」、「首页优化」，让 eStudio 的推广变得更有效！

- 💗「**北海道之旅**」，生日之旅 🎂 期待美好的 MOMENTS 发生 \~ → Special Birthday GIFT :)
- <edoc-reference-text refdoccontent="💠 系统分析模型 (Systematical Analysis).mental" refdoccuid="f1670110-8625-4243-8a76-7b4695b2e104"></edoc-reference-text> 结合书籍《系统化思维导论》加深

---

## Monthly Summary

✨ GOOD

- 游泳 🏊🏻 成为了好习惯：水下睁眼、水下下潜、水下思考模式启动！ → 逐渐成为习惯
- AI 驱动编写文章的模式定然是未来！ → <edoc-reference-text refdoccontent="❓ Why I create elaboration studio?" refdoccuid="5fa059d0-d3a9-4efb-92ae-b08649430e3e"></edoc-reference-text> 有且仅有深度使用才会驱动优化 \~ - 2024-10-11
  - 写了 3 篇文章了，不错，继续保持。
- 逐渐适应去 Notion Focusing on eStudio 和纯粹系统软件的模式，Notion 和 iPad NB！
- 🤩 AI + v0 powered prototyping generating is perfect!
- 🦄 利用 Copilot 的 Refine 能力来优化 Why I Create [E.Studio](http://E.Studio) 的文章，自己塑就 & 打磨很重要
- 🤯 RAG 系统 EmbedJS 带来的崭新的可能性探索 → RAG crafted by FRAMEWORK level is much better than your own crafted ones.
- 🧩 去上海参加「TB 和宜搭共创」模式的一些会议，感受一下包括商机、产品、技术以及插件的空间可行性，对自己来说是一种有价值的 Participation。

😈 BAD

- 饮食有点糟糕，要退出外卖模式，退出「夜宵模式」了！ - 2024-10-18

- 实在是睡得太晚了，12 点半之后不要碰手机，Required.
- 饮食 🍛 品质需要再控制一下，健康才是 FIRST-PLACE，10 月问题很大

🤡 UGLY

- Yida 公式和子表的工单，的确令人头大，后续还是尽量交给 @龙彦 去处理吧，最好做到「术业有专攻」
- 迪士尼之旅，真的开始「累起来了」，年龄 or 心态，亦或是已经经历过的事物开始显得略微 `tedious` 了？
