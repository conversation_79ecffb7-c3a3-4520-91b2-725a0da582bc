# 🦄 CAREER CORE 2025.04

## DASHBOARD

### ✨ EStudio

`Systematic Agent` token birth on `e-studio` [e-studio.design](../design/ai-e-studio.design/e-studio.design.md)

> Let's use scenario driven development method.

* rethink and re-design the `e-studio` future [e-studio.future-scenario](../design/ai-e-studio.design/e-studio.future-scenario.mdx) - 4.27

EStudio Agentic System Scenario PMF

* ⚜️ [wealth-investment] unstable economic environment, unstable stock market, how to plan my wealth / investment in 2025.
* 🧑🏻‍💻 [python-exam] cope with DingTalk Python exam in actions [python-learn.sys](../systems/arno's-python-programming-language-learning.system.md)

### 🖼️ Yida CodeGen

[AI Code Gen](https://www.e-studio.ai/elaborator/10ddc74c-6682-4709-a053-6783845db5ca/systematic)

* [research](native-editing) edit content directly in the canvas page with native editor instead of props-driven editor

### 🦸🏻 CORE SYS

### 📟 Fragments

## TRACKS

### 🖼️ AI UICodeGeneration

* [theme] research on support `theme-selector` as part of prompt engineering, make AI understandable `theme` design, and implement the `theme` to make the AI-generated code UX friendly - 4.01
* [ai-canvas] show generation of the code in the UI - 4.02
* [ai-canvas] upgrade tech-stack / ui-libs of `yida-manus` project for DX - 4.02
* [ai-canvas] generation encounter error should contain the `autofix` mechanism to fix the error both in compile & runtime - 4.02
* ✨✨✨ [ai-canvas] finish the CURD of `AI-Canvas` powered by Yida's API x UI generated code - 4.03
* [ai-canvas] support code-panel to show batch-updated code in UI - 4.03
* [ai-canvas] 🎉🎉 write the MVP case for `Headless CMS` scenario read Yida's data and show in the UI - 4.03
* [ai-canvas] add `ahooks` to save output code - 4.03
* [ai-canvas] add use Yida API series to improve 2/8 scenario of CURD operation to improve hook and reduce ai-coding amount combined with `ahooks`
* [ai-canvas] support code-panel to show batch-updated code in UI and its final result - 4.07
* 🆒 design the [AI Assets](../design/ai-canvas/core/ai-canvas.assets.design.md) for the protocol and components driven pro-code generation and finish the MVP demo - 4.07
* [plugin-loader-wrapper] support all use FormLazyLoadWrapper to load component lazy and support one componentName to rule them all - 4.07
* 🌟🌟🌟 [CTO-report] prepare and do the report for the CTO of new DingTalk AI Code Gen process [AI Canvas](../design/ai-canvas/ai-canvas.report.md) - 4.04 ~ 4.**08**
* [ai-canvas] find most valuable scenario to build basic abilities of canvas app -> Print Scenario - 4.08
* [ai-print] write the debug console for template generation mvp in HTML as print agentic generation result - 4.09
* 🖨️ AI Print Scenario MVP -> [ai-print.design.md](../design/ai-canvas/scenarios/ai-print.design.md) - 4.09
* 🚀 support [chat] scenario render and generate with chart datasource - 4.11
* [hotfix] ✅ markdownPlugin rendering error patch - 4.11
* [canvas-page-render] make online resources stable in canvas dependency - 4.14
* [canvas] ✅ `dependency` to online resources of `g.alicdn` - 4.14
* [canvas-page-render] make online resources stable in canvas dependency - 4.16
* [precise-modify] support precise modify of the generated code - 4.17
* [error-optimization] adapt agentic generation with rendering error retry / compile error / eslint error retry to make the generation more stable - 4.18
* [headless-cms] use tool-call of form data schema to make headless cms scenario more stable and reliable also to work with more info / display scenario - 4.18
* [prompting-effects] try to cooperate and cleanness of the prompting content - 4.18
  * @MiWei to adjust basic prompting and cleanness of design rules
* [prompting-effects] try to cooperate Google AI Prompt research results into my page-gen works and improve the prompting quality [ai-page-gen.core.prompt](../design/ai-canvas/core/ai-page-gen.core.prompt.design.md) - 4.21
* [arno] Arno's basic Financial System and investment full booster engine -> add recent research & reports - 4.21
* [revision] support revision of the generated code quickly (yida's schema version mechanism) - default page revision skills - 4.21
* [chart-render] use tool-call of chart data schema to make chart content render in agentic process MVP with @YueFei - 4.21
* [file-workspace] explore the idea and possibilities to generate page x component architecture in current agentic process - 4.24
* [dingtalk-award] DingTalk Award scenario adjustment to fit better user experience & generation effects - 4.24
* [focus][CRMSimpleExample] write the sophisticated example for `CRM` scenario to show the basic abilities of `AI Canvas`
  * ✅ PRD focus on DemoShow's home page customization generation - 4.28
  * ✅ add `antd` to support more variation - 4.28
  * ✅ add jump to data-manage page in home - 4.28
  * ✅ add all links to home page module with BP exploration - 4.28
  * ✅ home-page style variation try with @DuoYue
  * ✅ mock data stability @XiaYang - 4.28
  * ✅ 7-table level try for the base-data management - 4.28
* [ai-canvas] prompt optimization and engineering optimization for stable app gen. - 4.29
  * ✅ refactor prd-gen to more precise and detailed design - 4.29
  * ✅ basic code-gen use better English and precised prompting skills via Claude - 4.29
  * ✅ refactor dedicated scenario page prompt to `application` and `portal` page for current stage - 4.29
  * ✅ step by step log generation prompt and trace generation process and dig & optimize & renew - 4.29
* ✅ [ai-canvas] more details should be added to make sure the generation is stable and reliable - 4.30
  * add router related api for page jump
  * try claude parallel project generation in SPA pattern of front-end application code-gen

### 🦄 EStudio Systematic Agent

* [e-studio] support use `Claude` model family as the LLM - 4.06
* [e-studio] MVP version of `Systematic Agent` to support the daily use in `e-studio`
  * ✅ generate plan - 4.06
  * ✅ execute plan mvp - 4.07
  * 🔥 write core / necessary tools implementation - 4.08
  * 🦄 run and adjust the agentic process in real-world Arno system - 4.09 ~ 11
    * ✅ planning model switching to `Claude` 3.7 -> stable the graph generation quality and try to support the partial graph generation with `streamObject` via ai-sdk - 4.13
    * ✅ try to support `deepSeekR1` model in final content generation, may use `useChat()` instead of `useCompletion()` - 4.10
    * ✅ add the missing systematic-view as full final prompting in a more structured way - 4.13
    * ✅ add more details in `Flow Diagram` including: correct space margin / input properties (tooltip + detail) / output properties (tooltip + detail) - 4.13
    * ✅ simplify the logger information and support to show state map of context - 4.13
    * ✅ support strong / medium / easy planning mode - 4.13
    * ✅ full-tools cases run in the `e-studio` and test its boundary and quality - 4.13
* [e-studio] claude 3.7 adapt the reasoning skills (I think use the deepSeekR1 model is fine) - 4.14
* [llm] add GPT4.1 series model to support the agentic process with more choice on demand - 4.16
* [sys-agent] ask agent should add `systematic-view` as part of the context by default - 4.16
* [mvp-ppt-toolset] MVP idea of PPT Toolset for EStudio Agentic System in e-studio - 4.19
* [arno] Arno's Health System (1D Basic image) - 4.20
* [arno] Arno's self image mirror system and Family System (Arno & Vicky) - 4.20
* [systematic-agent] add `genes` / `elaborations` / `reference` text-fields and refactor to show all contents & refs in the UI and merge request into a single request - 4.20
* [arno] build the rest of the systematic-view for Arno's system and investment full booster engine: e-studio, ETIWTT, Career, Yida x CodeGen, etc. - 4.21
* [systematic-agent] add outline of the systematic-view to scroll and show the current position in the UI - 4.22
* [file-workspace] explore the idea and possibilities to generate page x component architecture in current agentic process - landing page MVP - 4.22
* create double-synced VSCode plugin for `elaborator-systematic-view` and `elaboration-doc`
  * ✅ [server] api user token support to get agentic / `e-doc` content to markdown - 4.28
  * ✅ [server] implement parser from markdown to `e-doc` content and vice versa, with meta description and etc. - 4.29
  * ✅ [client] vscode-extension to fetch and create doc from remote use api-token - 5.01
  * ✅ [client] sync the modified content back to remote server use api-token - 5.02

### 📕 Research and Learn

* [dev-in] learn the general idea of product of `dev-in` - 4.07
* [ezsite](https://ezsite.ai/) for site-generation - 4.14
* [coze-space] research on coze-space and its product - 4.21
* [research] how to implement the native editor in the canvas page - deepResearch - 4.22
* [deep-think] great change took place in Yida x DingTalk, what is the way I shall follow? - 4.22
* [deep-research] from global perspective, and China perspective, research the stock-marketing related reports to inform myself. - 4.22

### 🐛 Bug Fixes & Maintenance

* [plugin] plugin i18n related bugfix - 4.08
* [arno-vicky] let's craft a emoji-calendar to share thoughts with each other - 4.07 ~ 4.08
* [AICookbook] write the AI cookbook for Arno's system and daily routes - 4.20

## REVIEW

✨ GOOD

* A CodeGen, fight for what's truly worth and valuable, and make it happen. - 4.16
* `VibeCoding` the efficiency booster to reshape the future of coding in every corner of the digital landscape, I truly believe it - 4.20
* recommend yourself to do research with deep-research at least 1 per day, raise this as a habit - 4.22
* final year FY25 is 120W+ package, and still feel thankful about Yida and @YZQ - 4.25
* so luck to use **Google Deep Research** and **ML Notebook** for research and learn
* surf **cursor** is so great and can not stop using it ~ that's a great product should be.
* Google Gemini Pro 2.5 x @Wanqiu's teaching lesson is so great and helpful ~ - 4.26
* VSCode Ext. for `e-studio` is so great and helpful ~ - 4.30

😈 BAD

* 祸兮福所附，和 Vicky 的争执，我相信最终换来的是成熟和稳定，是向更深层次的爱 ❤️ 的演进。 - 4.13
* fall of @ZhouQuan's team, Yida is lost its position - 4.22

🤡 UGLY

* although meet CTO 1on1 is not happening, but still be a great chance to elaborate the idea with @ZhouQuan x @PiShi and etc.
* if you were a leader, you should consider the tradeoffs and worst case of the decision to craft out something, also the best case and the benefits, and know your cards and the limits. - @JiangQi 4.09
* `3.5` but `high-potential` little awkward - 4.14
* uncertainty of @WuZhao's coming back, which leads larger impact on Yida's team - 4.16
* demoShow of ai-canvas is not ready so causing me so awkward, resulting in not gaining the trust from @ShaoLei - 4.24
