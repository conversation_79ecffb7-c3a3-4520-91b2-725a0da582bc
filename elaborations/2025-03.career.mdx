# ✨ CAREER CORE 2025.03

## DASHBOARD

### 🔥 Core Targets

🦄 Introduce the `Systematic Agent` 🌟 idea to `e-studio` [EStudio Systematic Agent](../design/ai-e-studio.design/e-studio.system.agentic.design.md)

- start with a simple `Health` and `Exercise` system-agentic assistant MVP and PMF
- try a `Alibaba Stock Analysis` quiz for system-agentic assistant to answer the questions and test its abilities (use deep-thinking to answer the questions)
- try a more complex scenario of `General Investment to predict the 2025's investment strategy & plan & actions` system-agentic assistant to use more-tools (search, stock-query, etc.) in single agent form

---

🖼️ AI Canvas MVP for 526 DingTalk 8.0 -> [AI Canvas](../design/ai-canvas/ai-page.design.md)

### ⏰ Cycle / Watcher Stack

### 📟 Fragments

- [e-studio] support redirect to the required-login page when user is not logged in after the login page is updated

## Tracks

### 🖼️ AI Web Canvas

- write a FaaS / Serverless demo for one-time es-build / babel compile for source-code to runtime-code in browser - 03-19
- support federation load UI module from existed plugin or other scenarios - 03-21
- esbuild-wasm in-browser build for single jsx file in standard format (babel-less) - 03-23
- canvas 2d / animation libs research for AI to operate - 03-23
- Math scenario dig with @Darling for given scenarios on math-problem-solving - 03-24
- upgrade it to es-build-wasm browser-side build and support single jsx file - 03-24
- try some more scenarios with AI: in Education of Math and Physics + Info Graph and Diagrams to make prompt engineering more-powerful & stable with DeepSeek-R1 & DeepSeek-Chat - 03-25
- ✅ add `Mermaid` support for Canvas - 03-25
- ✅ add `Framer Motion` support for Canvas - 03-25
- ✅ esbuild-wasm as federated module to consume in all fields via ai-canvas across all of the scenarios - 03-25
- ✅ integrate with diff and replace to make re-editable content available via @JiangQi - 03-25
- ✨ illustrate the CORE idea of AI Canvas -> GUI Maker for AI Era - 03-26
- 🤯🤯🤯 production research on Loveable AI + prompt collector -> [Loveable AI](https://alidocs.dingtalk.com/i/nodes/m9bN7RYPWdyrPBREcQn3RbdXVZd1wyK0) - 03-27
- support dynamic load dependencies via AST parser of code file - 03-27
- fix dynamic tailwind css / runtime compiler when source-code is changed - 03-28
- support ShadCN and Radix UI components load and render in canvas - 03-28
- support framer-motion for animation - 03-28
- support lucide-react for icons - 03-28
- fix `lucid-react` icon mis-use and use dynamic import to avoid the error cause - 3.31
- step diff operation -> use diff-modification to make the single-file code editable @XiaYang - 3.31
- finish the report research on both `VercelV0` and `Claude Artifacts` - 3.31
- fix the issue `iframe` designer css loading for `radix-ui` components - 3.31

### 🧠 Yida Opening & AI & Innovation & Experiments

Key Strokes

- 🌱 Yida Opening for third-party developers -> [Plugin Third Party Opening](../design/yida-opening.design/yida-opening-sdk-x-scanfold.design.md) 🛑 HOLD

---

- 🧩 Plugin Open to Third-Party Developers -> write the mini demo of Opening Code in Yida and Github that can run in current pre-dev env [Plugin Third Party Opening](../design/yida-opening.design/yida-opening-sdk-x-scanfold.design.md)
- 🤔 AI conference notes -> [AI Battle Key Report](../idea/tracks/2025-03-03.ai-report.md)
- 🦄 write the mini-demo of `AI Composable Display UI` for Yida x AI conceptualization
- [AIBlock] try the demo and know the factors of [AI Block](../design/ai-canvas/ai-page.design.md)
- 💡 idea: talk with @XinLi about the new idea to `AIGC` horizontal app
- 💡 idea: talk with @ChenHan about the agent-abilities into Yida for opening skills to plugin system
- test `qwen-qwq-32b` can fluently use MCP protocol to perform tasks correctly
- think and dive the Yida FY26 KO and talk to key persons to get insights and align the direction after the kickoff meeting by 2025-03-18
- write the open-source mvp in plugin-opening system to make demo work x AI cursor -> it works on Github 🎉 - 03-19
- [Open Plugin] for DingTalk Doctor scenario to use Yida's opening system & plugin system - 03-20

### 🦄 EStudio Systematic Agent

- product conceptual design of `Systematic Agentic AI` for `e-studio` —> target scenario: build Arno's investment system
- [SystematicAgent] 🦄🦄🦄🦄🦄 design the concepts for the future of EStudio Systematic Agent
- generate system-agentic elemental content by AI to auto fill according to the `Systematic Thinking Approach` - 03-24
  - turn out to see with existed `Systematic Thinking Agent` to generate the content template and just do copy-paste to generate the content
- use `gpt4.5-preview` to do the deep-thinking and use `gpt4o` to do the final answer -> 03-24
- use `Perplexity` to search the information and use `Stock-query` to get the stock-price and use `News` to get the news-info -> API key amount usage
- 🧠 design the `state-machine` driven planner and executor architecture for `Systematic Agent` running in `e-studio` - 03-27

### 🚥 Horizon

- 🤩 [horizon] ai-generation topic content and cron job to trigger the generation
- [Horizon] fix cronJob of `ai-generation` topic and make it work
- 📦 release: fetch all rss-ai feeds and send to dingtalk bot
- [horizon] make better home-page and user-exp for app to use, btw, fix the time-zone bug of cron job - 03-20
- [horizon] add more channels and make it more stable to work and collaborate with DB - 03-24

### 🌟 Personal Impactions

- 💡 idea: find BP for cursor and write an guide in my blog
  - compile [cursor bp](https://github.com/PatrickJS/awesome-cursorrules?tab=readme-ov-file#frontend-frameworks-and-libraries) to my common guide
- 🎉 [av-cards] create card CURD and count app in minutes via Arno x Vicky -> 03-23
- [av-cards] support upload image manually and add <https://yugioh-card.linziyou.info/> link to card designer guide

### 🧩 Plugin Development & Optimization & Opening

- 🪲 fix: plugin load on demand in yida-apps
- 📦 release `Skeleton` plugin as UI plugin
- 📝 `Markdown` plugin optimization for Mobile and setters' description
- [Plugin] design the solution for `Composite Fields View` of complex type plugin to reach the final goal of `Form Plugin`
- [Plugin Platform] `Plugin` platform related enhancements, about 10 requirements
- [Plugin UI ErrorBoundary] fix the error boundary of `Plugin` UI to avoid the whole app crash
- [Cloud Print] optimize and add support for `app` page to use
- 📦 release 5 requirements of `Plugin` platform
- 🆙 upgrade: use `ai-sdk` of yida to Refactor the `AIFormAutoFill` and `AIGenerativeButton` plugins
  - bugfix: `AIGenerativeButton` plugin `_csrf` token error use standard api from AI team optimization
  - bugfix: `AIGenerativeButton` call ds connector invocation sometimes wrong error
- 🐛 fix: `mobileContent` render in `mobile` mode of `AIGenerativeButton` plugin
- [Plugin-AIAutoFill] component upgrade: new streaming APIs, stable Filling Abilities and upgrade streaming sdk and fix the issues of `AIAutoFill`
- 🎉🎉 [AI FormAutoFill Plugin] support more fields to fill and timeout -> 1.5 mins and auto-correct the truncate error as json string
- upgrade ai-generation plugin to enlarge the `max_tokens` to 4k and switch connector call from GET to POST
- [idea] 🗣️ talk with @LuoYuan x @YouZe about the future opening plan of Yida Plugin System
- i18n dev for `Plugin` platform - 2025-03-18
- upgrade ai-form-fill plugin to support `onResult` callback and `onError` callback - 2025-03-18
- talk with the key-person and know the opportunity based on the ctx of [FY2026 Yida's opening plan](../design/yida-opening.design/yida-opening-x-plugins.plan.md) to find the recent zoom-in points
- [WeeklyPlugin](../design/yida-opening.design/yida-plugins-vendors.md) enrich the high-quality plugins for Yida -> Maintain the FORCE

### 🐛 Bug Fixes & Maintenance

- 🐛 fix `Time` component related blocking issues (MobileField & Formatter)
- ✅【IST202502270019】手机打开数据管理页报错 -> 本质上是按需加载的问题需要修复
- [YidaFE] version release: charge the release work of Yida FE process - 2025-03-20
- [IST202503210012] `TimeField` plugin default value in TableField + support time step 15 mins config @ZiMo - 03-26
- ✅ scan the rest of support problems and do quick-fixes (component hide + JSAPI auth error + rich text paste) - 2025-03-27

### 📊 Planning & Reporting & FY25

- 🥅 design: write your OKR and prepare the performance & job report
- 📃 report: performance & job report of FY2025
- 📝 design the [UX kit](../design/ai-team-infra/ai-team-infra.design.md) for Yida AI Team
- 📃 try list meta-data fields demo for `Finance` plugin extension & example
- [OpeningYida] plan the future of Yida Plugin System x Opening x AI of Yida -> [yida-opening.design](../design/yida-opening.design/yida-opening-x-plugins.plan.md)

### 🃏 AVCards

- [AV_Cards] build the original demo with `supabase` via AI to manage cards grid, can add new or remove existing cards and have countable number of cards with passcode to login - 03-24
- [av_cards] support upload images and add / delete cards - 03-25
- [av_cards] more stable cards info and guide for user to link to the card designer external site - 03-27

## Monthly Summary

✨ GOOD

- AI Prompt Driven `Horizon app` online! Iterate and design so quick! 🔥🎉
- idea of `Systematic Agent` 🌟 for `e-studio` and `opening` for new ERA and possible new opportunities
- create app like `av-cards` to test the abilities of AI and make it work in minutes, amazing! 🎉
- new focus on `CodeGen` engine of Arno's trip in Alibaba and DingTalk
- surly have `sex` with @Darling in Shanghai ~ 👻
- more engaged time with @Vicky and let's craft things together! 💪
- NEW DIRECTION: AI Coding / Vibe Coding / AI Canvas / AI Web Application development

😈 BAD

- use emotion reason to attack @Darling is pretty bad idea

🤡 UGLY

- 🤩 watch for opportunities to craft the PMF of DingTalk and contribute my own parts
- relationship with @YueFei seems to be a bit of awkward, stick and align with your **Leader** will be fine, interests conflicts will be fine, we just do it to prove our values and it shall absorb the positive energy and pals together!
- Yida Opening is in vain for business scope change, it is not a good idea to continue to invest in it
- leave those fake requirements and the real important ones shall be floating up to the top a few days later, it is not a good idea to continue to invest in it
