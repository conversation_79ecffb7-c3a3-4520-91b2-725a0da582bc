# Weekly

2025-02-24

* 🤩 idea: visit WuFangZhai and learn user's opinions and requirements and demonstrate the Yida Platform AI new features

2025-02-25

* 🤔 think about the future of Yida x AI and take necessary notes and decisions for recent development tasks
* 📦 release: release `AIForm` and `AIAutoFormFilling`
* 📦 release: Landing Page Blocks
* 📦 release: Enterprise Information Search plugin

2025-02-26

* 🤩 think: `../design/ai-block-composer.md` full elaboration and inspired by the `Cursor` way to build the `AI Block Composer`

2025-02-27 ~ 2025-02-30

* 🚀 perf: add load as demand ability to plugin runtime framework
* 💎 dev: `AIBlockComposer` as a mono-repo for AI powered stuffs which organized in `open-source` way, write the prototyping engineering demos
* 🌟 design: context engineering for AI Block Composer -> [Context Engineering](../design/ai-context-engineering/ai-context-engineering.guide.md)
* 💎 dev: `AIBlockSpec` as a standard for AI Block Composer and connect open-source way into Yida Platform, write the prototyping engineering demos

* 💎 dev: finish the `AIFeeds` in Horizon App finish the push demo and target to add into ALab. chat group as a bot

# Weekly

2025-02-18

* 🦄 design: AI Auto Form Filling continue Prototyping (Advanced with LIST / CONDITIONAL FILLS)
* 🌟 feat: draw AI material / designer architecture diagram for new ERA of AI driven Yida
* 🐛 fix: View Replacement Plugin domain adapt *TableView* and *BasicFilterView* (Use `TimeField` to take for example)
* 🐛 fix: a hard problem caused by the runtime error of plugin-framework
* 🐛 fix: the problem that the `DatePicker` component cannot be selected in the `BasicFilterView`
* 📦 release: Link Component and series of optimizations on Yida iteration

2025-02-19

* 🦄 design: `AI Full Diagram` of Yida and Your specific Domain to contribute to the Yida Project
* 🌟 feat: AI Auto Form Filling support image recognition via `ImageField` x Domain Completions

2025-02-20

* 🤩 idea: ask questions and zoom in on the paradigm of AI driven Yida generation mechanism and combine DS Thoughts results with thinking pattern in e-studio to output related results with my leader
* 🦄 design: design and implement the `AI Generative UI Block` standard for Yida based on the Homepage / Landing Page Blocks as examples
* 🌟 design: consider a new `list-component` for Yida and driven by our design team
* 🐛 fix: a problem caused by the `SelectField` dynamic options problem

2025-02-21

* 🤩 idea for Yida Agent Abilities Platform v.s. Yida Platform -> FOR BUILDING agent abilities into Yida Platform
* 🐛 fix: a problem of mini-package rendering scheme

Weekly

* 🌟 feat: support `Enterprise Information Search` query ability for new Component of `Form Component`
* 🐛 fix: yida-builder for private resource deployment problem for @XiZe

---

# 💎 2025.02 CORE

## 目标要点

* 💒 建立对「**婚姻**」思考的蓝图，人需要变得成熟。
* 🧩 「**连接类组件扩展**」「**流程自动化节点扩展**」围绕 「**<u>AI + 开放</u>**」的话题，做出声音，让周全看到因我而不一样的事情，为绩效季出力！ <edoc-reference-text refdoccontent="🧱 Yida.sys " refdoccuid="d74b0ce1-**************-206a6093d1f3"></edoc-reference-text> 思考下一个阶段 AI + Yida 的组合范式是怎样的？
* `🦄 e-studio` <edoc-reference-text refdoccontent="🌅 Horizon.sys" refdoccuid="3d19255e-8b0b-4f21-91cf-50034feaed3d"></edoc-reference-text> 做出原型后，尝试向社区推广。将 `DeepSeek R1` 作为 e-studio 的武器之一引入。
* 🤑 基于 AI & <edoc-reference-text refdoccontent="🪙 Arno 的财富系统.sys" refdoccuid="4cd363e6-36e5-4ca4-9302-6582cb81500b"></edoc-reference-text> 输出 Arno 2025 的投资思考框架，并发布到 <edoc-reference-text refdoccontent="🏙️ Arno.Portfolio" refdoccuid="2387b44a-738e-4134-8cb3-d51c0ecef4be"></edoc-reference-text> 之上。
* 🚜 学习 `Nginx` 的服务器架构 & Gain INSIGHTS of AI First Study 🤓

## Sessions

* 🥰 和晚秋 の「厦门」的旅程，并在一周年的时候双子塔上求婚 \~
* 有灵魂的「卡片」系列 😁
* 有爱，才会有 `NICE` 的室内写真 ❤️
* 一天两个 AI 驱动的插件，让插件中心变得丰富，先走量
* `.context` 的探索
* 将 `.cursor` 和 `.context` 的思想迁移到设计文档 & 日常文档使用 Cursor 编写的维度之上
* KKK `AIGenerativeButton` `Markdown` & `DingTalk Agent`
* 王炸 `AIAutoFillForm` 驱动的自动填表能力：适配子表、多模态甚至能力，直接爆款
* AI 插件四小龙 🐲 `AIButton` / `AIAgent` / `AI AutoFill` 以及 `AI Validator`
* AI + Yida 时代的作品思考 🤔 <edoc-reference-text refdoccontent="🧱 Yida 宜搭.sys " refdoccuid="d74b0ce1-**************-206a6093d1f3"></edoc-reference-text>  x <edoc-reference-text refdoccontent="🦄 Yida 宜搭在 AI 时代的主要范式转移是什么？" refdoccuid="07981e3f-ce60-4723-a4d9-3ababfbf1ba4"></edoc-reference-text>
* AI Composer 的思想：*Block Composer* x *Page Composer*
* 半天 Dev 完成企业信息查询插件
* 久违的见面 with 显志，with 晚秋，《青楼》之剧本杀
* 深度思考 Yida 的应用模式的范式转移
* 和 @金喜 团队对焦智能体未来和 Yida 连接的空间，尤其是插件和能力的深度链接
* 正式定义和执行 AI 时代下的执行 SOP → <edoc-reference-text refdoccontent="✨ AI 时代下做事的 SOP" refdoccuid="a9f2e320-945f-4498-9ce2-f63a5a31f825"></edoc-reference-text>
* Cursor 驱动的 Elaborations 系列：深度演绎 `Block Composer` ，AI 结合上下文后，配合提问、引导个体思考回答，再进行回答，如此往复的模式，可以将思考上升到新的高度

## Monthly Summary

✨ GOOD

* `DeepSeek` 插件是比较有趣的！800+ 的安装量，这波 PR 有价值！我们还需要更多的有趣的工具和方式来推动和证明插件开放的价值。
* 平凡的本质就在点点滴滴，一顿饭，一次出行，都需要渗透爱意
* **<u>化繁为简，关注本质，而非形式</u>**，无论是 Notes / Markdown 都是回归思考本质的形式，e-studio 也是秉持这样的思想来做设计的，毕竟「**大道至简**」
* AI 插件四小龙的确有趣，有灵魂，侧面证明插件的价值和意义
* 学会使用 `降维思考` 的方式，思考问题，当前开发的组件插件在低复杂度的情况下，会被高级的类似 Cursor x Yida 生成 Block 的模式做降维打击，因此过于投入的意义和价值其实并不大，需要关注，最好就 AI 简单包装也就足矣
* 对核心模块，比如 `yida-federation-runtime` build-plugin 增加单元测试，的确令人舒心 ☺️
* Cursor + DS / Claude 3.7 是好东西，结合 eStudio 进行 Combo 我理解也会成为王炸的
* AI x Cursor 对 <edoc-reference-text refdoccontent="🌅 Horizon.sys" refdoccuid="3d19255e-8b0b-4f21-91cf-50034feaed3d"></edoc-reference-text> 的重构和飞速实现的确令人惊艳！

😈 BAD

* 宜搭过于混沌了，事情糅合在一起之后，效率有所明显降低，缺乏活力！
* 晚睡，依旧受到自身的诟病 😴 请务必早睡！
* 有些事情，要学会判断价值，谨慎聚焦 THX.
* 对性 & 欲求的搁置，爱的冲动和主动性，还是非常需要的！

🤡 UGLY

* 第一次探洞失败，哭死 😭 in XiaMen Conard 😂
* 继续 Hunting for the HOLE，积跬步至千里，逐渐适应 😂
* 有些兴奋，但也感觉无奈 🤷🏻‍♀️ EStudio 被 Cursor 的模式降维打击了 …
