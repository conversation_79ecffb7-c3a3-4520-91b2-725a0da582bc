# 🦄 CAREER CORE 2025.05

> 你祈求的今日，是昨日殒身之人所求的明日。

## ✨ EStudio

[all-in-vscode] 

- [x] remove dead `elaborator` code and clean 45+ files to boost the performance of e-studio - 5.27
- [x] design the **best** and **impressive** vscode mirror experience of extension usage experience - 5.27
- [x] write the core services / api-routes and UI for all slugs workspace, ela-doc, agent, blocks - 5.28
- [0c] let's create workspace / elaborator / doc / block in vscode extension
- [0c] let's keep in sync for workspace / elaborator / doc / block in vscode extension (version sync, full agent sync, background save and pull, etc.) -> user experience is the key
- [2c] try support files type of resources to be synced with extension

---

[agentic-context]

- [2c] agentic systematic agent -> provide best context-engineering MVP for S&S agentic systems
  - copy types of system-agent context (shallow / deep / deeper)
  - direct ask AI to organize the context in a QA form for systematic agent in related formations
  - direct qa chat with AI on e-studio for systematic agent
- [2c] craft a simple MCP server to cover the agentic context abilities above to work seamlessly with vscode copilot and other MCP powered tools for experimental usages

---

- [3c][saas-boilerplate] build the SaaS boilerplate system for Arno's future SaaS projects, with full-stack and off-ground tool like Jules and Cursor and use it as recommendation for vibe-coding first repo
  - consider transfer the existed projects like `horizon` app into this boilerplate system as one impl. instance
- [3a][quotes] build the quotes system from scratch -> full-stack and off-ground tool like Jules

---

- [1][e-studio-extension] release the vscode extension to the marketplace and keep version in sync

## 🖼️ C4 AI

> explore the new curve of AI Code Generation.


## 🦸🏻 Leverages

[portfolio] keep upgrade SEO features & content for Arno's portfolio site

- [3][bp-portfolio-growth] by systematically learning the growth strategies given by the growth hacker, and apply it to Arno's portfolio site

[wealth-investment] ⚜️ build Arno's wealth and investment system in `e-studio` with full-enhanced model


[production-growth]

- [2a] deep-research on the topics and build the growth idea from scratch
  - [x] research on personal digital brand and site growth strategies
  - [ ] research on 2C and 2B products growth strategies
- [2c] book reading: hacker growth from silicon valley to china, and the growth of 2C and 2B products general conception build
- [2c] **Growth**, it's all you need to learn about both 2C and 2B products, and your e-studio growth, lead to real actions with context of C4 and e-studio, and the growth of Arno's portfolio and personal brand (personal, 2C, 2B).


[production-research]


[DAO](../kesw/wisdom/DAO.md)

- [x] use Manus to generate DAO conceptual and design guide-line compared with Google Deep Research
- [3h] read paper book (classical one) with fragments time ;)

[MentalModels]

`content/models.mdx` Claude offered some models to learn, one day one model, on the go ~

> AI Powered required. :)


## ☕️ Fragments

## REVIEW

💬 QUOTES

- in today's ERA, prompt is the basic flue, better prompt, better efficiency, better result

✨ GOOD

- python jupyter notebook great way to learn python, resulting in 61 points in exam, No.1 in our small team
- 一张一弛，偶尔也需要看看 6 点的晚霞，和它一起下班，一起「欣欣然」感受生活的日子，那才是我想要的人生
- 问题引导法，是 e-studio 演绎的核心
- Career of Alibaba is void, focus on your own business, that's the way to go
- Welcome the new member of MacMini :)
- use `Excalidraw` to draw the system diagram, leverage the vscode x e-studio extension to sync the diagram both 🆒
- upcoming Paris trip, looking forward to it with @Vicky and self-driving to the south of France, Blue Coast, and the Mediterranean Sea 🗺️
- **VibeCoding** BP x VSCode Extension dev -> so quick, so vibrant, so cool ~~~~~~
- detailed plan for Paris and France trip (50% for sure) -> can not wait for self-driving to the south of France, Blue Coast, and the Mediterranean Sea 🗺️ and combo the Paris trip 🇫🇷
- craft the **new ways of ai-tasks x human**, in Arno's ai daily routines
- use Google Jules to operate `Agentic` all remote operations is supa cool, and SEO optimization is done in hours remotely during the working hours, and the results are great, this is the power of AI tools and agentic system

😈 BAD

- Crno & Loca will send back to home-town later when Vicky is pregnant is a bad decision for me, the feeling of can not control and lost is sad and feeling so weakness and helpless
- [agentic-match] DingTalk Agentic Match, pathetic and not useful, just a waste of time and energy

🤡 UGLY

- 钉钉的变动，让我对「**工作**」的定义产生了动摇，CodeGen 项目像个「小丑」似的，风雨飘渺。如果 Yida 是上个世纪的产品模式的话，那引力弹弓的主体确乎要另寻他路了
- 钉钉最近真的「病了」，也可能是组织想要换血，故意恶心人 ... 卡作息、加班不说，近期还拿代码量、Python 考试这种事情来恶心人，本来是想要好好做事的，结果却被这些事情搞得心态崩了，第一顺位不是如何写好代码，不少同学在思考的是如何写出 4000 行代码，真的是 sick + 病态，荒谬至极，aber 为之生气，造成情绪波动，显得幼稚就没有必要了
- Arno 新 workbench -> elaborations x portfolio x arno-prompts
- Gemini Pro 2.5 preview is quick and good for normal coding experience with Github Copilot, 🆒
- try Cursor and use `CursorVIP` in so cheap way xD free - version tech behind scene
