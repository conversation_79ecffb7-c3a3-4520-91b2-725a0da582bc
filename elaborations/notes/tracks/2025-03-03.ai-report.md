# AI Battle Key Report

## AI Calling Amount Increases

* C Client 用户粘性问题 ds impactions on `doubao` and etc.
* AI Entrance is `AI Assistant` -> correct? -> strategy?
* User really care to use AI in the best spots of DingTalk apps.
* Driven from `basic` user of DingTalk and drive users to use AI in the best spots of DingTalk apps.
  * LUI 2.0
  * Tabs of Client
  * IM Assistant
  * Copilot / Pinned Ball AI
  * ...
* Share chain impactions of AI application.
* 钉钉场域的核心差异化 -> 高价值转化
* ZQ: you must collaborate the **THE FIRST 5 CUSTOMERS** to define the AI Assistant of DingTalk.

---

@ZQ

* 快速在精进一个版本，核心第一指标：AI 助理，完成「渗透」，只要有更多人用，那就是神
* 这个心智搭出来，就是靠谱的 -> AI 帮你分担工作 -> 配合运营的力量

## AI Abilities

* LUI 的开放、技能的开放 + ... -> 「`LUI 的开放`」（Generative Streaming UI）
* 中活跃助理 `2K+`，高活跃助理 `100+`（DS 事件之前更少） -> Yida 有多少个占有中活或者高活？贡献了多少？
  * 企业的渗透率还是需要很大的提升，日 1W+ DAU 寥寥无几，目前牧原是一个。
* `AgentMesh` -> 「AgentMesh」possible? to schedule the agents-mesh to the best spot?

> 是否人人都可以低成本地搭出来？这样的技能 + 能力。

* 阿里钉的技能的数量很少，智能的技能编排还是比较重要的。
* 所有钉钉场域、业务和数据进行深度融合！-> 钉钉的压倒性之优势（这就是要构筑的壁垒）
* 要求案例是需要回流的，产研团队需要更多来自一线的案例。
* 高阶开放和集成能力：方案数量、组织 / 助理数量等等
* 智能助理 x YIDA 模式：AI 助理（问答智能 = 用企业的知识库和存量知识进行 AI 回答） -> AI 应用（业务智能 = 在数字化的应用系统上，AI 提供更智能的便捷交互） -> AIPaaS（组织智能 = 超级员工、超级 AI 助理、超级组织，AI 深度辅助决策）
* 办公场景的定制还不具备交付能力 -> 服务商其实很难调出客户想要的效果，比较难买单 -> 交付难，看似低，其实高
* 只要做 AI 时代的模板 + 解决方案的连接需要深度联合，成为一体

---

@ZQ

* 第二个战队解决：AI 商业 + AI 组织的设计
* ALab 手上的货去盘一盘，第三个战队最核心的哪三个场景
* 组织战队支持的是商业团队
* 二战队的支持位置 -> 非常重要，需要做一个整体的设计，组织连接
* 准备：脱一层皮 -> AI 商业和渗透都在探索中，所以就需要「快」，快速地响应市场

## AI Commercialization

* `6kw` -> `500W` -> `200+W` 真正的到 A.Lab. 的营收头上的
* `ds` -> 带来的 AI 商机是非常多的，比 Yida 多很多，大量的客户对交流的需求很多，没有可以落地的商业化项目
* 客户的「明确感」，很薄弱，大部分商机都是可以想要问我们：「你们有什么，你们能够做什么？」
* 云南白药 -> DAU `5`，其实没有起来
* 「问答」重交付的 AI -> 文档进准切片，基本上容易陷进去；「问数」每一条数据背后都是虚伪的 `SQL`，都是非常重交付的模式；
* 标品和模板模式是，要解决一些重「交付资产」的问题
* 问数类产品 -> code 太多了，而且商业化的依赖比较盘根错节，边界非常不清晰。算力、生产力平台、SaaS 本身，如何进行计算？
* 算力成本的问题（我们 5 ~ 100 倍），和 Coze 比较相悖论

---

@ZiTui

* 低价格的不确定性的 Agent 产品是有问题的
* 所有标品都是平台能力的「外延」
* 标品都围绕着成熟和规模化效应的产品去「切入」解决透，在透过这些标品去打磨开放和二开的空间
* AI 交付的很多内容，交付和核心 & 问题等等，都需要「重交付」解决
* 生产力平台的商机，最后一公里很难，收回前
* 优质客户的落地，是近期需要重点突破的，顶上向下的推动
* 「算粒」：现在是免费；「私有化」：未来是可以考虑
* ChatBI / ChatMemo 历史问题很多，完成了产品基础能力的打通，有很多问题绑定存在复杂度，交付的版本问题都需要解决
* 「生态 AI」：部分生态知道场景 know-how，知道如何和钉钉做 integration；生态的初步交付；

---

@ZQ

* `PDSA` -> 帮助 GTM 有商机和营收
* AI 的商业：持续聚焦在产品上
* 产研有好的「想法」，但是最终都围绕「快速验证」，但必须围绕「人性」和「商业本质」
* AI 硬件要加速，AI 时代的体感会更好一些
* AI 商业是核心 -> 那块最赚钱，最核心 -> 「AI 商业」
* 阳心：D.LAB + 钉钉的交付中心，否则跑不动

今年 3 个亿，加速！

## My Questions

* DingTalk AI Assistant production features as core differentiators from other AI apps like `doubao` etc?
  * DingTalk AI Assistant 的核心产品心智是什么？三个点可否构成？
  * DS 的核心认知：「复杂场景问题处理 -> R1」
  * `Kimi` 的核心认知：「Kimi 海量搜索结果 -> KimiResearch」
  * 元宝 AI、知乎 AI、小红书 AI 的核心认知：「微信私域」、「小红书私域」、「知乎私域」
* Core 5 Abilities to define the AI Assistant of DingTalk -> What's the core Production Features of DingTalk AI Assistant?
