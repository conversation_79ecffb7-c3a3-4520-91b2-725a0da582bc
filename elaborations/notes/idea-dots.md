# Connecting dots

## AI x X

* AI Yida Theme Generator

### Agents

* [idea of agents](../idea/agents/idea-of-agents.md)

## Plugins

* [WeeklyPlugin](../design/yida-opening.design/yida-plugins-vendors.md)

## EStudio

> [e-studio.design](../design/ai-e-studio.design/e-studio.design.md)

* Think with eStudio: how to make one problem digest deeply with eStudio to find various of aspects in mental-models & thinking frameworks and etc. The new elaboration ways should be like `thinking with eStudio` form of the way?

---

2025-05-26 idea that ignites for agentic agent 👇🏻

* single or multiple Yida instances data to use code-gen to generate static page in need
* chat-rooms for agents to explore an idea or a topic and gain insights -> multiple domain-experts to discuss and share knowledge
* page resources igniter: agent that fetch the resources from the page such as: fonts, images, videos, icons etc.
* `Aone` create task / requirements / bugs use MCP server