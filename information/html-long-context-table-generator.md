This is a `html` fragment extract from part of **Financial Site** which offered open API category.

I want you to extract its category tree and organize it as a `markdown` format like below:

```md
# FMP open API Category

## Company Search

[[generate a table with the empty value fields like below]]

| CategoryName   | Implemented | Description | CacheLevel | Notes |
| -------------- | ----------- | ----------- | ---------- | ----- |
| General Search |             |             |            |       |
| Name Search    |             |             |            |       |

---

[[File you want to analysis]]
```
