---
source: https://gemini.google.com/u/1/app/c_7d89144c62df9e50
---

# Yida Platform PRD User Story Template

## 1. User Story Structure

### Format
Use the standard format: 
> "As a [type of user], I want to [perform an action/achieve a goal] so that [I get this benefit/value]."

### Example
> "As a marketing specialist (B-end, `artifact-mode` or `simple-app-mode`), I want to generate an interactive product introduction page by providing product details and images, so that I can quickly launch a promotional campaign without needing deep coding skills."

## 2. Functionality & Acceptance Criteria

Connect to the system's "Abilities" and "Design Targets."

### Modes
| Mode | Description |
|------|-------------|
| `artifact-mode` | Generation of a simple, standalone page |
| `simple-app-mode` | Generation of a long-tailed simple application |
| `custom-page-mode` | Generation of a standalone custom page for an existing system |
| `custom-component-mode` | Generation of a standalone custom component |

### Implementation Details

#### Inputs
- What information will the user provide? 
- Examples: text prompts, data sources, UI preferences, target scenario like "vote app"

#### Process 
- Briefly describe the interaction
- Will it be chat-based (leveraging `app shell` and `code-gen-agent`)?

#### Output
- What is the expected generated artifact?
- Examples: HTML/CSS/JS code, a deployable Yida page, a component

### Core Competitive Advantages

- [ ] Use of existing Yida customers, systems, or data
- [ ] Sophisticated prompting-engineering for scenario-fit
- [ ] Utilization of Yida platform abilities (metadata-engine, process, auth, OSS)
- [ ] "Native editing in code and mirror to visual-page edit" capabilities

### Acceptance Criteria

#### Effectiveness
- [ ] The generated page/app must be stable and function as described
- [ ] The generated code must be accurate and meet user's functional requirements
- [ ] The output effectively addresses the chosen scenario

#### Efficiency
- [ ] Generation process completed within [TIME] (seconds/minutes)
- [ ] Low-cost solution (minimal reliance on expensive external services)
- [ ] Scalable generated artifact (if applicable)

#### Elegance
- [ ] Aesthetically pleasing and modern UI
- [ ] High-quality UI elements and precise interactions
- [ ] "Aha moment" for users through simplicity and quality of output
- [ ] Smooth and purposeful animations (if included)

### Development Phases

#### Phase 1 (MVP)
- Focus on one of the "6-top long-tailed apps scenarios for stable generation"
- Keep scope minimal but viable

#### Phase 2
- More complex interactions
- Production-level quality
- Collaboration with KOLs/customers

## 3. Standard PRD Elements

| Element | Description |
|---------|-------------|
| **Story ID** | A unique identifier |
| **Title** | A concise summary of the user story |
| **Priority** | High/Medium/Low - align with phase goals |
| **Dependencies** | Features, APIs (`Yida platform APIs`, `llm-services`), or systems this story depends on |
| **Assumptions** | Assumptions made while defining the story |
| **Out of Scope** | What is explicitly NOT being built |
| **Success Metrics** | How to measure success (e.g., task completion rate, generation speed) |
| **Notes/Open Questions** | Unresolved issues or points for discussion |

## 4. Example User Story

### Story ID: CG-US-001
### Title: Generate a Simple Voting Page via Chat Prompt

### User Story
> As a team lead (B-end, `simple-app-mode`), I want to generate a functional voting page by describing the voting topic and options in a chat interface, so that I can quickly gather team preferences for an upcoming event without manual setup.

### Alignment with Goals & Values
- ✓ Contributes to "world's best source-code x low-code driven page generation tool"
- ✓ Promotes "代码平权" by allowing a non-technical lead to create a functional app
- ✓ "Accelerates数字化系统建设" by providing a rapid way to create a common business tool

### Mode
`simple-app-mode` (target scenario: long-tailed mini-app - vote)

### Inputs
- User prompt via chat interface (e.g., "Create a vote for our next team lunch: Pizza, Burgers, Salad")
- *(Future: options for anonymity, deadlines)*

### Process
1. User types a prompt describing the vote
2. `code-gen-agent` interprets the prompt
3. `llm-services` and Yida platform abilities generate the page structure and basic logic
4. A link to the generated voting page (or embedded page) is provided to the user

### Output
- A standalone, functional voting page
- Users can view options and cast a vote
- *(MVP)* Basic display of results

### Acceptance Criteria

#### Effectiveness
- [ ] The generated page loads correctly and is stable
- [ ] Users can successfully cast one vote per identified option
- [ ] The system accurately tallies votes for each option
- [ ] The generated page reflects the topic and options provided in the prompt

#### Efficiency
- [ ] Page generation from prompt to usable link takes less than 2 minutes (Phase 1 target)
- [ ] The solution leverages Yida's existing infrastructure for hosting and data

#### Elegance
- [ ] UI is clean, intuitive, and visually appealing
- [ ] Interaction for voting is simple and clear
- [ ] Chat interaction for generation feels natural and efficient

### Dependencies
- `app shell` for chat UI
- `code-gen-agent` for prompt processing
- `llm-services` for natural language understanding and code suggestion
- Yida platform for page hosting/rendering

### Priority
**High** (Fits within Phase 1: "6-top long-tailed apps scenarios")

### Success Metrics
| Metric | Target |
|--------|--------|
| Generation time | < 2 minutes |
| Success rate | > 90% of prompts generate working pages |
| User satisfaction | > 4/5 rating for generated pages |
