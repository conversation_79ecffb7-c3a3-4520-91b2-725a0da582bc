# General Design Template

## Design Goal

* KEY Goals / Vision [User]
* Secondary Goals / Goals sub-sections [AI]
* Meaning & Value [User]

## Elaborations

> According to the given goal, we can use mental-models or theories to elaborate the goal.

* idea first sketch / elaborate diagram [User] -> build basic design concepts & ideas
* [AI] Mental-models or Theories to test and shape the elaborations for deep and comprehensive design.

<PERSON><PERSON> often used [mental-models](https://www.e-studio.ai/elaboration/c18dc030-0baf-43b5-b8e5-78f5e6feb47d):

## Design Principles

> Generate design principles based on the goal and elaborations & patterns.

### Design Patterns

> Patterns are the essence of design.

* Patterns used in the design

### Design Constraints

* Technical Constraints
* Business Constraints
* Resource Constraints

## Market Research

* Market Size
* Market Growth
* Market Segment
* Market Trend
* Market Competition: main competitors, their strengths and weaknesses

## User Research

* Target Users
* User Needs & Pain Points
* User Journey
* Personas

### Scenarios Driven Design

> Carefully crafted scenarios to drive the design.

* [AI] + [User] -> [Scenarios]

## Design Structure

> Think in a systematic way.

 [AI] + [User] -> [Systematic Design]

* Structure
* Genes
* Functions
* Relations

## Design Elements

* Elements

## Design Functionalities

* Functionalities

## Design Implementation

* Implementation
* Technical Requirements
* Dependencies

## Testing & Validation

* Validation Methods
* Success Metrics
* Feedback Collection Plan

## Risks & Mitigations

* Potential Risks
* Risk Management Strategies

## Iterations & Future Work

* Planned Iterations
* Scalability Considerations
* Future Enhancements

## Core Elaborations

> Key trace of the design.

* Key Trace

## References

* References
* Inspirations
* Similar Solutions
