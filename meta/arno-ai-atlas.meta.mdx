---
type: doc
uuid: e343754d-23b2-4b4b-b233-82fcc21f99b9
name: 🗺️ Arno's AI Atlas
---

Public cited resources:

> this article is synced with [Portfolio](https://arno.surfacew.com/posts/en/arno-ai-map)

Private executing principles:

* choose the **best** tools without considering the cost
* AI era should try as much as possible to use AI to do the work

My VibeCoding tools:

* `Cursor`: home use, first choice, may buy cheap account to run `side-car` mode
* `Augment`: run free version, in Work of Alibaba(for now, because Cursor is banned), complex tasks should use `Augment` but normal tab engineering should be ok
* `Github Copilot`: use free version, and `Augment` to do the completion work, but mainly use VSCode for coding

---

* A<PERSON>'s Project -> Fully boosted via Cursor
* Works' Project -> simple tasks performed via Github Copilot, complex tasks performed via `Augment` free version (switching account to work) also consider to buy credits for `Augment` if it is truly helpful in the future to handle the complex tasks

# Elaborated

* version 0 is crafted on 2025.04.20 mainly focus on list all tools / apps and basic scenarios
* refactor on `scenario driven` and better look view on 25.05
* add concepts of AI working procedure and AI working system
* add my VibeCoding tools and principles in working system 05.29