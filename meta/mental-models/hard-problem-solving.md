# ✅ 复杂问题解决模型(Complex Problem Solving).mental

# 模型定义

> 要尝试去解决很难的问题，生活中的问题，新华书店里的问题，书里面的问题或许都不是当前最困难的，因为正在解决这些困难的人，没有时间去写书！—— 王坚

问题的本质是「**矛盾**」：现况和预期的差异。

## 界定问题

如果问题有存量的系统分析模型 <edoc-reference-text refdoccontent="💠 系统分析模型 Systematical Analysis.mental" refdoccuid="f1670110-8625-4243-8a76-7b4695b2e104"></edoc-reference-text>，请关联到具体的系统或者实体结构上作为分析基础。

- 认清楚问题的矛盾
- 设置好问题的边界
- 定义好问题的目标

> 必要的时候，需要拆解问题。

在问题归因上，我们可以尝试从：

- 横向角度：思考问题的广度，和同类问题的联系
- 纵向角度：思考问题的深度，从垂直的角度去思考问题，并且需要将一个点挖得足够深，足以认识到系统的本质
- 系统角度：看待问题需要让视角调得更高，看到整体，看到宏观，看到整个体系机制；内部、外部、子系统、父系统、关联系统 ...
  - 理解所要解决的问题关联的系统本质
  - 政治系统、经济系统、文化系统、军事系统、国别系统 …

## 寻求方案

动用集体智慧，不要做重复的劳动，不要妄图一个人做全部的事情，用好身边已有的系统和资源去实现集体目标。

找到系统和问题背后的：

- 「抓手」：关键要素，符合 2/8 原则的关键要素
- 「杠杆」：通用的杠杆往往具备规模效应，边际成本很低

### 智慧层

- 对分支问题确认优先级，分配权值，分配资源，确认分支问题之中的 **关键问题**，利用 **2/8** 原则；
- 清楚不同的问题类型，有不同的「特点」需要「对症下药」，根据不同类型问题的解决策略做梳理；
- 使用 [思考模型(Thinking & Mental Methods / Models / Framework).index](https://www.notion.so/Thinking-Mental-Methods-Models-Framework-index-e0f64c2aea1d4812bb8ef5ef79020e38?pvs=21) ，考虑问题牵连的「全域思维」；
  - 比如： [系统分析模型(SystemAnalysisModel).model](https://www.notion.so/SystemAnalysisModel-model-8bc166ef5d284293a520683d9ed018b5?pvs=21)
  - 比如： [TRIZ：](https://www.notion.so/TRIZ-book-e6899eef00af485f90aaca0f58490159?pvs=21)[创新算法.book](http://创新算法.book)
    - ARIZ 71 Algorithm（SOP）
- 动用人际关系，寻求智慧层面的建议；

### 经验层

利用现存的解决方案、模型、知识、经验等等，看看它是否能够优雅地解决问题。Search in your brain or Get through your **INFO NETWORK**。（**Self-Elaboration Stage** 自演绎阶段）

我们往往能够从已有的模型以及解决方案中得到启发，直接沿用或者扩展创新，甚至推倒革新！但是，每一种革新都是有迹可循的，都是基于已有的事物和认知来创造出新的事物和认知的，我们总是站在前人的肩膀上，继续探索！（**Borrow and Learn Stage** 借鉴和已有经验的探索阶段）

- 同类解决方案 `.solution`
- 同类项目方案 `.project`
- 使用逻辑树分解问题，参考 `金字塔模型`
- 动用人际关系，寻求智慧层面的建议

### 知识层

搜寻问题相关联的： <edoc-reference-text refdoccontent="🧬 Frame.meta" refdoccuid="ec34b031-90cb-4a7b-9995-a0264ee1dbda"></edoc-reference-text>

- 结构 `.squo`
- 知识 `.klg`
- 模型 `.model`
- 模式 `.pattern`
- 研究 `.research`
- 说明 `.desc`
- 观点 `.concept`
- ...

### 信息层

- 连接存量的 **SYSTEM INSTANCE** 做具体分析；
- 动用核心的 [信息渠道(info-channel).frame](https://www.notion.so/info-channel-frame-0932d993c7e841a2aca0cf72a76a3a4e?pvs=21) 富集相关资料；

### 数据层

- 使用「数据」/「大数据」的思维去解决问题，富集数据，发掘价值信息；

## 选择方案

- 关键设计决策上，拒绝「直观」的解决方案，深入才会发现新的世界；
- 能够使用「电梯法则」讲清楚的方案，更容易传达和落地；
- 考虑「帕累托」要素（2/8原则）

相关联的模型包括： <edoc-reference-text refdoccontent="💎 Arno 的决策模型(Arno Decision).mental" refdoccuid="b81a233b-4e33-4036-9e47-e50a894e5641"></edoc-reference-text>

## 付诸行动

我们将一系列的评估因素赋予重要程度的权值，然后用这些评估因素对解决问题的方案进行评估，最终选择出理想的解决问题的方案。

## 反馈闭环

利用好 FeedbackLoop 的结构，持续跟踪问题解决的迭代 & 演绎。

- 使用思维模型：`第一、第二顺位思考`； <edoc-reference-text refdoccontent="🎭 角色与换位思考(Role Thinking).mental" refdoccuid="fe1747da-b990-410e-821f-2dca4e66b731"></edoc-reference-text>
- 建立持续跟踪和反馈体系（指标）；