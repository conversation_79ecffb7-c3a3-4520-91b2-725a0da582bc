# 💎 Arno 的决策模型(Arno Decision).mental

# 模型定义

> 灵魂三问：有什么？要什么？要舍弃什么？

Arno 日常进行决策的时候参考的方法论。

# 决策方法论

- 坚守「**价值主义**」，意义导向，价值导向，目标导向
- 考虑「**机会成本**」和「**沉没成本**」
- 考虑决策结果的「**能级**」和「**势能**」

## Arno 决策原则

- 越高 Level 的决策越应该在 <edoc-reference-text refdoccontent="💠 系统分析模型 Systematical Analysis.mental" refdoccuid="f1670110-8625-4243-8a76-7b4695b2e104"></edoc-reference-text> 的基础上进行
  - 资源利用率（局部最优 v.s. 整体最优）
  - 系统开放性（反脆弱 v.s. 杜绝封闭）→ 拥抱不确定性 → 生态特征（自组织、自生长）
  - 系统中的杠杆
  - 系统中的反馈循环 → 复利模型
  - 系统中的周期
  - …
- 回归本质规律，从「**第一性质原理**」出发去找根源驱动因子
- 考虑「**帕累托**」要素（2/8原则）
- 考虑「**时间线**」<edoc-reference-text refdoccontent="⌛️ 时间线分析(Timeline Analysis).mental" refdoccuid="8b8203d0-**************-2bfbe8835e5a"></edoc-reference-text>
- 多角度、多维度思考
  - <edoc-reference-text refdoccontent="🎩 6 Hats.mental" refdoccuid="a922b374-b6fc-4af8-95c7-073c5967492e"></edoc-reference-text>
  - 极性分析：「最好 v.s. 最坏」、「G.B.U」、「黑马 & 黑天鹅 & 非对称回报」
  - WIP: 跨学科思考模型 → 芒格的多元思考模型
  - WIP：生产模型等经典的经济学模型（边际效应、周转率）
  - WIP：加入「概率」的数学要素辅助决策
- 充分利用好「**集体智慧**」，来纠正自己的可能「**认知偏见**」
  - 领域专家
  - 高人、贵人
  - 集体头脑
- 「**数据驱动**」让决策更加理性

## 决策工具

- 权重矩阵（WeightMatrix）
- KT 决策方法论

## 决策过程

- 「**苏格拉底式**」 → 疯狂自问自答
- 「**系统分析式**」 → 在复杂系统分析的模型上推演
- 「**演进演化式**」 → 持续关注决策的过程、反馈和变化 → 决策树追踪法