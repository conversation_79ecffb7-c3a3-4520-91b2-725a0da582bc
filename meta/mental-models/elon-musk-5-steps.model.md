# 🙉 Elon Musk 的做事五步思考法则(5 Steps Process).pattern

> 埃隆·马斯克的五步工作法是一种让你在不确定的环境中做出好的决定的方法，它可以帮助你更清楚地认识你的需求，更简单地设计你的流程，更快速地验证你的决定，更自动化地执行你的任务。这种方法也是基于第一性原理的思考，也就是从最根本的事实出发，避免受到传统的约束和影响，创造出新的解决方案。

# 思考模型定义

- **质疑每项要求**：**所有的需求都是假设，既然是需求，就是仍未实现的事情，你如何证明一个还不存在事物的正确性？** 马斯克特别强调了，在收到需求的时候一定要质疑，特别是那些来自专家的需求，因为你会不假思索的被对方的专业性所麻痹。你要自己判断，你的需求是不是基于最基本的事实，是不是符合最普遍的规律，是不是反映最重要的目标。例如，当马斯克想要制造一种能够将人类运送到火星的火箭时，他没有听从那些说这是不可能的，太贵的，或者没有必要的专家的声音，而是从物理学的基本原理出发，计算出火箭的最低成本，最高效率，和最大可能性。

  - [第一性原理(FirstPrinciple).principle](https://www.notion.so/FirstPrinciple-principle-012dbf7ccec548c897e5243541273b1d?pvs=21)

- **最少的流程**：**对于一个组织来说，如果没有觉得流程不够用，就说明流程已经太多了**。 你要避免遵循一些复杂，低效，或者过时的流程，因为它们可能浪费你的时间，精力，或者资源。你要自己创造，你的流程是不是基于最必要的步骤，是不是符合最优化的原则，是不是反映最新的技术。组织中存在流程是一件好事，但是如果所有的事情都有流程，那么组织中的个体就失去了创新和探索的能力，因此流程用于那些已经验证过的问题，无法应对未知的场景。当**一个组织用既定的方式去应对不相匹配的全新问题的时候只有2种结果，**

  **1）流程会失效无法推进；**

  **2）按照既定流程做出错误的决策并造成不可预料的结果。**

  无论哪种情况，最终受到伤害的都是组织本身。出现类似问题的时候，需要组织中个体站出来创造性的解决问题，只有这样组织才能充分适应未知的情况。当然这还必须依赖组织中鼓励创新的激励措施，

  有关于这一点马斯克也在很多场合提到过自己公司中是如何激励创新的。例如，当马斯克想要提高特斯拉汽车的生产效率时，他没有沿用那些传统的，繁琐的，或者固定的流程，而是鼓励他的团队用创造性的方式解决问题，减少不必要的环节，**改变不合理的规则，利用最先进的工具。**

- **简化和优化**：\*\*你要避免保留一些多余，低效，或者错误的地方，因为它们可能影响你的表现，质量，或者结果。\*\*你要自己改进，你的流程是不是基于最核心的因素，是不是符合最高的标准，是不是反映最好的实践。

  [帕累托原则(ParetoPrinciple - 28).principle](https://www.notion.so/ParetoPrinciple-28-principle-654daabed5fe4d19ba9aa45fb4e08ff1?pvs=21)

  例如，当马斯克想要提升神经连接的技术水平时，他没有满足于那些复杂，低效，或者不准确的方法，而是不断地简化和优化他的产品设计，消除冗余和低效的部分，提高信号的质量和速度，采用最安全和最便捷的方式。 所有的管理理论都在提简化和优化，但是 简化和优化的前提是目标的正确性（第一步）和鼓励创新的组织文化。 否则要么方向错误，要么找不到简化和优化的余地。**“这个事情没有办法，流程就是这样”，这样的说法大家一定都听到过，其实这就是组织缺少创新激励文化的体现**

- **加速迭代**：**你要避免拖延，犹豫，或者害怕做错，因为它们可能阻碍你的学习，改进，或者适应。你要自己尝试，你的决定是不是基于最真实的数据，是不是符合最快的速度，是不是反映最新的需求。你要不断地学习和改进，不断地适应和优化。你要保持一个好奇，积极，进取的心态，让你的决定变得更好，更快，更强。例如，当马斯克想要改变世界的交通方式时，他没有停止于那些理论，设想，或者计划，而是不断地测试和反馈他的超级高铁的概念，看看它们在实际中的可行性，效率，和优势。注意我们需要 加速的是迭代，而不是速度。** 迭代是一个循环，通过这个循环我们可以不停的验证当前推进的方向，并且持续的进行改进和优化。五步工作法不是一个单向一次性的行为，而应该在最小可执行粒度上持续的循环推进。 也意味着，你需要持续的质疑需求，持续的优化流程，持续的探索加速这个循环。这个过程的最终状态是组织进入一个自我推动，自我加速的状态，形成持续的创新和响应异常情况的能力，其实就是我们常说的敏捷性（agility）。

  - 敏捷和速度致胜 → 尤其对于创业模式的公司来说是非常重要的

- **自动化**：**你要避免做一些重复，繁琐，或者复杂的任务，因为它们可能消耗你的时间，精力，或者能力**。你要自己利用，你的任务是不是基于最适合的技术，是不是符合最省的成本，是不是反映最高的质量。你要在合适的场景下，利用自动化技术提高效率和质量，但同时注意自动化的成本，风险和局限性。例如，当马斯克想要提高特斯拉工厂的生产能力时，他没有盲目地追求全面的自动化，而是根据不同的产品和环节，选择合适的程度和方式的自动化，同时保留一定的人工干预和调整的空间。相信大家所看到的大多数介绍特斯拉工厂的视频中展示的也都是高度自动化流水线，机器人以及空无一人的厂房。实际上，按照马斯克自己说法，他一直都希望特斯拉工厂中能够有更多的工人而不是全部都由机器人来工作。自动化的弊端有3个，分别是

  **1）高昂的成本**

  **2）极高的出错几率**

  **3）妨碍引入新特性**

  实际上，特斯拉工厂中的工人数量大大多于其他汽车生产厂家，这其实也是马斯克有意为之。 一来是因为特斯拉汽车几乎所有的零部件都是自己生产/非外包的，这让特斯拉的工厂承载了几乎普通汽车厂家的整个产业链上工人的数量。其次，机器人/流水线都只能在非常严苛的固定条件下才能顺利工作，一旦出现异常，整条流水线都必须停下来等待问题的解决。这让整个流水线变成一个严重前后依赖的单线程系统，**而马斯克真正希望构建的是一个可以随时更换组件、流程、探索新特性的并行系统。**

# 适用场景

为特定复杂系统系统，设计方案以为了实现特定的目标。

## 关联模型

- 系统思考模型 <edoc-reference-text refdoccontent="💠 系统分析模型 Systematical Analysis.mental" refdoccuid="f1670110-8625-4243-8a76-7b4695b2e104"></edoc-reference-text>

# Cases

WIP