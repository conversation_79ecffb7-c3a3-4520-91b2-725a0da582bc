# 🛥️ DFSQBZS - 道法术器兵志势.mental

# 模型定义

利用「道」、「法」、「术」、「器」、「兵」、「志」、「势」的多角度思考一个系统或者实现目标。

## 道 Dao

> 道生一，一生二，二生三，三生万物。

宇宙级的，**最根本** 的，**恒定** 的，**原子性** ⚛ 的规律、法则。是「真理」之所在。

也可以代指，人的「**智慧 Wisdom**」。

- 比如：物理基本原理、思想

## 法 Principles

基于「道」，在复杂个体或者群体中所衍生出来的 **法则、规律**。

- 比如：经济学规律、社会学规律

## 术 Strategies / Tactics

基于道法所产生的：**战略、策略、战术、方法论** 等具体的抽象手段。

- 比如：商业的战略、战术

## 器 Tools

泛指 **工具**，广义上可以衍生为 **实现「术」的实体或者系统**。

- 比如：从软件工程的视角来看，代码、包、工具、服务、平台都可以作为 Tools，是具象的存在

## 兵 Resources

> 兵贵精，不贵多。

操作或者实现「**器**」所需要的 **资源**，涵盖以人、财、物、场等维度的富集，时间也是一个恒等推演的资源。

## 志 Goals

> 因为相信，所以看见。

**愿景，使命和目标，以及背后的人为赋予的意义、价值。**

## 势 Force

上述综合效应产生的 **势能**。也是当下的局势，好比物理学中的势能之概念。

- 比如：行业的发展势能，政策的势能等

# 适用场景

宏观分析较为庞大的系统，比如：软件系统、公司、个人身体 & 财富系统等，宏观系统地多维度分析视角。

> 造风和借势

「天时」、「地利」与「人和」思想也体现在这里：

- 天时：时机、道

- 地利：

  - 有效的法、术、器
  - 有效的切口 or 切面
  - 有效的规模（受自己把控）

- 人和：

  - 组织保证、恰当的人、NB 的角色的推动

# Cases

- Yida 低代码领域的分析 → [link](https://www.notion.so/arno93/DFSQBZS-hpaPaaS-dive-2f479f3ca6a2432499d1016d5853aade)
- 钉钉的商业系统分析 → DingTalk OS