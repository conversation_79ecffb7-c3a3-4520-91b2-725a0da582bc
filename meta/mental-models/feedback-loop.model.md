# 🌀 循环图 & 反馈环模型(FeedbackLoop).model

## 模型定义

理解系统之中组成元素的关系以及中间的反馈环。

> 本质：原因 → 结果，所有的连接的因果关系非 + 必 -

- 平衡反馈：+ / - 抵消（平衡维护）
- 正向循环反馈（良性循环）
- 负向循环反馈（恶性循环）

## 模型使用

1. 可视化关系网络；
2. 增加正向 / 负向反馈；
3. 识别有效的反馈网络（+ / - 反馈网络）；
   1. 识别增强回路（正向循环、恶性循环）
      1. 环路 - 为偶数（0 也算），是典型的增强回路，运转一次就增强自己
   2. 识别调节回路
      1. 环路 - 之和为奇数，是典型的调节回路，整个回路在寻求一种动态平衡的目标
4. 注意连接「时滞效应」，要素影响往往具备时效性
5. 带入权重、分析函数提升分析的有效性，进行数学建模

---

一些比较推荐的用法：

- 寻找「增长」和「衰退」引擎
  - 增长曲线：线性增长、指数增长
  - 增长制约要素（天花板）
  - …
- 找到屠婆瓶颈和限制的要素（第二驱动点）
- 理解系统地动态平衡机理（城市和人口的平衡）
- 公共事务分析（公共性政策、策略分析）
- 根据系统循环图，巧妙地设定目标，并分配权重和要素

## 图形绘制技巧

- 了解并设定好问题的边界，本质上是定义清楚「系统边界」，杜绝发散
- 从有趣的，最显而易见的地方开始，先看表象，再渗透到本质 [第一性原理(FirstPrinciple).principle](https://www.notion.so/FirstPrinciple-principle-012dbf7ccec548c897e5243541273b1d?pvs=21)
  - 询问系统地关键目标是什么？
  - 系统最关键的外部驱动力是什么？
  - 相关因素中最关键的是什么？
- 询问：它将驱动什么，以及它的驱动力是什么？（首尾连接）
- 不要陷入混乱，合并影响力低的要素，保持聚焦到关键要素之上 [帕累托原则(ParetoPrinciple - 28).principle](https://www.notion.so/ParetoPrinciple-28-principle-654daabed5fe4d19ba9aa45fb4e08ff1?pvs=21) ，所有的影响因子应该由「权重」的概念来排序优先级和重要性
- 使用可以度量的名词而非动词作为循环图的要素，也不要使用使 xx 上升或者下降等辞藻，聚焦可量化的名词作为因素
- 随着时间的进展建立连接类型（甚至是函数动态类型） + / - 并非完全是静态的
- 坚持就是胜利，随着认知和时间的推移持续优化系统循环图
- 服务于现实，扎根于现实的循环图才是合理的，否则它永远都是没有价值的模型