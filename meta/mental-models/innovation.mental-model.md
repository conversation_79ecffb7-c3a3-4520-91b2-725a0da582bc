# 🔅 创新模型(Innovation).model WIP

WIP <https://www.notion.so/arno93/Thinking-Mental-Methods-Models-Framework-index-e0f64c2aea1d4812bb8ef5ef79020e38?p=2e623c1158724edd85d36031f588287e&pm=s>

需要我们再来 :S

## TRIZ - ARIZ 71 

by G.S.Altshuller 1985

***0.1.*** *Determine the final goal of a solution:*

1. What is the *technical* goal (what characteristic of the object must be

   changed)?

2. What *characteristic* of the object obviously cannot be changed in the process of solving a problem?

3. Which expense will be reduced if the problem is solved?

4. What is the roughly acceptable expense?

5. What is the main technical / economic characteristic that must be improved?

***0.2.*** *Investigate a "bypass approach". Imagine that the problem, in principle, cannot be solved. What other, more general problem, can be solved to reach the required final result?*

1. Proceed to the super-system (for the given system, from which the problem originated) and reformulate the original problem at the level of the super- system.

2. Proceed to the sub-systems (the given system contains a set of sub- systems) and reformulate the original problem at the level of the sub- systems (e.g. substances).

3. Reformulate the original problem for three levels (super-system, system, sub-system) by replacing the required action (or feature) with an opposite action (or feature).

***0.3.*** *Determine which problem, the original or the bypass, makes the most sense to solve. Choose which to pursue: take into account the objective factors (what are the system reserves of evolution); take into account the subjective factors (which problem it is supposed to solve – mini-problem or maxi-problem).*

***0.4.*** *Determine the required quantitative characteristics.\
**0.5.** Increase the required quantitative characteristics by considering the time for*

*implementing the invention.*

***0.6.*** *Define the requirements of the specific conditions in which the invention is going to function.*

1. Consider the specific conditions for manufacturing the product: in particular, the acceptable degree of complexity.

2. Consider the scale of future applications.

***0.7.*** *Examine if it is possible to solve the problem by direct application of the Inventive Standards. If the problem has been solved, go to 5.1. If the problem is still unsolved, go to 1.8.*

***0.8.*** *Define the problem more precisely using patent information.*

1. How are problems close to the given one solved in *other patents*?

2. How are similar problems solved in *leading industries*?

3. How are opposite problems solved?

***0.9.*** *Use STC operator (Size, Time, Cost).*

1. Imagine changing the *dimensions* of an object from its given value to *infinity* (S →

   ∞). Can this problem now be solved? If so, how?

2. Imagine changing the *dimensions* of an object from its given value to *zero* (S →

   0). Can this problem now be solved? If so, how?

3. Imagine changing the *time* of the process (or the *speed* of an object) from its

   given value to *infinity* (T → ∞). Can this problem now be solved? If so, how?

4. Imagine changing the *time* of the process (or the *speed* of an object) from its

   given value to *zero* (T → 0). Can this problem now be solved? If so, how?

5. Imagine changing the *cost* (allowed spending) of an object or process from its

   given value to *infinity* (C → ∞). Can this problem now be solved? If so, how?

6. Imagine changing the *cost* (allowed spending) of an object or process from its

   given value to *zero* (C → 0). Can this problem now be solved? If so, how?