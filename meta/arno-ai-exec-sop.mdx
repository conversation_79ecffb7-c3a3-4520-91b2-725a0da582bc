# ✨ AI 时代下做事的 SOP

## 一般事务

> 定位关键 系统作为全局上下文，使用问题引导法思考

- 设置目标，厘清价值和意义
- 确定大题执行基调：3E 维度拆解
- 思考演绎路径：思考模型、思维模型、原则、方法论、关联的 `KESW`，模板等等加速一般过程，联合 public 和 private domains 进行
- 问题解决的一般路径思考🤔🤔 CoT

识别 `ORDERIFY` 或 `ETIWTT` 维度

- 存量 vs 增量 vs 全新
- 设计 vs 创新 vs 发明
- 欣赏 vs 鉴赏 vs 创作

工欲善其事必先利其器

- LLM 最优秀的工具，或者快速创造 LLM 执行体的工具，AI 工具驱动自己创意和实现价值，**Context Engineering** 在这个过程中颇为重要！追求简单的思想在这里就会体现出人的价值。
- 执行：分步骤 FullContextEngineering in One Context

## 领域事务

* 比如源代码研究
* 比如欣赏音乐会
* 比如创意产品研发 product engineering
